# 货代单证智能解析插件 SDK 集成指南

## 📋 概述

货代单证智能解析插件 SDK 是一个零配置的 JavaScript 插件，可以轻松集成到任何网站中，提供智能的单证解析和表单回填功能。

## 🚀 快速开始

### 1. 引入 SDK

```html
<!-- 在页面中引入 SDK -->
<script src="https://your-domain.com/static/js/freight-parser-sdk.js"></script>
```

### 2. 基础配置

```javascript
// 配置 SDK（可选）
window.freightParserConfig = {
    apiBaseUrl: '/api',           // API 基础路径
    clientId: 'your_client_id',   // 客户端 ID
    userId: 'user_id',            // 用户 ID
    theme: 'light',               // 主题：light/dark
    position: 'bottom-right',     // 位置：bottom-right/bottom-left/top-right/top-left
    autoDetect: true,             // 自动检测文档类型
    enableOcr: true,              // 启用 OCR
    ocrLanguage: 'chi_sim+eng'    // OCR 语言
};
```

### 3. 自动初始化

SDK 会在页面加载完成后自动初始化。如果需要手动控制，可以设置：

```javascript
// 禁用自动初始化
window.freightParserAutoInit = false;

// 手动初始化
document.addEventListener('DOMContentLoaded', function() {
    const parser = new FreightParser({
        apiBaseUrl: '/api',
        clientId: 'your_client_id'
    });
    parser.init();
});
```

## 🎯 高级用法

### 1. 事件监听

```javascript
// 监听插件事件
document.addEventListener('freightParser:initialized', function(e) {
    console.log('插件初始化完成', e.detail);
});

document.addEventListener('freightParser:parseComplete', function(e) {
    console.log('解析完成', e.detail);
    // 可以在这里处理解析结果
});

document.addEventListener('freightParser:fillComplete', function(e) {
    console.log('回填完成', e.detail);
    // 可以在这里处理回填结果
});
```

### 2. 程序化调用

```javascript
// 获取插件实例
const parser = window.freightParserInstance;

// 程序化解析文档
const file = document.getElementById('fileInput').files[0];
parser.selectedFile = file;
parser.parseDocument();

// 程序化回填表单
if (parser.currentParseResult) {
    parser.fillForm();
}
```

### 3. 自定义字段映射

```javascript
// 扩展字段映射
const parser = new FreightParser({
    apiBaseUrl: '/api',
    customFieldMapping: {
        'CUSTOM_FIELD': ['自定义字段', 'custom', 'special']
    }
});
```

## 🎨 样式自定义

### 1. CSS 变量

```css
:root {
    --fp-primary-color: #007bff;
    --fp-success-color: #28a745;
    --fp-border-radius: 8px;
    --fp-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
```

### 2. 自定义样式

```css
/* 自定义触发按钮样式 */
.fp-trigger-btn {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
}

/* 自定义面板样式 */
.fp-panel {
    border-radius: 16px !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}
```

## 📊 支持的文档类型

| 文档类型 | 英文名称 | 支持字段 |
|---------|---------|---------|
| 海运提单 | BILL_OF_LADING | 提单号、发货人、收货人、起运港、目的港等 |
| 商业发票 | COMMERCIAL_INVOICE | 发票号、金额、币种、贸易条款等 |
| 装箱单 | PACKING_LIST | 装箱单号、件数、重量、体积等 |
| 报关单 | CUSTOMS_DECLARATION | 报关单号、商品信息、税费等 |
| 货代费用单 | FREIGHT_INVOICE | 费用明细、总金额等 |
| 原产地证 | CERTIFICATE_OF_ORIGIN | 原产地、商品描述等 |

## 🔧 API 参考

### FreightParser 类

#### 构造函数
```javascript
new FreightParser(config)
```

#### 方法

| 方法 | 描述 | 参数 |
|-----|------|------|
| `init()` | 初始化插件 | 无 |
| `parseDocument()` | 解析文档 | 无 |
| `fillForm()` | 回填表单 | 无 |
| `destroy()` | 销毁插件 | 无 |

#### 事件

| 事件名 | 描述 | 数据 |
|-------|------|------|
| `freightParser:initialized` | 插件初始化完成 | `{version}` |
| `freightParser:parseComplete` | 文档解析完成 | `{parsedData, ...}` |
| `freightParser:fillComplete` | 表单回填完成 | `{filledFields, totalFields}` |
| `freightParser:pageScanComplete` | 页面扫描完成 | `{formCount}` |

## 🛠️ 集成示例

### React 集成

```jsx
import { useEffect } from 'react';

function MyComponent() {
    useEffect(() => {
        // 动态加载 SDK
        const script = document.createElement('script');
        script.src = '/static/js/freight-parser-sdk.js';
        script.onload = () => {
            const parser = new window.FreightParser({
                apiBaseUrl: '/api',
                clientId: 'react_client'
            });
            parser.init();
        };
        document.head.appendChild(script);

        return () => {
            // 清理
            if (window.freightParserInstance) {
                window.freightParserInstance.destroy();
            }
        };
    }, []);

    return <div>我的 React 组件</div>;
}
```

### Vue 集成

```vue
<template>
    <div>我的 Vue 组件</div>
</template>

<script>
export default {
    mounted() {
        this.loadFreightParser();
    },
    beforeDestroy() {
        if (window.freightParserInstance) {
            window.freightParserInstance.destroy();
        }
    },
    methods: {
        loadFreightParser() {
            const script = document.createElement('script');
            script.src = '/static/js/freight-parser-sdk.js';
            script.onload = () => {
                const parser = new window.FreightParser({
                    apiBaseUrl: '/api',
                    clientId: 'vue_client'
                });
                parser.init();
            };
            document.head.appendChild(script);
        }
    }
};
</script>
```

### Angular 集成

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';

@Component({
    selector: 'app-my-component',
    template: '<div>我的 Angular 组件</div>'
})
export class MyComponent implements OnInit, OnDestroy {
    
    ngOnInit() {
        this.loadFreightParser();
    }
    
    ngOnDestroy() {
        if ((window as any).freightParserInstance) {
            (window as any).freightParserInstance.destroy();
        }
    }
    
    private loadFreightParser() {
        const script = document.createElement('script');
        script.src = '/static/js/freight-parser-sdk.js';
        script.onload = () => {
            const parser = new (window as any).FreightParser({
                apiBaseUrl: '/api',
                clientId: 'angular_client'
            });
            parser.init();
        };
        document.head.appendChild(script);
    }
}
```

## 🔒 安全考虑

1. **API 密钥管理**：确保 API 密钥安全存储，不要在前端代码中硬编码
2. **域名限制**：在后端配置中限制允许的域名
3. **文件大小限制**：设置合理的文件大小限制
4. **文件类型验证**：严格验证上传的文件类型

## 🐛 故障排除

### 常见问题

1. **插件无法初始化**
   - 检查 SDK 文件是否正确加载
   - 检查控制台是否有错误信息
   - 确认 API 基础路径配置正确

2. **文档解析失败**
   - 检查文件格式是否支持
   - 检查文件大小是否超限
   - 检查网络连接和 API 服务状态

3. **表单回填不准确**
   - 检查表单字段的 name 属性是否正确
   - 检查字段映射配置
   - 查看控制台日志了解匹配过程

### 调试模式

```javascript
// 启用调试模式
window.freightParserConfig = {
    debug: true,  // 启用详细日志
    // ... 其他配置
};
```

## 📞 技术支持

如有问题，请联系技术支持：
- 邮箱：<EMAIL>
- 文档：https://docs.example.com
- GitHub：https://github.com/example/freight-parser-sdk

## 📄 许可证

MIT License - 详见 LICENSE 文件
