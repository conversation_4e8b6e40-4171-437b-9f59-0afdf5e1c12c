/**
 * 货代单证智能解析浏览器扩展 - Background Script
 * 负责扩展的后台逻辑、消息传递和状态管理
 */

class FreightParserBackground {
    constructor() {
        this.init();
    }

    init() {
        // 监听扩展安装/更新
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 监听来自content script和popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdated(tabId, changeInfo, tab);
        });

        // 监听标签页激活
        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.handleTabActivated(activeInfo);
        });

        console.log('FreightParser Background Script initialized');
    }

    /**
     * 处理扩展安装/更新
     */
    async handleInstalled(details) {
        console.log('Extension installed/updated:', details);

        if (details.reason === 'install') {
            // 首次安装
            await this.setDefaultSettings();
            this.showWelcomeNotification();
        } else if (details.reason === 'update') {
            // 更新
            await this.migrateSettings(details.previousVersion);
            this.showUpdateNotification();
        }
    }

    /**
     * 设置默认配置
     */
    async setDefaultSettings() {
        const defaultSettings = {
            apiBaseUrl: 'http://localhost:8080/api',
            autoDetectDocumentType: true,
            enableOcr: true,
            ocrLanguage: 'chi_sim+eng',
            enableNotifications: true,
            enableAutoFill: true,
            theme: 'light'
        };

        await chrome.storage.sync.set(defaultSettings);
        console.log('Default settings saved:', defaultSettings);
    }

    /**
     * 迁移设置（版本更新时）
     */
    async migrateSettings(previousVersion) {
        console.log('Migrating settings from version:', previousVersion);
        
        // 这里可以添加版本迁移逻辑
        // 例如：添加新的默认设置项
        const currentSettings = await chrome.storage.sync.get();
        
        // 添加新设置项（如果不存在）
        const newSettings = {};
        if (!currentSettings.hasOwnProperty('enableAutoFill')) {
            newSettings.enableAutoFill = true;
        }
        if (!currentSettings.hasOwnProperty('theme')) {
            newSettings.theme = 'light';
        }

        if (Object.keys(newSettings).length > 0) {
            await chrome.storage.sync.set(newSettings);
            console.log('Settings migrated:', newSettings);
        }
    }

    /**
     * 处理消息
     */
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'getSettings':
                    const settings = await chrome.storage.sync.get();
                    sendResponse({ success: true, data: settings });
                    break;

                case 'saveSettings':
                    await chrome.storage.sync.set(request.settings);
                    sendResponse({ success: true });
                    break;

                case 'checkApiConnection':
                    const connectionResult = await this.checkApiConnection(request.apiUrl);
                    sendResponse(connectionResult);
                    break;

                case 'showNotification':
                    this.showNotification(request.title, request.message, request.type);
                    sendResponse({ success: true });
                    break;

                case 'openOptionsPage':
                    chrome.runtime.openOptionsPage();
                    sendResponse({ success: true });
                    break;

                case 'getTabInfo':
                    const tabInfo = await this.getTabInfo(sender.tab.id);
                    sendResponse(tabInfo);
                    break;

                default:
                    sendResponse({ error: 'Unknown action: ' + request.action });
            }
        } catch (error) {
            console.error('Background script error:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * 处理标签页更新
     */
    handleTabUpdated(tabId, changeInfo, tab) {
        // 当页面加载完成时，检查是否为货代相关网站
        if (changeInfo.status === 'complete' && tab.url) {
            this.checkFreightWebsite(tab);
        }
    }

    /**
     * 处理标签页激活
     */
    async handleTabActivated(activeInfo) {
        const tab = await chrome.tabs.get(activeInfo.tabId);
        this.updateBadge(tab);
    }

    /**
     * 检查是否为货代相关网站
     */
    checkFreightWebsite(tab) {
        const freightKeywords = [
            'freight', 'shipping', 'logistics', 'cargo', 'container',
            '货代', '物流', '运输', '集装箱', '海运', '空运',
            'forwarder', 'customs', 'import', 'export'
        ];

        const url = tab.url.toLowerCase();
        const title = (tab.title || '').toLowerCase();
        
        const isFreightSite = freightKeywords.some(keyword => 
            url.includes(keyword) || title.includes(keyword)
        );

        if (isFreightSite) {
            this.updateBadge(tab, 'freight');
            console.log('Detected freight website:', tab.url);
        }
    }

    /**
     * 更新扩展图标徽章
     */
    updateBadge(tab, type = '') {
        let badgeText = '';
        let badgeColor = '#007bff';

        switch (type) {
            case 'freight':
                badgeText = '🚢';
                badgeColor = '#28a745';
                break;
            case 'processing':
                badgeText = '⏳';
                badgeColor = '#ffc107';
                break;
            case 'success':
                badgeText = '✅';
                badgeColor = '#28a745';
                break;
            case 'error':
                badgeText = '❌';
                badgeColor = '#dc3545';
                break;
        }

        chrome.action.setBadgeText({
            text: badgeText,
            tabId: tab.id
        });

        if (badgeText) {
            chrome.action.setBadgeBackgroundColor({
                color: badgeColor,
                tabId: tab.id
            });
        }
    }

    /**
     * 检查API连接
     */
    async checkApiConnection(apiUrl) {
        try {
            const response = await fetch(`${apiUrl}/document/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                return {
                    success: true,
                    status: 'connected',
                    data: data
                };
            } else {
                return {
                    success: false,
                    status: 'error',
                    error: `HTTP ${response.status}: ${response.statusText}`
                };
            }
        } catch (error) {
            return {
                success: false,
                status: 'error',
                error: error.message
            };
        }
    }

    /**
     * 获取标签页信息
     */
    async getTabInfo(tabId) {
        try {
            const tab = await chrome.tabs.get(tabId);
            return {
                success: true,
                data: {
                    id: tab.id,
                    url: tab.url,
                    title: tab.title,
                    status: tab.status
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 显示通知
     */
    showNotification(title, message, type = 'basic') {
        const iconUrl = this.getNotificationIcon(type);
        
        chrome.notifications.create({
            type: 'basic',
            iconUrl: iconUrl,
            title: title,
            message: message
        });
    }

    /**
     * 显示欢迎通知
     */
    showWelcomeNotification() {
        this.showNotification(
            '货代单证智能解析助手',
            '扩展安装成功！点击工具栏图标开始使用智能解析功能。',
            'success'
        );
    }

    /**
     * 显示更新通知
     */
    showUpdateNotification() {
        this.showNotification(
            '货代单证智能解析助手',
            '扩展已更新到最新版本，享受更好的解析体验！',
            'info'
        );
    }

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return 'icons/icon48.png';
            case 'error':
                return 'icons/icon48.png';
            case 'warning':
                return 'icons/icon48.png';
            default:
                return 'icons/icon48.png';
        }
    }

    /**
     * 记录使用统计
     */
    async logUsage(action, data = {}) {
        try {
            const stats = await chrome.storage.local.get(['usageStats']) || { usageStats: {} };
            const today = new Date().toISOString().split('T')[0];
            
            if (!stats.usageStats[today]) {
                stats.usageStats[today] = {};
            }
            
            if (!stats.usageStats[today][action]) {
                stats.usageStats[today][action] = 0;
            }
            
            stats.usageStats[today][action]++;
            
            await chrome.storage.local.set(stats);
            console.log('Usage logged:', action, stats.usageStats[today]);
        } catch (error) {
            console.error('Failed to log usage:', error);
        }
    }

    /**
     * 清理旧数据
     */
    async cleanupOldData() {
        try {
            const stats = await chrome.storage.local.get(['usageStats']);
            if (stats.usageStats) {
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                
                const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];
                
                Object.keys(stats.usageStats).forEach(date => {
                    if (date < cutoffDate) {
                        delete stats.usageStats[date];
                    }
                });
                
                await chrome.storage.local.set(stats);
                console.log('Old usage data cleaned up');
            }
        } catch (error) {
            console.error('Failed to cleanup old data:', error);
        }
    }
}

// 初始化Background Script
new FreightParserBackground();

// 定期清理旧数据（每天一次）
chrome.alarms.create('cleanupOldData', { periodInMinutes: 24 * 60 });
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'cleanupOldData') {
        new FreightParserBackground().cleanupOldData();
    }
});
