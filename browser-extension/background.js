/**
 * 货代单证智能解析浏览器扩展 - Background Script
 * 负责扩展的后台逻辑、消息传递和状态管理
 */

// 监听扩展安装/更新
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed/updated:', details.reason);

    if (details.reason === 'install') {
        // 首次安装，设置默认配置
        setDefaultSettings();
        showWelcomeNotification();
    } else if (details.reason === 'update') {
        // 更新版本
        console.log('Extension updated from version:', details.previousVersion);
    }
});

// 监听来自content script和popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    handleMessage(request, sender, sendResponse);
    return true; // 保持消息通道开放
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        checkFreightWebsite(tab);
    }
});

console.log('FreightParser Background Script initialized');

/**
 * 设置默认配置
 */
async function setDefaultSettings() {
    const defaultSettings = {
        apiBaseUrl: 'http://localhost:8080/api',
        autoDetectDocumentType: true,
        enableOcr: true,
        ocrLanguage: 'chi_sim+eng',
        enableNotifications: true,
        enableAutoFill: true,
        theme: 'light'
    };

    try {
        await chrome.storage.sync.set(defaultSettings);
        console.log('Default settings saved:', defaultSettings);
    } catch (error) {
        console.error('Failed to save default settings:', error);
    }
}

/**
 * 处理消息
 */
async function handleMessage(request, sender, sendResponse) {
    try {
        switch (request.action) {
            case 'getSettings':
                const settings = await chrome.storage.sync.get();
                sendResponse({ success: true, data: settings });
                break;

            case 'saveSettings':
                await chrome.storage.sync.set(request.settings);
                sendResponse({ success: true });
                break;

            case 'checkApiConnection':
                const connectionResult = await checkApiConnection(request.apiUrl);
                sendResponse(connectionResult);
                break;

            case 'showNotification':
                showNotification(request.title, request.message, request.type);
                sendResponse({ success: true });
                break;

            case 'openOptionsPage':
                chrome.runtime.openOptionsPage();
                sendResponse({ success: true });
                break;

            default:
                sendResponse({ error: 'Unknown action: ' + request.action });
        }
    } catch (error) {
        console.error('Background script error:', error);
        sendResponse({ error: error.message });
    }
}

/**
 * 检查是否为货代相关网站
 */
function checkFreightWebsite(tab) {
    const freightKeywords = [
        'freight', 'shipping', 'logistics', 'cargo', 'container',
        '货代', '物流', '运输', '集装箱', '海运', '空运',
        'forwarder', 'customs', 'import', 'export'
    ];

    const url = tab.url.toLowerCase();
    const title = (tab.title || '').toLowerCase();

    const isFreightSite = freightKeywords.some(keyword =>
        url.includes(keyword) || title.includes(keyword)
    );

    if (isFreightSite) {
        console.log('Detected freight website:', tab.url);
        // 可以在这里添加徽章或其他标识
    }
}

/**
 * 检查API连接
 */
async function checkApiConnection(apiUrl) {
    try {
        const response = await fetch(`${apiUrl}/document/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            return {
                success: true,
                status: 'connected',
                data: data
            };
        } else {
            return {
                success: false,
                status: 'error',
                error: `HTTP ${response.status}: ${response.statusText}`
            };
        }
    } catch (error) {
        return {
            success: false,
            status: 'error',
            error: error.message
        };
    }
}

/**
 * 显示通知
 */
function showNotification(title, message, type = 'basic') {
    try {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: title,
            message: message
        });
    } catch (error) {
        console.error('Failed to show notification:', error);
    }
}

/**
 * 显示欢迎通知
 */
function showWelcomeNotification() {
    showNotification(
        '货代单证智能解析助手',
        '扩展安装成功！点击工具栏图标开始使用智能解析功能。'
    );
}

// 扩展初始化完成
console.log('FreightParser Background Script loaded successfully');
