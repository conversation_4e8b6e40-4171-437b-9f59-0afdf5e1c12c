/* 货代单证智能解析扩展 - 设置页面样式 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background: #f5f5f5;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo img {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.version {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 主内容区 */
.main-content {
    flex: 1;
    padding: 32px;
}

/* 标签导航 */
.tabs {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 32px;
    gap: 4px;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    border-radius: 6px 6px 0 0;
    transition: all 0.2s ease;
    position: relative;
}

.tab-button:hover {
    background: #f8f9fa;
    color: #333;
}

.tab-button.active {
    background: #007bff;
    color: white;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: #007bff;
}

/* 标签内容 */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 设置区块 */
.section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e0e0e0;
}

.section h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

/* 设置组 */
.setting-group {
    margin-bottom: 20px;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.setting-group:last-child {
    margin-bottom: 0;
}

/* 设置标签 */
.setting-label {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    margin-bottom: 8px;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: #007bff;
    border-color: #007bff;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.setting-description {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* 输入框样式 */
.setting-input,
.setting-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    margin-top: 4px;
}

.setting-input:focus,
.setting-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.setting-input[type="number"] {
    width: 120px;
}

.setting-input[type="url"] {
    font-family: monospace;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover:not(:disabled) {
    background: #007bff;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-icon {
    font-size: 16px;
}

/* 设置操作区 */
.setting-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

/* 连接状态 */
.connection-status {
    margin-left: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.connection-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.connection-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.connection-status.testing {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 关于页面样式 */
.about-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.info-item {
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.info-item strong {
    color: #007bff;
}

.description {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    margin-bottom: 24px;
}

.description p {
    margin: 0;
    line-height: 1.6;
}

.features {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    margin-bottom: 24px;
}

.features h3 {
    margin-bottom: 16px;
    color: #333;
}

.features ul {
    list-style: none;
    padding: 0;
}

.features li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.features li:last-child {
    border-bottom: none;
}

.links {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.link-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: #f8f9fa;
    color: #007bff;
    text-decoration: none;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.link-button:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* 使用统计 */
.usage-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.stat-item {
    background: white;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #007bff;
}

/* 底部操作栏 */
.footer {
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    padding: 20px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-actions {
    display: flex;
    gap: 12px;
}

.save-status {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.save-status.success {
    background: #d4edda;
    color: #155724;
}

.save-status.error {
    background: #f8d7da;
    color: #721c24;
}

.save-status.saving {
    background: #fff3cd;
    color: #856404;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    .header {
        padding: 16px 20px;
    }
    
    .logo h1 {
        font-size: 20px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .tabs {
        flex-wrap: wrap;
    }
    
    .tab-button {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .section {
        padding: 16px;
    }
    
    .footer {
        padding: 16px 20px;
        flex-direction: column;
        gap: 12px;
    }
    
    .footer-actions {
        width: 100%;
        justify-content: center;
    }
    
    .about-info {
        grid-template-columns: 1fr;
    }
    
    .usage-stats {
        grid-template-columns: 1fr;
    }
    
    .links {
        justify-content: center;
    }
    
    .setting-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .setting-actions .btn {
        justify-content: center;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .container {
        background: #2d2d2d;
    }
    
    .section {
        background: #3a3a3a;
        border-color: #555;
    }
    
    .setting-group {
        background: #2d2d2d;
        border-color: #555;
    }
    
    .setting-input,
    .setting-select {
        background: #3a3a3a;
        border-color: #555;
        color: #e0e0e0;
    }
    
    .checkmark {
        border-color: #666;
    }
    
    .footer {
        background: #3a3a3a;
        border-color: #555;
    }
    
    .about-info .info-item,
    .description,
    .features,
    .stat-item {
        background: #3a3a3a;
        border-color: #555;
    }
    
    .link-button {
        background: #3a3a3a;
        border-color: #555;
    }
    
    .link-button:hover {
        background: #4a4a4a;
    }
}

/* 打印样式 */
@media print {
    .header,
    .footer,
    .tabs {
        display: none;
    }
    
    .main-content {
        padding: 0;
    }
    
    .tab-content {
        display: block !important;
    }
    
    .section {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
