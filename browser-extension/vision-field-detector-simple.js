/**
 * 简化版视觉字段检测器
 * 不依赖外部库，使用浏览器原生API
 */

class VisionFieldDetector {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        console.log('VisionFieldDetector initialized (simple version)');
    }

    /**
     * 主要检测流程
     */
    async detectFields() {
        try {
            console.log('Starting vision-based field detection...');
            
            // 1. 分析页面布局
            const layoutAnalysis = await this.analyzePageLayout();
            
            // 2. 检测输入元素的视觉特征
            const visualElements = await this.detectVisualInputElements();
            
            // 3. 文本标签分析
            const textLabels = await this.analyzeTextLabels();
            
            // 4. 智能匹配
            const matchedFields = await this.matchFieldsIntelligently(visualElements, textLabels);
            
            console.log(`Vision detection found ${matchedFields.length} fields`);
            
            return {
                success: true,
                fields: matchedFields,
                confidence: this.calculateOverallConfidence(matchedFields)
            };
            
        } catch (error) {
            console.error('Vision field detection failed:', error);
            return {
                success: false,
                error: error.message,
                fields: []
            };
        }
    }

    /**
     * 分析页面布局
     */
    async analyzePageLayout() {
        const layout = {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
        };
        
        console.log('Page layout:', layout);
        return layout;
    }

    /**
     * 检测视觉输入元素
     */
    async detectVisualInputElements() {
        const elements = [];
        
        // 扩展的选择器，包含更多可能的输入控件
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="tel"]',
            'input[type="number"]',
            'input[type="date"]',
            'input[type="url"]',
            'input:not([type])',
            'textarea',
            'select',
            '[contenteditable="true"]',
            // 常见UI框架的输入控件
            '.ant-input',
            '.el-input__inner',
            '.form-control',
            '.layui-input',
            '.ivu-input',
            // 通过属性识别
            '[role="textbox"]',
            '[role="combobox"]',
            '[data-field]',
            '[data-name]'
        ];

        for (const selector of selectors) {
            try {
                const nodeList = document.querySelectorAll(selector);
                nodeList.forEach(element => {
                    if (this.isValidInputElement(element)) {
                        const visualInfo = this.extractVisualInfo(element);
                        if (visualInfo) {
                            elements.push(visualInfo);
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with selector ${selector}:`, error);
            }
        }

        console.log(`Found ${elements.length} visual input elements`);
        return elements;
    }

    /**
     * 检查是否为有效的输入元素
     */
    isValidInputElement(element) {
        // 基本检查
        if (!element || !element.isConnected) return false;
        
        // 排除隐藏元素
        if (element.type === 'hidden') return false;
        
        // 获取计算样式
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || style.visibility === 'hidden') {
            return false;
        }
        
        // 检查元素尺寸
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 && rect.height === 0) {
            return false;
        }
        
        // 排除按钮和密码字段
        const excludeTypes = ['button', 'submit', 'reset', 'password', 'file'];
        if (excludeTypes.includes(element.type)) {
            return false;
        }
        
        return true;
    }

    /**
     * 提取视觉信息
     */
    extractVisualInfo(element) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return {
            element: element,
            id: element.id || '',
            name: element.name || '',
            type: element.type || element.tagName.toLowerCase(),
            tagName: element.tagName.toLowerCase(),
            className: element.className || '',
            
            // 位置信息
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY,
            width: rect.width,
            height: rect.height,
            
            // 视觉特征
            backgroundColor: style.backgroundColor,
            borderColor: style.borderColor,
            borderWidth: style.borderWidth,
            borderStyle: style.borderStyle,
            fontSize: style.fontSize,
            fontFamily: style.fontFamily,
            
            // 可见性
            isVisible: rect.width > 0 && rect.height > 0,
            zIndex: style.zIndex,
            
            // 值和属性
            value: this.getElementValue(element),
            placeholder: element.placeholder || '',
            title: element.title || '',
            ariaLabel: element.getAttribute('aria-label') || ''
        };
    }

    /**
     * 获取元素值
     */
    getElementValue(element) {
        if (element.tagName.toLowerCase() === 'select') {
            return element.selectedOptions.length > 0 ? element.selectedOptions[0].text : '';
        } else if (element.contentEditable === 'true') {
            return element.textContent || element.innerText || '';
        } else {
            return element.value || '';
        }
    }

    /**
     * 分析文本标签
     */
    async analyzeTextLabels() {
        const labels = [];
        
        // 查找所有可能的标签元素
        const labelSelectors = [
            'label',
            '.label',
            '.form-label',
            '.field-label',
            'th', // 表格表头
            '.ant-form-item-label',
            '.el-form-item__label'
        ];

        for (const selector of labelSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const labelInfo = this.extractLabelInfo(element);
                    if (labelInfo) {
                        labels.push(labelInfo);
                    }
                });
            } catch (error) {
                console.warn(`Error with label selector ${selector}:`, error);
            }
        }

        // 查找独立的文本节点（可能是标签）
        const textNodes = this.findTextNodes();
        textNodes.forEach(node => {
            const labelInfo = this.extractTextNodeInfo(node);
            if (labelInfo) {
                labels.push(labelInfo);
            }
        });

        console.log(`Found ${labels.length} text labels`);
        return labels;
    }

    /**
     * 提取标签信息
     */
    extractLabelInfo(element) {
        const text = element.textContent.trim();
        if (!text || text.length > 50) return null;

        const rect = element.getBoundingClientRect();
        if (rect.width === 0 && rect.height === 0) return null;

        return {
            element: element,
            text: this.cleanLabelText(text),
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY,
            width: rect.width,
            height: rect.height,
            tagName: element.tagName.toLowerCase(),
            className: element.className || '',
            forAttribute: element.getAttribute('for') || ''
        };
    }

    /**
     * 查找文本节点
     */
    findTextNodes() {
        const textNodes = [];
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    const text = node.textContent.trim();
                    // 过滤条件：长度适中，包含中文或英文，可能是标签
                    if (text.length > 1 && text.length < 30 && 
                        /[\u4e00-\u9fa5a-zA-Z]/.test(text) &&
                        !/^\d+$/.test(text)) {
                        return NodeFilter.FILTER_ACCEPT;
                    }
                    return NodeFilter.FILTER_REJECT;
                }
            },
            false
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        return textNodes.slice(0, 100); // 限制数量避免性能问题
    }

    /**
     * 提取文本节点信息
     */
    extractTextNodeInfo(textNode) {
        const parent = textNode.parentElement;
        if (!parent) return null;

        const text = textNode.textContent.trim();
        const rect = parent.getBoundingClientRect();

        return {
            element: parent,
            text: this.cleanLabelText(text),
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY,
            width: rect.width,
            height: rect.height,
            tagName: parent.tagName.toLowerCase(),
            className: parent.className || '',
            isTextNode: true
        };
    }

    /**
     * 清理标签文本
     */
    cleanLabelText(text) {
        return text
            .replace(/[：:*]/g, '') // 移除冒号和星号
            .replace(/\s+/g, ' ') // 合并空白字符
            .trim();
    }

    /**
     * 智能匹配字段
     */
    async matchFieldsIntelligently(visualElements, textLabels) {
        const matchedFields = [];

        for (const element of visualElements) {
            // 查找最相关的标签
            const relevantLabels = this.findRelevantLabels(element, textLabels);
            
            // 分析字段类型
            const fieldType = this.analyzeFieldType(element, relevantLabels);
            
            if (fieldType.confidence > 0.3) {
                matchedFields.push({
                    element: element.element,
                    fieldType: fieldType.type,
                    confidence: fieldType.confidence,
                    visualInfo: element,
                    labels: relevantLabels.slice(0, 3), // 保留前3个最相关的标签
                    coordinates: {
                        x: element.x,
                        y: element.y,
                        width: element.width,
                        height: element.height
                    }
                });
            }
        }

        return matchedFields;
    }

    /**
     * 查找相关标签
     */
    findRelevantLabels(element, textLabels) {
        const relevantLabels = [];
        const maxDistance = 200; // 最大距离

        for (const label of textLabels) {
            // 1. 检查for属性关联
            if (label.forAttribute && label.forAttribute === element.id) {
                relevantLabels.push({
                    ...label,
                    relevance: 1.0,
                    relationship: 'for_attribute'
                });
                continue;
            }

            // 2. 计算空间距离
            const distance = this.calculateDistance(element, label);
            if (distance < maxDistance) {
                const relevance = 1 - (distance / maxDistance);
                const relationship = this.determineRelationship(element, label);
                
                relevantLabels.push({
                    ...label,
                    relevance: relevance,
                    relationship: relationship,
                    distance: distance
                });
            }
        }

        // 按相关性排序
        return relevantLabels.sort((a, b) => b.relevance - a.relevance);
    }

    /**
     * 计算距离
     */
    calculateDistance(element, label) {
        const elementCenterX = element.x + element.width / 2;
        const elementCenterY = element.y + element.height / 2;
        const labelCenterX = label.x + label.width / 2;
        const labelCenterY = label.y + label.height / 2;

        return Math.sqrt(
            Math.pow(elementCenterX - labelCenterX, 2) + 
            Math.pow(elementCenterY - labelCenterY, 2)
        );
    }

    /**
     * 确定关系类型
     */
    determineRelationship(element, label) {
        // 标签在左侧
        if (label.x + label.width < element.x) {
            return 'left';
        }
        // 标签在上方
        if (label.y + label.height < element.y) {
            return 'top';
        }
        // 标签在右侧
        if (label.x > element.x + element.width) {
            return 'right';
        }
        // 标签在下方
        if (label.y > element.y + element.height) {
            return 'bottom';
        }
        // 重叠
        return 'overlap';
    }

    /**
     * 分析字段类型
     */
    analyzeFieldType(element, relevantLabels) {
        const fieldKeywords = {
            'BL_NO': ['提单号', '提单', 'bl', 'bill', 'lading'],
            'SHIPPER': ['发货人', '托运人', 'shipper', 'consignor', '发货'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver', '收货'],
            'PORT_OF_LOADING': ['起运港', '装货港', 'pol', 'loading', '起运'],
            'PORT_OF_DISCHARGE': ['目的港', '卸货港', 'pod', 'discharge', '目的'],
            'VESSEL_VOYAGE': ['船名', '航次', 'vessel', 'voyage', '船'],
            'CONTAINER_NO': ['集装箱', '箱号', 'container', 'cntr', '集装'],
            'INVOICE_NO': ['发票号', '发票', 'invoice', 'inv'],
            'TOTAL_AMOUNT': ['金额', '总额', 'amount', 'total', 'value', '价格'],
            'DATE': ['日期', 'date', '时间'],
            'QUANTITY': ['数量', '件数', 'quantity', 'pieces', 'pkgs'],
            'WEIGHT': ['重量', '毛重', 'weight', 'gross'],
            'VOLUME': ['体积', '立方', 'volume', 'cbm']
        };

        let bestMatch = { type: 'UNKNOWN', confidence: 0 };

        for (const [fieldType, keywords] of Object.entries(fieldKeywords)) {
            let score = 0;

            // 检查标签文本
            for (const label of relevantLabels) {
                for (const keyword of keywords) {
                    if (label.text.toLowerCase().includes(keyword.toLowerCase())) {
                        score += label.relevance * 0.8; // 标签匹配权重高
                    }
                }
            }

            // 检查元素属性
            const elementText = [
                element.id,
                element.name,
                element.className,
                element.placeholder,
                element.title,
                element.ariaLabel
            ].join(' ').toLowerCase();

            for (const keyword of keywords) {
                if (elementText.includes(keyword.toLowerCase())) {
                    score += 0.6; // 元素属性匹配
                }
            }

            if (score > bestMatch.confidence) {
                bestMatch = { type: fieldType, confidence: Math.min(score, 1.0) };
            }
        }

        return bestMatch;
    }

    /**
     * 计算整体置信度
     */
    calculateOverallConfidence(fields) {
        if (fields.length === 0) return 0;

        const totalConfidence = fields.reduce((sum, field) => sum + field.confidence, 0);
        return totalConfidence / fields.length;
    }
}

// 确保全局可用
if (typeof window !== 'undefined') {
    window.VisionFieldDetector = VisionFieldDetector;
}
