/* 货代单证智能解析扩展 - Content Script 样式 */

/* 字段高亮样式 */
.freight-parser-highlight {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
    transition: all 0.3s ease !important;
}

.freight-parser-highlight:hover {
    outline-color: #0056b3 !important;
    background-color: rgba(0, 123, 255, 0.2) !important;
}

/* 填充效果样式 */
.freight-parser-filled {
    background-color: #e8f5e8 !important;
    border-color: #28a745 !important;
    transition: all 0.3s ease !important;
    animation: freight-parser-fill-pulse 0.6s ease-in-out;
}

@keyframes freight-parser-fill-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 字段标签提示 */
.freight-parser-tooltip {
    position: absolute !important;
    background: #333 !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    z-index: 10000 !important;
    pointer-events: none !important;
    white-space: nowrap !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.freight-parser-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    margin-left: -5px !important;
    border-width: 5px !important;
    border-style: solid !important;
    border-color: #333 transparent transparent transparent !important;
}

/* 扫描指示器 */
.freight-parser-scanning {
    position: relative !important;
}

.freight-parser-scanning::before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    border: 2px solid #007bff !important;
    border-radius: 4px !important;
    animation: freight-parser-scan 2s linear infinite !important;
    pointer-events: none !important;
    z-index: 9999 !important;
}

@keyframes freight-parser-scan {
    0% {
        border-color: #007bff;
        opacity: 1;
    }
    50% {
        border-color: #0056b3;
        opacity: 0.5;
    }
    100% {
        border-color: #007bff;
        opacity: 1;
    }
}

/* 错误状态样式 */
.freight-parser-error {
    outline: 2px solid #dc3545 !important;
    outline-offset: 2px !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

/* 成功状态样式 */
.freight-parser-success {
    outline: 2px solid #28a745 !important;
    outline-offset: 2px !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
}

/* 处理中状态样式 */
.freight-parser-processing {
    outline: 2px solid #ffc107 !important;
    outline-offset: 2px !important;
    background-color: rgba(255, 193, 7, 0.1) !important;
    position: relative !important;
}

.freight-parser-processing::after {
    content: '⏳' !important;
    position: absolute !important;
    top: 50% !important;
    right: 8px !important;
    transform: translateY(-50%) !important;
    font-size: 14px !important;
    animation: freight-parser-spin 1s linear infinite !important;
    pointer-events: none !important;
    z-index: 10001 !important;
}

@keyframes freight-parser-spin {
    from {
        transform: translateY(-50%) rotate(0deg);
    }
    to {
        transform: translateY(-50%) rotate(360deg);
    }
}

/* 字段匹配置信度指示器 */
.freight-parser-confidence {
    position: relative !important;
}

.freight-parser-confidence::before {
    content: attr(data-confidence) !important;
    position: absolute !important;
    top: -20px !important;
    right: 0 !important;
    background: #007bff !important;
    color: white !important;
    padding: 2px 6px !important;
    border-radius: 10px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    z-index: 10000 !important;
    pointer-events: none !important;
}

.freight-parser-confidence[data-confidence^="9"]::before {
    background: #28a745 !important;
}

.freight-parser-confidence[data-confidence^="8"]::before {
    background: #28a745 !important;
}

.freight-parser-confidence[data-confidence^="7"]::before {
    background: #ffc107 !important;
    color: #333 !important;
}

.freight-parser-confidence[data-confidence^="6"]::before {
    background: #fd7e14 !important;
}

.freight-parser-confidence[data-confidence^="5"]::before,
.freight-parser-confidence[data-confidence^="4"]::before,
.freight-parser-confidence[data-confidence^="3"]::before {
    background: #dc3545 !important;
}

/* 表单区域高亮 */
.freight-parser-form-highlight {
    outline: 2px dashed #007bff !important;
    outline-offset: 4px !important;
    background-color: rgba(0, 123, 255, 0.05) !important;
    border-radius: 4px !important;
}

/* 批量操作指示器 */
.freight-parser-batch-indicator {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: #007bff !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    z-index: 10002 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    animation: freight-parser-slide-in 0.3s ease-out !important;
}

@keyframes freight-parser-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 字段类型图标 */
.freight-parser-field-icon {
    position: absolute !important;
    top: 2px !important;
    left: 2px !important;
    width: 16px !important;
    height: 16px !important;
    background: #007bff !important;
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 10px !important;
    font-weight: bold !important;
    z-index: 10001 !important;
    pointer-events: none !important;
}

.freight-parser-field-icon.text::before {
    content: 'T' !important;
}

.freight-parser-field-icon.number::before {
    content: '#' !important;
}

.freight-parser-field-icon.date::before {
    content: '📅' !important;
    font-size: 8px !important;
}

.freight-parser-field-icon.email::before {
    content: '@' !important;
}

.freight-parser-field-icon.select::before {
    content: '▼' !important;
}

.freight-parser-field-icon.textarea::before {
    content: '📝' !important;
    font-size: 8px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .freight-parser-tooltip {
        font-size: 11px !important;
        padding: 3px 6px !important;
    }
    
    .freight-parser-batch-indicator {
        top: 10px !important;
        right: 10px !important;
        padding: 6px 12px !important;
        font-size: 11px !important;
    }
    
    .freight-parser-field-icon {
        width: 14px !important;
        height: 14px !important;
        font-size: 9px !important;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .freight-parser-tooltip {
        background: #555 !important;
        color: #fff !important;
    }
    
    .freight-parser-tooltip::after {
        border-color: #555 transparent transparent transparent !important;
    }
    
    .freight-parser-batch-indicator {
        background: #0d6efd !important;
    }
}

/* 打印时隐藏所有扩展元素 */
@media print {
    .freight-parser-highlight,
    .freight-parser-tooltip,
    .freight-parser-batch-indicator,
    .freight-parser-field-icon {
        display: none !important;
    }
}
