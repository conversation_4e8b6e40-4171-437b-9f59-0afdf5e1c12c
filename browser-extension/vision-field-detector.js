/**
 * 基于计算机视觉的字段识别器
 * 使用页面截图 + OCR + 视觉分析来识别表单字段
 */

class VisionFieldDetector {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.ocrWorker = null;
        this.initTesseract();
    }

    /**
     * 初始化Tesseract OCR
     */
    async initTesseract() {
        // 使用Tesseract.js进行OCR识别
        if (typeof Tesseract !== 'undefined') {
            this.ocrWorker = await Tesseract.createWorker('chi_sim+eng');
        }
    }

    /**
     * 主要识别流程
     */
    async detectFields() {
        try {
            // 1. 截取页面截图
            const screenshot = await this.capturePageScreenshot();
            
            // 2. 预处理图像
            const processedImage = await this.preprocessImage(screenshot);
            
            // 3. OCR文字识别
            const ocrResults = await this.performOCR(processedImage);
            
            // 4. 视觉元素检测
            const visualElements = await this.detectVisualElements(processedImage);
            
            // 5. 智能字段匹配
            const fields = await this.matchFieldsIntelligently(ocrResults, visualElements);
            
            // 6. 坐标映射回DOM
            const mappedFields = await this.mapCoordinatesToDOM(fields);
            
            return {
                success: true,
                fields: mappedFields,
                confidence: this.calculateOverallConfidence(fields)
            };
            
        } catch (error) {
            console.error('Vision field detection failed:', error);
            return {
                success: false,
                error: error.message,
                fields: []
            };
        }
    }

    /**
     * 截取页面截图
     */
    async capturePageScreenshot() {
        return new Promise((resolve, reject) => {
            // 使用Chrome扩展API截图
            chrome.tabs.captureVisibleTab(null, {
                format: 'png',
                quality: 100
            }, (dataUrl) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(dataUrl);
                }
            });
        });
    }

    /**
     * 图像预处理
     */
    async preprocessImage(imageDataUrl) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                // 创建canvas进行图像处理
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                
                // 绘制原图
                ctx.drawImage(img, 0, 0);
                
                // 图像增强处理
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const enhancedData = this.enhanceImageForOCR(imageData);
                
                ctx.putImageData(enhancedData, 0, 0);
                
                resolve(canvas.toDataURL());
            };
            img.src = imageDataUrl;
        });
    }

    /**
     * 图像增强（提高OCR识别率）
     */
    enhanceImageForOCR(imageData) {
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            // 转换为灰度
            const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
            
            // 增强对比度
            const enhanced = gray > 128 ? 255 : 0;
            
            data[i] = enhanced;     // R
            data[i + 1] = enhanced; // G
            data[i + 2] = enhanced; // B
            // data[i + 3] 保持alpha不变
        }
        
        return imageData;
    }

    /**
     * 执行OCR识别
     */
    async performOCR(imageDataUrl) {
        if (!this.ocrWorker) {
            throw new Error('OCR worker not initialized');
        }

        try {
            const { data } = await this.ocrWorker.recognize(imageDataUrl);
            
            // 解析OCR结果，提取文字和位置信息
            const textElements = data.words.map(word => ({
                text: word.text,
                confidence: word.confidence,
                bbox: word.bbox,
                x: word.bbox.x0,
                y: word.bbox.y0,
                width: word.bbox.x1 - word.bbox.x0,
                height: word.bbox.y1 - word.bbox.y0
            }));

            return textElements;
        } catch (error) {
            console.error('OCR recognition failed:', error);
            return [];
        }
    }

    /**
     * 检测视觉元素（输入框、按钮等）
     */
    async detectVisualElements(imageDataUrl) {
        // 使用OpenCV.js或自定义算法检测矩形框
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                // 边缘检测算法
                const elements = this.detectRectangularElements(canvas);
                resolve(elements);
            };
            img.src = imageDataUrl;
        });
    }

    /**
     * 检测矩形元素（简化的边缘检测）
     */
    detectRectangularElements(canvas) {
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const elements = [];

        // 简化的矩形检测算法
        // 实际项目中可以使用OpenCV.js的更高级算法
        const threshold = 50;
        const minWidth = 50;
        const minHeight = 20;

        // 扫描图像寻找矩形边界
        for (let y = 0; y < canvas.height - minHeight; y += 5) {
            for (let x = 0; x < canvas.width - minWidth; x += 5) {
                const rect = this.detectRectangleAt(imageData, x, y, minWidth, minHeight);
                if (rect && rect.confidence > threshold) {
                    elements.push(rect);
                }
            }
        }

        return elements;
    }

    /**
     * 在指定位置检测矩形
     */
    detectRectangleAt(imageData, startX, startY, minWidth, minHeight) {
        // 简化的矩形检测逻辑
        // 检查边界线的连续性
        const data = imageData.data;
        const width = imageData.width;
        
        // 检测水平边界
        let topBorder = this.detectHorizontalLine(data, width, startX, startY, minWidth);
        let bottomBorder = this.detectHorizontalLine(data, width, startX, startY + minHeight, minWidth);
        
        // 检测垂直边界
        let leftBorder = this.detectVerticalLine(data, width, startX, startY, minHeight);
        let rightBorder = this.detectVerticalLine(data, width, startX + minWidth, startY, minHeight);
        
        if (topBorder && bottomBorder && leftBorder && rightBorder) {
            return {
                x: startX,
                y: startY,
                width: minWidth,
                height: minHeight,
                confidence: (topBorder + bottomBorder + leftBorder + rightBorder) / 4,
                type: 'input_field'
            };
        }
        
        return null;
    }

    /**
     * 检测水平线
     */
    detectHorizontalLine(data, width, x, y, length) {
        let edgeCount = 0;
        for (let i = 0; i < length; i++) {
            const pixelIndex = ((y * width) + (x + i)) * 4;
            const gray = (data[pixelIndex] + data[pixelIndex + 1] + data[pixelIndex + 2]) / 3;
            
            // 检测边缘（简化）
            if (gray < 128) {
                edgeCount++;
            }
        }
        return edgeCount / length * 100; // 返回边缘密度百分比
    }

    /**
     * 检测垂直线
     */
    detectVerticalLine(data, width, x, y, length) {
        let edgeCount = 0;
        for (let i = 0; i < length; i++) {
            const pixelIndex = (((y + i) * width) + x) * 4;
            const gray = (data[pixelIndex] + data[pixelIndex + 1] + data[pixelIndex + 2]) / 3;
            
            if (gray < 128) {
                edgeCount++;
            }
        }
        return edgeCount / length * 100;
    }

    /**
     * 智能字段匹配
     */
    async matchFieldsIntelligently(ocrResults, visualElements) {
        const fields = [];
        
        // 为每个视觉元素寻找最近的文字标签
        for (const element of visualElements) {
            const nearbyTexts = this.findNearbyTexts(element, ocrResults);
            const fieldInfo = this.analyzeFieldType(element, nearbyTexts);
            
            if (fieldInfo.confidence > 60) {
                fields.push({
                    ...element,
                    ...fieldInfo,
                    labels: nearbyTexts
                });
            }
        }
        
        return fields;
    }

    /**
     * 查找附近的文字
     */
    findNearbyTexts(element, ocrResults) {
        const maxDistance = 100; // 最大距离阈值
        const nearbyTexts = [];
        
        for (const text of ocrResults) {
            const distance = this.calculateDistance(element, text);
            if (distance < maxDistance) {
                nearbyTexts.push({
                    ...text,
                    distance: distance,
                    position: this.getRelativePosition(element, text)
                });
            }
        }
        
        // 按距离排序
        return nearbyTexts.sort((a, b) => a.distance - b.distance);
    }

    /**
     * 计算两个元素之间的距离
     */
    calculateDistance(element1, element2) {
        const dx = (element1.x + element1.width/2) - (element2.x + element2.width/2);
        const dy = (element1.y + element1.height/2) - (element2.y + element2.height/2);
        return Math.sqrt(dx*dx + dy*dy);
    }

    /**
     * 获取相对位置
     */
    getRelativePosition(element, text) {
        const elementCenterX = element.x + element.width/2;
        const elementCenterY = element.y + element.height/2;
        const textCenterX = text.x + text.width/2;
        const textCenterY = text.y + text.height/2;
        
        if (textCenterX < element.x) return 'left';
        if (textCenterX > element.x + element.width) return 'right';
        if (textCenterY < element.y) return 'top';
        if (textCenterY > element.y + element.height) return 'bottom';
        return 'inside';
    }

    /**
     * 分析字段类型
     */
    analyzeFieldType(element, nearbyTexts) {
        const fieldKeywords = {
            'BL_NO': ['提单号', '提单', 'bl', 'bill'],
            'SHIPPER': ['发货人', '发货', 'shipper', '托运人'],
            'CONSIGNEE': ['收货人', '收货', 'consignee'],
            'PORT_OF_LOADING': ['起运港', '装货港', 'pol', 'loading'],
            'PORT_OF_DISCHARGE': ['目的港', '卸货港', 'pod', 'discharge'],
            'VESSEL_VOYAGE': ['船名', '航次', 'vessel', 'voyage'],
            'CONTAINER_NO': ['集装箱', '箱号', 'container', 'cntr'],
            'INVOICE_NO': ['发票号', '发票', 'invoice'],
            'TOTAL_AMOUNT': ['金额', '总额', 'amount', 'total']
        };
        
        let bestMatch = null;
        let maxScore = 0;
        
        for (const [fieldType, keywords] of Object.entries(fieldKeywords)) {
            let score = 0;
            
            for (const text of nearbyTexts) {
                for (const keyword of keywords) {
                    if (text.text.toLowerCase().includes(keyword.toLowerCase())) {
                        score += text.confidence * (1 / (text.distance + 1));
                    }
                }
            }
            
            if (score > maxScore) {
                maxScore = score;
                bestMatch = fieldType;
            }
        }
        
        return {
            fieldType: bestMatch,
            confidence: Math.min(maxScore, 100),
            matchedTexts: nearbyTexts.slice(0, 3) // 保留前3个最相关的文字
        };
    }

    /**
     * 将坐标映射回DOM元素
     */
    async mapCoordinatesToDOM(fields) {
        const mappedFields = [];
        
        for (const field of fields) {
            // 使用坐标查找对应的DOM元素
            const domElement = document.elementFromPoint(
                field.x + field.width/2, 
                field.y + field.height/2
            );
            
            if (domElement && this.isInputElement(domElement)) {
                mappedFields.push({
                    element: domElement,
                    fieldType: field.fieldType,
                    confidence: field.confidence,
                    visualInfo: field,
                    coordinates: {
                        x: field.x,
                        y: field.y,
                        width: field.width,
                        height: field.height
                    }
                });
            }
        }
        
        return mappedFields;
    }

    /**
     * 检查是否为输入元素
     */
    isInputElement(element) {
        const inputTags = ['input', 'textarea', 'select'];
        return inputTags.includes(element.tagName.toLowerCase()) ||
               element.contentEditable === 'true';
    }

    /**
     * 计算整体置信度
     */
    calculateOverallConfidence(fields) {
        if (fields.length === 0) return 0;
        
        const totalConfidence = fields.reduce((sum, field) => sum + field.confidence, 0);
        return totalConfidence / fields.length;
    }

    /**
     * 清理资源
     */
    async cleanup() {
        if (this.ocrWorker) {
            await this.ocrWorker.terminate();
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VisionFieldDetector;
} else {
    window.VisionFieldDetector = VisionFieldDetector;
}
