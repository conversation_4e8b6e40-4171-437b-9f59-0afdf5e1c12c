/**
 * 基于机器学习的字段识别器
 * 使用TensorFlow.js和预训练模型进行表单字段检测
 */

class MLFieldDetector {
    constructor() {
        this.model = null;
        this.isModelLoaded = false;
        this.initModel();
    }

    /**
     * 初始化机器学习模型
     */
    async initModel() {
        try {
            // 方案1: 使用预训练的目标检测模型 (COCO-SSD)
            this.objectDetectionModel = await cocoSsd.load();
            
            // 方案2: 使用自定义训练的表单字段检测模型
            // this.formFieldModel = await tf.loadLayersModel('/models/form-field-detector.json');
            
            this.isModelLoaded = true;
            console.log('ML models loaded successfully');
        } catch (error) {
            console.error('Failed to load ML models:', error);
        }
    }

    /**
     * 主要检测流程
     */
    async detectFields() {
        if (!this.isModelLoaded) {
            throw new Error('ML models not loaded');
        }

        try {
            // 1. 截取页面截图
            const screenshot = await this.captureScreenshot();
            
            // 2. 预处理图像
            const processedImage = await this.preprocessForML(screenshot);
            
            // 3. 目标检测 - 检测可能的输入区域
            const detectedObjects = await this.detectObjects(processedImage);
            
            // 4. 文字识别和分析
            const textAnalysis = await this.analyzeText(processedImage);
            
            // 5. 字段分类和匹配
            const classifiedFields = await this.classifyFields(detectedObjects, textAnalysis);
            
            // 6. 后处理和验证
            const validatedFields = await this.validateAndRefine(classifiedFields);
            
            return {
                success: true,
                fields: validatedFields,
                confidence: this.calculateConfidence(validatedFields)
            };
            
        } catch (error) {
            console.error('ML field detection failed:', error);
            return {
                success: false,
                error: error.message,
                fields: []
            };
        }
    }

    /**
     * 截取页面截图
     */
    async captureScreenshot() {
        return new Promise((resolve, reject) => {
            chrome.tabs.captureVisibleTab(null, {
                format: 'png',
                quality: 100
            }, (dataUrl) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(dataUrl);
                }
            });
        });
    }

    /**
     * 为机器学习预处理图像
     */
    async preprocessForML(imageDataUrl) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                // 创建canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 调整图像大小以适应模型输入
                const targetWidth = 640;
                const targetHeight = 480;
                
                canvas.width = targetWidth;
                canvas.height = targetHeight;
                
                // 保持宽高比缩放
                const scale = Math.min(targetWidth / img.width, targetHeight / img.height);
                const scaledWidth = img.width * scale;
                const scaledHeight = img.height * scale;
                
                const offsetX = (targetWidth - scaledWidth) / 2;
                const offsetY = (targetHeight - scaledHeight) / 2;
                
                ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);
                
                resolve({
                    canvas: canvas,
                    originalWidth: img.width,
                    originalHeight: img.height,
                    scale: scale,
                    offsetX: offsetX,
                    offsetY: offsetY
                });
            };
            img.src = imageDataUrl;
        });
    }

    /**
     * 使用目标检测模型检测对象
     */
    async detectObjects(processedImage) {
        try {
            // 使用COCO-SSD模型检测对象
            const predictions = await this.objectDetectionModel.detect(processedImage.canvas);
            
            // 过滤出可能的输入相关对象
            const relevantObjects = predictions.filter(prediction => {
                const relevantClasses = ['person', 'book', 'laptop', 'cell phone', 'mouse'];
                return prediction.score > 0.3; // 置信度阈值
            });
            
            // 转换坐标到原始图像尺寸
            return relevantObjects.map(obj => ({
                ...obj,
                bbox: this.scaleCoordinates(obj.bbox, processedImage)
            }));
            
        } catch (error) {
            console.error('Object detection failed:', error);
            return [];
        }
    }

    /**
     * 文字分析（结合OCR和NLP）
     */
    async analyzeText(processedImage) {
        try {
            // 使用Tesseract进行OCR
            const ocrWorker = await Tesseract.createWorker('chi_sim+eng');
            const { data } = await ocrWorker.recognize(processedImage.canvas);
            await ocrWorker.terminate();
            
            // 提取文字信息
            const textElements = data.words.map(word => ({
                text: word.text,
                confidence: word.confidence,
                bbox: this.scaleCoordinates([
                    word.bbox.x0, word.bbox.y0,
                    word.bbox.x1 - word.bbox.x0,
                    word.bbox.y1 - word.bbox.y0
                ], processedImage),
                semanticType: this.analyzeTextSemantic(word.text)
            }));
            
            return textElements;
            
        } catch (error) {
            console.error('Text analysis failed:', error);
            return [];
        }
    }

    /**
     * 语义分析文字内容
     */
    analyzeTextSemantic(text) {
        const patterns = {
            'field_label': /^(.*?)[：:]\s*$/,
            'bill_number': /^[A-Z]{2,4}\d{8,12}$/,
            'date': /^\d{4}[-/]\d{1,2}[-/]\d{1,2}$/,
            'amount': /^\d+(\.\d{2})?$/,
            'port_code': /^[A-Z]{5,6}$/,
            'container': /^[A-Z]{4}\d{7}$/
        };
        
        for (const [type, pattern] of Object.entries(patterns)) {
            if (pattern.test(text.trim())) {
                return type;
            }
        }
        
        return 'unknown';
    }

    /**
     * 字段分类和匹配
     */
    async classifyFields(detectedObjects, textAnalysis) {
        const fields = [];
        
        // 使用空间关系和语义信息进行匹配
        const inputRegions = await this.detectInputRegions(detectedObjects, textAnalysis);
        
        for (const region of inputRegions) {
            const fieldInfo = await this.classifyFieldType(region, textAnalysis);
            
            if (fieldInfo.confidence > 0.5) {
                fields.push({
                    ...region,
                    ...fieldInfo
                });
            }
        }
        
        return fields;
    }

    /**
     * 检测输入区域
     */
    async detectInputRegions(detectedObjects, textAnalysis) {
        const regions = [];
        
        // 方法1: 基于文字标签推断输入框位置
        const labels = textAnalysis.filter(text => text.semanticType === 'field_label');
        
        for (const label of labels) {
            // 在标签右侧或下方寻找可能的输入区域
            const inputRegion = this.findInputRegionNearLabel(label, textAnalysis);
            if (inputRegion) {
                regions.push(inputRegion);
            }
        }
        
        // 方法2: 基于视觉模式检测
        const visualRegions = await this.detectVisualInputPatterns(detectedObjects);
        regions.push(...visualRegions);
        
        return this.deduplicateRegions(regions);
    }

    /**
     * 在标签附近查找输入区域
     */
    findInputRegionNearLabel(label, textAnalysis) {
        const searchRadius = 200; // 搜索半径
        const labelRight = label.bbox[0] + label.bbox[2];
        const labelBottom = label.bbox[1] + label.bbox[3];
        
        // 定义可能的输入区域
        const possibleRegions = [
            // 标签右侧
            {
                x: labelRight + 10,
                y: label.bbox[1],
                width: 200,
                height: label.bbox[3],
                confidence: 0.8
            },
            // 标签下方
            {
                x: label.bbox[0],
                y: labelBottom + 5,
                width: 200,
                height: 30,
                confidence: 0.6
            }
        ];
        
        // 选择最可能的区域
        return possibleRegions.reduce((best, current) => 
            current.confidence > best.confidence ? current : best
        );
    }

    /**
     * 检测视觉输入模式
     */
    async detectVisualInputPatterns(detectedObjects) {
        // 这里可以使用更复杂的计算机视觉算法
        // 例如检测矩形边框、输入框的视觉特征等
        
        return []; // 简化实现
    }

    /**
     * 分类字段类型
     */
    async classifyFieldType(region, textAnalysis) {
        // 查找区域附近的文字
        const nearbyTexts = this.findTextsNearRegion(region, textAnalysis);
        
        // 使用规则和机器学习结合的方法分类
        const ruleBasedType = this.classifyByRules(nearbyTexts);
        
        // 如果有自定义模型，可以使用ML分类
        // const mlBasedType = await this.classifyByML(region, nearbyTexts);
        
        return {
            fieldType: ruleBasedType.type,
            confidence: ruleBasedType.confidence,
            nearbyTexts: nearbyTexts
        };
    }

    /**
     * 基于规则的分类
     */
    classifyByRules(nearbyTexts) {
        const fieldPatterns = {
            'BL_NO': ['提单号', '提单', 'bl', 'bill of lading'],
            'SHIPPER': ['发货人', '托运人', 'shipper', 'consignor'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver'],
            'PORT_OF_LOADING': ['起运港', '装货港', 'pol', 'port of loading'],
            'PORT_OF_DISCHARGE': ['目的港', '卸货港', 'pod', 'port of discharge'],
            'VESSEL_VOYAGE': ['船名', '航次', 'vessel', 'voyage'],
            'CONTAINER_NO': ['集装箱号', '箱号', 'container', 'cntr'],
            'INVOICE_NO': ['发票号', 'invoice', 'inv'],
            'TOTAL_AMOUNT': ['金额', '总额', 'amount', 'total', 'value']
        };
        
        let bestMatch = { type: 'UNKNOWN', confidence: 0 };
        
        for (const [fieldType, patterns] of Object.entries(fieldPatterns)) {
            let score = 0;
            
            for (const text of nearbyTexts) {
                for (const pattern of patterns) {
                    if (text.text.toLowerCase().includes(pattern.toLowerCase())) {
                        score += text.confidence * 0.01; // 转换为0-1范围
                    }
                }
            }
            
            if (score > bestMatch.confidence) {
                bestMatch = { type: fieldType, confidence: score };
            }
        }
        
        return bestMatch;
    }

    /**
     * 查找区域附近的文字
     */
    findTextsNearRegion(region, textAnalysis) {
        const maxDistance = 150;
        const nearbyTexts = [];
        
        for (const text of textAnalysis) {
            const distance = this.calculateDistance(region, text.bbox);
            if (distance < maxDistance) {
                nearbyTexts.push({
                    ...text,
                    distance: distance
                });
            }
        }
        
        return nearbyTexts.sort((a, b) => a.distance - b.distance);
    }

    /**
     * 计算距离
     */
    calculateDistance(region, bbox) {
        const regionCenterX = region.x + region.width / 2;
        const regionCenterY = region.y + region.height / 2;
        const bboxCenterX = bbox[0] + bbox[2] / 2;
        const bboxCenterY = bbox[1] + bbox[3] / 2;
        
        return Math.sqrt(
            Math.pow(regionCenterX - bboxCenterX, 2) + 
            Math.pow(regionCenterY - bboxCenterY, 2)
        );
    }

    /**
     * 坐标缩放
     */
    scaleCoordinates(bbox, processedImage) {
        const scaleX = processedImage.originalWidth / processedImage.canvas.width;
        const scaleY = processedImage.originalHeight / processedImage.canvas.height;
        
        return [
            (bbox[0] - processedImage.offsetX) * scaleX,
            (bbox[1] - processedImage.offsetY) * scaleY,
            bbox[2] * scaleX,
            bbox[3] * scaleY
        ];
    }

    /**
     * 去重区域
     */
    deduplicateRegions(regions) {
        const deduplicated = [];
        const threshold = 50; // 重叠阈值
        
        for (const region of regions) {
            const isOverlapping = deduplicated.some(existing => 
                this.calculateDistance(region, existing) < threshold
            );
            
            if (!isOverlapping) {
                deduplicated.push(region);
            }
        }
        
        return deduplicated;
    }

    /**
     * 验证和优化结果
     */
    async validateAndRefine(fields) {
        // 过滤低置信度结果
        const filtered = fields.filter(field => field.confidence > 0.3);
        
        // 去重
        const deduplicated = this.deduplicateRegions(filtered);
        
        // 映射到DOM元素
        const mapped = await this.mapToDOM(deduplicated);
        
        return mapped;
    }

    /**
     * 映射到DOM元素
     */
    async mapToDOM(fields) {
        const mappedFields = [];
        
        for (const field of fields) {
            const element = document.elementFromPoint(
                field.x + field.width / 2,
                field.y + field.height / 2
            );
            
            if (element && this.isInputElement(element)) {
                mappedFields.push({
                    element: element,
                    fieldType: field.fieldType,
                    confidence: field.confidence,
                    coordinates: field
                });
            }
        }
        
        return mappedFields;
    }

    /**
     * 检查是否为输入元素
     */
    isInputElement(element) {
        const inputTags = ['input', 'textarea', 'select'];
        return inputTags.includes(element.tagName.toLowerCase()) ||
               element.contentEditable === 'true';
    }

    /**
     * 计算整体置信度
     */
    calculateConfidence(fields) {
        if (fields.length === 0) return 0;
        
        const totalConfidence = fields.reduce((sum, field) => sum + field.confidence, 0);
        return totalConfidence / fields.length;
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MLFieldDetector;
} else {
    window.MLFieldDetector = MLFieldDetector;
}
