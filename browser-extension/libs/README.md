# 依赖库下载说明

为了使用先进的字段识别技术，需要下载以下JavaScript库：

## 📦 必需的库文件

### 1. Tesseract.js (OCR文字识别)
```bash
# 下载地址
https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js

# 或者使用npm
npm install tesseract.js
```

### 2. TensorFlow.js (机器学习)
```bash
# 下载地址
https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4/dist/tf.min.js

# COCO-SSD模型 (目标检测)
https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2/dist/coco-ssd.min.js
```

### 3. OpenCV.js (计算机视觉) - 可选
```bash
# 下载地址
https://docs.opencv.org/4.x/opencv.js
```

## 🔧 安装步骤

### 方法一：手动下载
1. 创建 `browser-extension/libs/` 目录
2. 下载上述文件到该目录
3. 重命名文件：
   - `tesseract.min.js`
   - `tensorflow.min.js`
   - `coco-ssd.min.js`
   - `opencv.min.js` (可选)

### 方法二：使用CDN (推荐)
修改检测器文件，直接从CDN加载：

```javascript
// 在vision-field-detector.js中添加
async function loadTesseract() {
    if (typeof Tesseract === 'undefined') {
        await loadScript('https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js');
    }
}

async function loadTensorFlow() {
    if (typeof tf === 'undefined') {
        await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4/dist/tf.min.js');
        await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2/dist/coco-ssd.min.js');
    }
}

function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}
```

## 📁 目录结构
```
browser-extension/
├── libs/
│   ├── tesseract.min.js
│   ├── tensorflow.min.js
│   ├── coco-ssd.min.js
│   └── opencv.min.js (可选)
├── vision-field-detector.js
├── ml-field-detector.js
└── content.js
```

## 🚀 快速设置脚本

创建 `download-libs.sh` 脚本：

```bash
#!/bin/bash
mkdir -p libs
cd libs

echo "Downloading Tesseract.js..."
curl -o tesseract.min.js https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js

echo "Downloading TensorFlow.js..."
curl -o tensorflow.min.js https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4/dist/tf.min.js

echo "Downloading COCO-SSD..."
curl -o coco-ssd.min.js https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2/dist/coco-ssd.min.js

echo "All libraries downloaded successfully!"
```

运行脚本：
```bash
chmod +x download-libs.sh
./download-libs.sh
```

## ⚠️ 注意事项

1. **文件大小**：这些库文件较大，会增加扩展包大小
2. **加载时间**：首次加载可能需要几秒钟
3. **网络依赖**：使用CDN需要网络连接
4. **兼容性**：确保Chrome版本支持这些API

## 🔄 降级策略

如果高级检测失败，系统会自动降级到DOM检测：

```javascript
// 在content.js中
try {
    result = await this.scanWithVision();
} catch (error) {
    console.error('Vision detection failed, falling back to DOM');
    result = await this.scanWithDOM();
}
```

## 📊 性能对比

| 检测方法 | 准确率 | 速度 | 资源占用 | 兼容性 |
|---------|--------|------|----------|--------|
| DOM解析 | 30-50% | 快 | 低 | 高 |
| 视觉检测 | 70-85% | 中等 | 中等 | 中等 |
| 机器学习 | 85-95% | 慢 | 高 | 低 |

## 🎯 推荐配置

对于你的货代系统，推荐使用**视觉检测**方案：
- 准确率显著提升
- 不依赖DOM结构
- 资源占用适中
- 兼容性较好
