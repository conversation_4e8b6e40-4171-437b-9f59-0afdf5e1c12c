/**
 * 货代单证智能解析浏览器扩展 - Popup脚本
 */

class FreightParserPopup {
    constructor() {
        this.selectedFile = null;
        this.parseResult = null;
        this.apiBaseUrl = 'http://localhost:8080/api';
        
        this.init();
    }

    async init() {
        // 绑定事件
        this.bindEvents();

        // 加载设置
        await this.loadSettings();

        // 延迟扫描当前页面，避免初始化时的连接错误
        setTimeout(() => {
            this.scanCurrentPage();
        }, 1000);

        console.log('Popup initialized');
    }

    bindEvents() {
        // 文件上传
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            if (file) this.handleFileSelect(file);
        });

        // 按钮事件
        document.getElementById('parseBtn').addEventListener('click', () => this.parseDocument());
        document.getElementById('fillBtn').addEventListener('click', () => this.fillForm());
        document.getElementById('scanPageBtn').addEventListener('click', () => this.scanCurrentPage());
        document.getElementById('highlightFieldsBtn').addEventListener('click', () => this.highlightFields());
        
        // 链接事件
        document.getElementById('settingsLink').addEventListener('click', () => this.openSettings());
        document.getElementById('helpLink').addEventListener('click', () => this.openHelp());
    }

    handleFileSelect(file) {
        if (!file) return;
        
        // 验证文件
        if (!this.validateFile(file)) return;
        
        this.selectedFile = file;
        
        // 更新UI
        const uploadArea = document.getElementById('uploadArea');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'block';
        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        
        // 启用解析按钮
        document.getElementById('parseBtn').disabled = false;
        
        this.updateStatus('文件已选择', 'ready');
    }

    validateFile(file) {
        const maxSize = 20 * 1024 * 1024; // 20MB
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        
        if (file.size > maxSize) {
            this.showError('文件大小不能超过20MB');
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            this.showError('只支持PDF、JPG、PNG格式文件');
            return false;
        }
        
        return true;
    }

    formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }

    async parseDocument() {
        if (!this.selectedFile) {
            this.showError('请先选择文件');
            return;
        }

        try {
            this.showProgress('正在上传文件...', 10);
            this.updateStatus('解析中...', 'processing');

            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('documentType', document.getElementById('documentType').value);
            formData.append('clientId', 'browser_extension');
            formData.append('userId', 'extension_user');
            formData.append('enableOcr', 'true');
            formData.append('ocrLanguage', 'chi_sim+eng');

            this.showProgress('正在AI解析...', 50);

            const response = await fetch(`${this.apiBaseUrl}/document/parse`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            this.showProgress('解析完成', 100);

            if (result.success) {
                this.parseResult = result.data;
                this.showResult(result.data);
                document.getElementById('fillBtn').disabled = false;
                this.updateStatus('解析成功', 'success');
            } else {
                throw new Error(result.message || '解析失败');
            }

        } catch (error) {
            console.error('Parse error:', error);
            this.showError('解析失败: ' + error.message);
            this.updateStatus('解析失败', 'error');
        } finally {
            this.hideProgress();
        }
    }

    async fillForm() {
        if (!this.parseResult || !this.parseResult.parsedData) {
            this.showError('没有可用的解析结果');
            return;
        }

        try {
            this.updateStatus('回填中...', 'processing');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // 检查是否是有效的页面
            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                throw new Error('当前页面不支持表单回填');
            }

            // 尝试发送消息，如果失败则注入Content Script
            try {
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'fillForm',
                    data: this.parseResult.parsedData
                });

                if (response && response.success) {
                    this.updateStatus(`成功回填 ${response.filledCount} 个字段`, 'success');
                    this.showSuccess(`成功回填 ${response.filledCount} 个字段`);
                } else {
                    throw new Error(response?.error || '回填失败');
                }
            } catch (messageError) {
                console.log('Content script not ready for fill, injecting...');

                // 注入Content Script
                await this.injectContentScript(tab.id);

                // 等待一下再重试
                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'fillForm',
                            data: this.parseResult.parsedData
                        });

                        if (response && response.success) {
                            this.updateStatus(`成功回填 ${response.filledCount} 个字段`, 'success');
                            this.showSuccess(`成功回填 ${response.filledCount} 个字段`);
                        } else {
                            throw new Error(response?.error || '回填失败');
                        }
                    } catch (retryError) {
                        console.error('Retry fill failed:', retryError);
                        this.showError('回填失败: ' + retryError.message);
                        this.updateStatus('回填失败', 'error');
                    }
                }, 500);
            }

        } catch (error) {
            console.error('Fill error:', error);
            this.showError('回填失败: ' + error.message);
            this.updateStatus('回填失败', 'error');
        }
    }

    async scanCurrentPage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // 检查是否是有效的页面
            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                document.getElementById('formCount').textContent = '0';
                document.getElementById('fieldCount2').textContent = '0';
                return;
            }

            // 尝试发送消息，如果失败则注入Content Script
            try {
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'scanPage'
                });

                if (response) {
                    document.getElementById('formCount').textContent = response.formCount || 0;
                    document.getElementById('fieldCount2').textContent = response.fieldCount || 0;

                    // 显示检测方法
                    if (response.method) {
                        console.log('Detection method:', response.method);
                        // 可以在UI中显示检测方法
                        const statusElement = document.getElementById('status');
                        if (statusElement) {
                            const methodText = {
                                'vision': '视觉检测',
                                'ml': '机器学习',
                                'dom': 'DOM解析'
                            };
                            statusElement.title = `使用${methodText[response.method] || response.method}检测`;
                        }
                    }
                } else {
                    throw new Error('No response from content script');
                }
            } catch (messageError) {
                console.log('Content script not ready, injecting...');

                // 注入Content Script
                await this.injectContentScript(tab.id);

                // 等待一下再重试
                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'scanPage'
                        });

                        if (response) {
                            document.getElementById('formCount').textContent = response.formCount || 0;
                            document.getElementById('fieldCount2').textContent = response.fieldCount || 0;
                        }
                    } catch (retryError) {
                        console.error('Retry scan failed:', retryError);
                        document.getElementById('formCount').textContent = '?';
                        document.getElementById('fieldCount2').textContent = '?';
                    }
                }, 500);
            }

        } catch (error) {
            console.error('Scan error:', error);
            document.getElementById('formCount').textContent = '?';
            document.getElementById('fieldCount2').textContent = '?';
        }
    }

    showProgress(text, percent = 0) {
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        progressSection.style.display = 'block';
        progressFill.style.width = percent + '%';
        progressText.textContent = text;
    }

    hideProgress() {
        setTimeout(() => {
            document.getElementById('progressSection').style.display = 'none';
        }, 1000);
    }

    showResult(data) {
        const resultSection = document.getElementById('resultSection');
        const fieldCount = document.getElementById('fieldCount');
        const confidence = document.getElementById('confidence');
        
        resultSection.style.display = 'block';
        fieldCount.textContent = Object.keys(data.parsedData || {}).length;
        confidence.textContent = (data.confidenceScore || 0) + '%';
    }

    viewResult() {
        if (!this.parseResult) return;
        
        // 创建新窗口显示详细结果
        const resultWindow = window.open('', '_blank', 'width=600,height=400');
        resultWindow.document.write(`
            <html>
                <head>
                    <title>解析结果详情</title>
                    <style>
                        body { font-family: monospace; padding: 20px; }
                        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <h2>解析结果详情</h2>
                    <pre>${JSON.stringify(this.parseResult, null, 2)}</pre>
                </body>
            </html>
        `);
    }

    updateStatus(text, type = 'ready') {
        const statusText = document.querySelector('.status-text');
        const statusDot = document.querySelector('.status-dot');
        
        statusText.textContent = text;
        
        // 移除所有状态类
        statusDot.className = 'status-dot';
        
        // 添加对应状态类
        switch (type) {
            case 'processing':
                statusDot.style.background = '#ffc107';
                break;
            case 'success':
                statusDot.style.background = '#28a745';
                break;
            case 'error':
                statusDot.style.background = '#dc3545';
                break;
            default:
                statusDot.style.background = '#007bff';
        }
    }

    showError(message) {
        console.error('FreightParser Error:', message);

        // 更友好的错误提示
        if (message.includes('Could not establish connection')) {
            message = '页面连接失败，请刷新页面后重试';
        } else if (message.includes('Extension context invalidated')) {
            message = '扩展已更新，请刷新页面后重试';
        }

        // 可以后续改为更好的UI
        alert('错误: ' + message);
    }

    showSuccess(message) {
        console.log('FreightParser Success:', message);
        // 简单的成功提示，可以后续改为更好的UI
        alert('成功: ' + message);
    }

    async loadSettings() {
        try {
            const settings = await chrome.storage.sync.get(['apiBaseUrl']);
            if (settings.apiBaseUrl) {
                this.apiBaseUrl = settings.apiBaseUrl;
            }
        } catch (error) {
            console.error('Load settings error:', error);
        }
    }

    openSettings() {
        chrome.runtime.openOptionsPage();
    }

    openHelp() {
        chrome.tabs.create({
            url: 'https://github.com/your-repo/freight-parser-extension'
        });
    }

    /**
     * 刷新当前页面
     */
    async refreshCurrentPage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.reload(tab.id);

            // 关闭popup
            window.close();
        } catch (error) {
            console.error('Failed to refresh page:', error);
            this.showError('刷新页面失败');
        }
    }

    /**
     * 分析页面
     */
    async analyzePage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持分析');
                return;
            }

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'analyzePageElements'
            });

            if (response && response.success) {
                // 打开分析结果窗口
                const analysisWindow = window.open('', '_blank', 'width=900,height=700');
                analysisWindow.document.write(`
                    <html>
                        <head>
                            <title>页面元素分析</title>
                            <style>
                                body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                                .section { background: white; margin: 15px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                                .section h3 { color: #007bff; margin-top: 0; }
                                .stat-item { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
                                .stat-item:last-child { border-bottom: none; }
                                .count { font-weight: bold; color: #28a745; }
                                .framework { display: inline-block; background: #007bff; color: white; padding: 3px 8px; margin: 2px; border-radius: 12px; font-size: 12px; }
                                .element-sample { background: #f8f9fa; padding: 8px; margin: 5px 0; border-radius: 4px; font-size: 12px; }
                                .visible { color: #28a745; }
                                .hidden { color: #dc3545; }
                            </style>
                        </head>
                        <body>
                            <h2>🔍 页面元素分析报告</h2>
                            <div class="section">
                                <h3>📊 基本信息</h3>
                                <div class="stat-item"><span>页面标题:</span><span>${response.analysis.pageInfo.title}</span></div>
                                <div class="stat-item"><span>域名:</span><span>${response.analysis.pageInfo.domain}</span></div>
                                <div class="stat-item"><span>总元素数:</span><span class="count">${response.analysis.totalElements}</span></div>
                            </div>
                            <div class="section">
                                <h3>🛠️ 检测到的框架</h3>
                                ${response.analysis.frameworks.length > 0 ?
                                    response.analysis.frameworks.map(fw => `<span class="framework">${fw}</span>`).join('') :
                                    '<p>未检测到已知框架</p>'
                                }
                            </div>
                            <div class="section">
                                <h3>📝 输入元素统计</h3>
                                <div id="inputStats"></div>
                            </div>
                        </body>
                    </html>
                `);

                const inputStatsDiv = analysisWindow.document.getElementById('inputStats');
                Object.entries(response.analysis.inputElements).forEach(([type, data]) => {
                    if (data.error) {
                        inputStatsDiv.innerHTML += `<div class="stat-item"><span>${type}:</span><span style="color: red;">错误: ${data.error}</span></div>`;
                    } else {
                        inputStatsDiv.innerHTML += `
                            <div class="stat-item">
                                <span>${type}:</span>
                                <span class="count">${data.count}</span>
                            </div>
                            ${data.elements && data.elements.length > 0 ?
                                data.elements.map(el => `
                                    <div class="element-sample">
                                        &lt;${el.tag}${el.id ? ' id="' + el.id + '"' : ''}${el.name ? ' name="' + el.name + '"' : ''}&gt;
                                        <span class="${el.visible ? 'visible' : 'hidden'}">${el.visible ? '👁️ 可见' : '🙈 隐藏'}</span>
                                    </div>
                                `).join('') : ''
                            }
                        `;
                    }
                });

            } else {
                this.showError('页面分析失败');
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showError('分析失败: ' + error.message);
        }
    }

    /**
     * 强制使用截图检测
     */
    async forceScreenshotDetection() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持截图检测');
                return;
            }

            this.updateStatus('正在截图检测...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'forceScreenshotDetection'
            });

            if (response && response.success) {
                document.getElementById('formCount').textContent = response.formCount || 0;
                document.getElementById('fieldCount2').textContent = response.fieldCount || 0;
                this.updateStatus(`截图检测完成，找到 ${response.fieldCount} 个字段`, 'success');
                this.showSuccess(`截图检测找到 ${response.fieldCount} 个字段`);
            } else {
                throw new Error(response?.error || '截图检测失败');
            }

        } catch (error) {
            console.error('Screenshot detection error:', error);
            this.showError('截图检测失败: ' + error.message);
            this.updateStatus('截图检测失败', 'error');
        }
    }

    /**
     * 深度调试
     */
    async deepDebug() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持深度调试');
                return;
            }

            this.updateStatus('正在深度调试...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'deepDebug'
            });

            if (response && response.success) {
                // 打开详细调试窗口
                const debugWindow = window.open('', '_blank', 'width=1200,height=800');
                debugWindow.document.write(`
                    <html>
                        <head>
                            <title>🔬 深度调试报告</title>
                            <style>
                                body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                                .section { background: white; margin: 15px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                                .section h3 { color: #007bff; margin-top: 0; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                                .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
                                .element-item { background: #e9ecef; margin: 5px 0; padding: 8px; border-radius: 4px; font-size: 12px; }
                                .valid { border-left: 4px solid #28a745; }
                                .invalid { border-left: 4px solid #dc3545; }
                                .matched { background: #d4edda; border-left: 4px solid #28a745; }
                                .unmatched { background: #f8d7da; border-left: 4px solid #dc3545; }
                                .selector-result { margin: 5px 0; padding: 5px; background: #fff; border: 1px solid #dee2e6; }
                                .count { font-weight: bold; color: #007bff; }
                                .distance { color: #6c757d; font-size: 11px; }
                            </style>
                        </head>
                        <body>
                            <h2>🔬 深度调试报告</h2>
                            <div id="debugContent"></div>
                        </body>
                    </html>
                `);

                const contentDiv = debugWindow.document.getElementById('debugContent');

                // 显示检测步骤
                contentDiv.innerHTML = `
                    <div class="section">
                        <h3>📊 检测概览</h3>
                        <div class="step">总共找到 <span class="count">${response.debug.totalElements}</span> 个候选元素</div>
                        <div class="step">通过验证的元素：<span class="count">${response.debug.validElements}</span> 个</div>
                        <div class="step">找到文字标签：<span class="count">${response.debug.totalLabels}</span> 个</div>
                        <div class="step">最终匹配字段：<span class="count">${response.debug.finalFields}</span> 个</div>
                    </div>

                    <div class="section">
                        <h3>🔍 选择器检测结果</h3>
                        ${response.debug.selectorResults.map(result => `
                            <div class="selector-result">
                                <strong>${result.selector}</strong>:
                                找到 <span class="count">${result.found}</span> 个元素,
                                有效 <span class="count">${result.valid}</span> 个
                                ${result.elements.length > 0 ? `
                                    <div style="margin-top: 5px;">
                                        ${result.elements.map(el => `
                                            <div class="element-item ${el.valid ? 'valid' : 'invalid'}">
                                                &lt;${el.tag}${el.id ? ' id="' + el.id + '"' : ''}${el.name ? ' name="' + el.name + '"' : ''}${el.class ? ' class="' + el.class + '"' : ''}&gt;
                                                ${el.valid ? '✅' : '❌'} ${el.reason || ''}
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h3>📝 文字标签分析</h3>
                        ${response.debug.labelAnalysis.map(label => `
                            <div class="element-item">
                                "${label.text}" - 位置: (${Math.round(label.x)}, ${Math.round(label.y)}) - 尺寸: ${Math.round(label.width)}x${Math.round(label.height)}
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h3>🔗 字段匹配详情</h3>
                        ${response.debug.matchingDetails.map(match => `
                            <div class="element-item ${match.matched ? 'matched' : 'unmatched'}">
                                <strong>字段:</strong> &lt;${match.element.tag}${match.element.id ? ' id="' + match.element.id + '"' : ''}&gt;<br>
                                <strong>位置:</strong> (${Math.round(match.element.x)}, ${Math.round(match.element.y)})<br>
                                <strong>最近标签:</strong> ${match.nearbyLabels.length > 0 ?
                                    match.nearbyLabels.slice(0, 3).map(label =>
                                        `"${label.text}" <span class="distance">(距离: ${Math.round(label.distance)}px)</span>`
                                    ).join(', ') : '无'}<br>
                                <strong>字段类型:</strong> ${match.fieldType} (置信度: ${Math.round(match.confidence * 100)}%)<br>
                                ${match.matched ? '✅ 已匹配' : '❌ 未匹配'}
                            </div>
                        `).join('')}
                    </div>
                `;

                this.updateStatus('深度调试完成', 'success');

            } else {
                this.showError('深度调试失败');
            }

        } catch (error) {
            console.error('Deep debug error:', error);
            this.showError('深度调试失败: ' + error.message);
        }
    }

    /**
     * 激进扫描
     */
    async aggressiveScan() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持激进扫描');
                return;
            }

            this.updateStatus('正在激进扫描...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'aggressiveScan'
            });

            if (response && response.success) {
                document.getElementById('formCount').textContent = response.formCount || 0;
                document.getElementById('fieldCount2').textContent = response.fieldCount || 0;
                this.updateStatus(`激进扫描完成，找到 ${response.fieldCount} 个字段`, 'success');
                this.showSuccess(`激进扫描找到 ${response.fieldCount} 个字段！包括iframe和动态内容`);
            } else {
                throw new Error(response?.error || '激进扫描失败');
            }

        } catch (error) {
            console.error('Aggressive scan error:', error);
            this.showError('激进扫描失败: ' + error.message);
            this.updateStatus('激进扫描失败', 'error');
        }
    }

    /**
     * 高亮显示字段
     */
    async highlightFields() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持字段预览');
                return;
            }

            this.updateStatus('正在预览字段...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'highlightFields'
            });

            if (response && response.success) {
                this.updateStatus(`已识别 ${this.formFields?.length || 0} 个字段`, 'success');
                this.showSuccess(`字段预览已显示！\n\n🔵 蓝色边框 = 识别到的字段\n🏷️ 标签 = 字段名称\n\n预览将在8秒后自动消失`);

                // 关闭popup让用户看到高亮效果
                setTimeout(() => {
                    window.close();
                }, 1500);
            } else {
                throw new Error(response?.error || '字段预览失败');
            }

        } catch (error) {
            console.error('Highlight fields error:', error);
            this.showError('字段预览失败: ' + error.message);
            this.updateStatus('字段预览失败', 'error');
        }
    }

    /**
     * 清除字段高亮
     */
    async clearHighlights() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持清除高亮');
                return;
            }

            this.updateStatus('正在清除高亮...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'clearHighlights'
            });

            if (response && response.success) {
                this.updateStatus('高亮已清除', 'success');
                this.showSuccess('页面高亮已清除');
            } else {
                throw new Error(response?.error || '清除高亮失败');
            }

        } catch (error) {
            console.error('Clear highlights error:', error);
            this.showError('清除高亮失败: ' + error.message);
            this.updateStatus('清除高亮失败', 'error');
        }
    }

    /**
     * 生成字段报告
     */
    async generateFieldReport() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持字段报告');
                return;
            }

            this.updateStatus('正在生成字段报告...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'generateFieldReport'
            });

            if (response && response.success) {
                // 打开字段报告窗口
                const reportWindow = window.open('', '_blank', 'width=1000,height=700');
                reportWindow.document.write(`
                    <html>
                        <head>
                            <title>📋 字段识别报告</title>
                            <style>
                                body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
                                .header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                                .summary { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                                .field-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
                                .field-card { background: white; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #007bff; }
                                .field-card.shipper { border-left-color: #3498db; }
                                .field-card.consignee { border-left-color: #2ecc71; }
                                .field-card.port { border-left-color: #f39c12; }
                                .field-card.goods { border-left-color: #9b59b6; }
                                .field-card.unknown { border-left-color: #95a5a6; }
                                .field-title { font-weight: bold; color: #2c3e50; margin-bottom: 8px; }
                                .field-detail { color: #6c757d; font-size: 14px; margin: 4px 0; }
                                .confidence-bar { background: #e9ecef; height: 6px; border-radius: 3px; margin: 8px 0; }
                                .confidence-fill { height: 100%; border-radius: 3px; transition: width 0.3s; }
                                .high-confidence { background: #28a745; }
                                .medium-confidence { background: #ffc107; }
                                .low-confidence { background: #dc3545; }
                                .stats { display: flex; justify-content: space-around; text-align: center; }
                                .stat-item { padding: 10px; }
                                .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
                                .stat-label { color: #6c757d; font-size: 14px; }
                            </style>
                        </head>
                        <body>
                            <div class="header">
                                <h2>📋 字段识别报告</h2>
                                <p>页面: ${response.pageTitle}</p>
                                <p>URL: ${response.pageUrl}</p>
                                <p>扫描时间: ${new Date().toLocaleString()}</p>
                            </div>

                            <div class="summary">
                                <div class="stats">
                                    <div class="stat-item">
                                        <div class="stat-number">${response.totalFields}</div>
                                        <div class="stat-label">总字段数</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">${response.identifiedFields}</div>
                                        <div class="stat-label">已识别字段</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">${response.highConfidenceFields}</div>
                                        <div class="stat-label">高置信度字段</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">${Math.round(response.averageConfidence)}%</div>
                                        <div class="stat-label">平均置信度</div>
                                    </div>
                                </div>
                            </div>

                            <div class="field-grid">
                                ${response.fields.map((field, index) => {
                                    const confidencePercent = Math.round(field.confidence * 100);
                                    const confidenceClass = confidencePercent >= 70 ? 'high-confidence' :
                                                          confidencePercent >= 40 ? 'medium-confidence' : 'low-confidence';
                                    const cardClass = this.getFieldCardClass(field.fieldType);

                                    return `
                                        <div class="field-card ${cardClass}">
                                            <div class="field-title">${index + 1}. ${field.displayName}</div>
                                            <div class="field-detail"><strong>类型:</strong> ${field.fieldType}</div>
                                            <div class="field-detail"><strong>元素:</strong> &lt;${field.element.tag}${field.element.id ? ' id="' + field.element.id + '"' : ''}${field.element.name ? ' name="' + field.element.name + '"' : ''}&gt;</div>
                                            <div class="field-detail"><strong>位置:</strong> (${Math.round(field.coordinates.x)}, ${Math.round(field.coordinates.y)})</div>
                                            <div class="field-detail"><strong>尺寸:</strong> ${Math.round(field.coordinates.width)} × ${Math.round(field.coordinates.height)}</div>
                                            ${field.label !== 'Unknown' ? `<div class="field-detail"><strong>标签:</strong> ${field.label}</div>` : ''}
                                            <div class="confidence-bar">
                                                <div class="confidence-fill ${confidenceClass}" style="width: ${confidencePercent}%"></div>
                                            </div>
                                            <div class="field-detail">置信度: ${confidencePercent}%</div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </body>
                    </html>
                `);

                this.updateStatus('字段报告生成完成', 'success');

            } else {
                throw new Error(response?.error || '字段报告生成失败');
            }

        } catch (error) {
            console.error('Field report error:', error);
            this.showError('字段报告生成失败: ' + error.message);
            this.updateStatus('字段报告生成失败', 'error');
        }
    }

    getFieldCardClass(fieldType) {
        const typeMap = {
            'SHIPPER': 'shipper',
            'CONSIGNEE': 'consignee',
            'PORT_OF_LOADING': 'port',
            'PORT_OF_DISCHARGE': 'port',
            'VESSEL_VOYAGE': 'goods',
            'CONTAINER_NO': 'goods',
            'GOODS': 'goods',
            'UNKNOWN': 'unknown'
        };
        return typeMap[fieldType] || 'unknown';
    }

    /**
     * 调试字段识别
     */
    async debugFields() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持字段调试');
                return;
            }

            // 发送调试消息
            try {
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'debugFields'
                });

                if (response && response.success) {
                    // 打开新窗口显示调试信息
                    const debugWindow = window.open('', '_blank', 'width=800,height=600');
                    debugWindow.document.write(`
                        <html>
                            <head>
                                <title>字段调试信息</title>
                                <style>
                                    body { font-family: monospace; padding: 20px; }
                                    .field-item {
                                        border: 1px solid #ddd;
                                        margin: 10px 0;
                                        padding: 10px;
                                        border-radius: 5px;
                                    }
                                    .field-label { font-weight: bold; color: #007bff; }
                                    .field-details { margin-top: 5px; font-size: 12px; color: #666; }
                                    .highlight-btn {
                                        background: #28a745;
                                        color: white;
                                        border: none;
                                        padding: 5px 10px;
                                        border-radius: 3px;
                                        cursor: pointer;
                                        margin-top: 5px;
                                    }
                                </style>
                            </head>
                            <body>
                                <h2>🐛 字段调试信息</h2>
                                <p>找到 <strong>${response.fields.length}</strong> 个字段：</p>
                                <div id="fields"></div>
                            </body>
                        </html>
                    `);

                    const fieldsDiv = debugWindow.document.getElementById('fields');
                    response.fields.forEach((field, index) => {
                        const fieldDiv = debugWindow.document.createElement('div');
                        fieldDiv.className = 'field-item';
                        fieldDiv.innerHTML = `
                            <div class="field-label">${index + 1}. ${field.label || field.name || field.id || '未知字段'}</div>
                            <div class="field-details">
                                <strong>标签:</strong> ${field.tagName}<br>
                                <strong>类型:</strong> ${field.type}<br>
                                <strong>ID:</strong> ${field.id}<br>
                                <strong>Name:</strong> ${field.name}<br>
                                <strong>Class:</strong> ${field.className}<br>
                                <strong>占位符:</strong> ${field.placeholder}<br>
                                <strong>当前值:</strong> ${field.value}<br>
                                <strong>匹配文本:</strong> ${field.textContent}
                            </div>
                            <button class="highlight-btn" onclick="highlightField(${index})">高亮此字段</button>
                        `;
                        fieldsDiv.appendChild(fieldDiv);
                    });

                    // 添加高亮功能
                    debugWindow.highlightField = async (index) => {
                        try {
                            await chrome.tabs.sendMessage(tab.id, {
                                action: 'highlightField',
                                fieldIndex: index
                            });
                        } catch (error) {
                            console.error('Failed to highlight field:', error);
                        }
                    };
                } else {
                    throw new Error('调试信息获取失败');
                }
            } catch (messageError) {
                console.log('Content script not ready for debug, injecting...');
                await this.injectContentScript(tab.id);

                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'debugFields'
                        });
                        // 处理响应...
                    } catch (retryError) {
                        this.showError('调试失败: ' + retryError.message);
                    }
                }, 500);
            }

        } catch (error) {
            console.error('Debug error:', error);
            this.showError('调试失败: ' + error.message);
        }
    }

    /**
     * 注入Content Script到指定标签页
     */
    async injectContentScript(tabId) {
        try {
            // 注入CSS
            await chrome.scripting.insertCSS({
                target: { tabId: tabId },
                files: ['content.css']
            });

            // 注入JavaScript
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            });

            console.log('Content script injected successfully');
        } catch (error) {
            console.error('Failed to inject content script:', error);
            throw error;
        }
    }

    /**
     * 检查Content Script是否已加载
     */
    async isContentScriptReady(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'ping'
            });
            return response && response.success;
        } catch (error) {
            return false;
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new FreightParserPopup();
});
