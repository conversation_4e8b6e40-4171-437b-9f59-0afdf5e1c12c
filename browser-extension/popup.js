/**
 * 货代单证智能解析浏览器扩展 - Popup脚本
 */

class FreightParserPopup {
    constructor() {
        this.selectedFile = null;
        this.parseResult = null;
        this.apiBaseUrl = 'http://localhost:8080/api';
        
        this.init();
    }

    async init() {
        // 绑定事件
        this.bindEvents();

        // 加载设置
        await this.loadSettings();

        // 延迟扫描当前页面，避免初始化时的连接错误
        setTimeout(() => {
            this.scanCurrentPage();
        }, 1000);

        console.log('Popup initialized');
    }

    bindEvents() {
        // 文件上传
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            if (file) this.handleFileSelect(file);
        });

        // 按钮事件
        document.getElementById('parseBtn').addEventListener('click', () => this.parseDocument());
        document.getElementById('fillBtn').addEventListener('click', () => this.fillForm());
        document.getElementById('scanPageBtn').addEventListener('click', () => this.scanCurrentPage());
        document.getElementById('refreshPageBtn').addEventListener('click', () => this.refreshCurrentPage());
        document.getElementById('debugFieldsBtn').addEventListener('click', () => this.debugFields());
        document.getElementById('analyzePageBtn').addEventListener('click', () => this.analyzePage());
        document.getElementById('forceScreenshotBtn').addEventListener('click', () => this.forceScreenshotDetection());
        document.getElementById('viewResultBtn').addEventListener('click', () => this.viewResult());
        
        // 链接事件
        document.getElementById('settingsLink').addEventListener('click', () => this.openSettings());
        document.getElementById('helpLink').addEventListener('click', () => this.openHelp());
    }

    handleFileSelect(file) {
        if (!file) return;
        
        // 验证文件
        if (!this.validateFile(file)) return;
        
        this.selectedFile = file;
        
        // 更新UI
        const uploadArea = document.getElementById('uploadArea');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'block';
        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        
        // 启用解析按钮
        document.getElementById('parseBtn').disabled = false;
        
        this.updateStatus('文件已选择', 'ready');
    }

    validateFile(file) {
        const maxSize = 20 * 1024 * 1024; // 20MB
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        
        if (file.size > maxSize) {
            this.showError('文件大小不能超过20MB');
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            this.showError('只支持PDF、JPG、PNG格式文件');
            return false;
        }
        
        return true;
    }

    formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }

    async parseDocument() {
        if (!this.selectedFile) {
            this.showError('请先选择文件');
            return;
        }

        try {
            this.showProgress('正在上传文件...', 10);
            this.updateStatus('解析中...', 'processing');

            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('documentType', document.getElementById('documentType').value);
            formData.append('clientId', 'browser_extension');
            formData.append('userId', 'extension_user');
            formData.append('enableOcr', 'true');
            formData.append('ocrLanguage', 'chi_sim+eng');

            this.showProgress('正在AI解析...', 50);

            const response = await fetch(`${this.apiBaseUrl}/document/parse`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            this.showProgress('解析完成', 100);

            if (result.success) {
                this.parseResult = result.data;
                this.showResult(result.data);
                document.getElementById('fillBtn').disabled = false;
                this.updateStatus('解析成功', 'success');
            } else {
                throw new Error(result.message || '解析失败');
            }

        } catch (error) {
            console.error('Parse error:', error);
            this.showError('解析失败: ' + error.message);
            this.updateStatus('解析失败', 'error');
        } finally {
            this.hideProgress();
        }
    }

    async fillForm() {
        if (!this.parseResult || !this.parseResult.parsedData) {
            this.showError('没有可用的解析结果');
            return;
        }

        try {
            this.updateStatus('回填中...', 'processing');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // 检查是否是有效的页面
            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                throw new Error('当前页面不支持表单回填');
            }

            // 尝试发送消息，如果失败则注入Content Script
            try {
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'fillForm',
                    data: this.parseResult.parsedData
                });

                if (response && response.success) {
                    this.updateStatus(`成功回填 ${response.filledCount} 个字段`, 'success');
                    this.showSuccess(`成功回填 ${response.filledCount} 个字段`);
                } else {
                    throw new Error(response?.error || '回填失败');
                }
            } catch (messageError) {
                console.log('Content script not ready for fill, injecting...');

                // 注入Content Script
                await this.injectContentScript(tab.id);

                // 等待一下再重试
                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'fillForm',
                            data: this.parseResult.parsedData
                        });

                        if (response && response.success) {
                            this.updateStatus(`成功回填 ${response.filledCount} 个字段`, 'success');
                            this.showSuccess(`成功回填 ${response.filledCount} 个字段`);
                        } else {
                            throw new Error(response?.error || '回填失败');
                        }
                    } catch (retryError) {
                        console.error('Retry fill failed:', retryError);
                        this.showError('回填失败: ' + retryError.message);
                        this.updateStatus('回填失败', 'error');
                    }
                }, 500);
            }

        } catch (error) {
            console.error('Fill error:', error);
            this.showError('回填失败: ' + error.message);
            this.updateStatus('回填失败', 'error');
        }
    }

    async scanCurrentPage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // 检查是否是有效的页面
            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                document.getElementById('formCount').textContent = '0';
                document.getElementById('fieldCount2').textContent = '0';
                return;
            }

            // 尝试发送消息，如果失败则注入Content Script
            try {
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'scanPage'
                });

                if (response) {
                    document.getElementById('formCount').textContent = response.formCount || 0;
                    document.getElementById('fieldCount2').textContent = response.fieldCount || 0;

                    // 显示检测方法
                    if (response.method) {
                        console.log('Detection method:', response.method);
                        // 可以在UI中显示检测方法
                        const statusElement = document.getElementById('status');
                        if (statusElement) {
                            const methodText = {
                                'vision': '视觉检测',
                                'ml': '机器学习',
                                'dom': 'DOM解析'
                            };
                            statusElement.title = `使用${methodText[response.method] || response.method}检测`;
                        }
                    }
                } else {
                    throw new Error('No response from content script');
                }
            } catch (messageError) {
                console.log('Content script not ready, injecting...');

                // 注入Content Script
                await this.injectContentScript(tab.id);

                // 等待一下再重试
                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'scanPage'
                        });

                        if (response) {
                            document.getElementById('formCount').textContent = response.formCount || 0;
                            document.getElementById('fieldCount2').textContent = response.fieldCount || 0;
                        }
                    } catch (retryError) {
                        console.error('Retry scan failed:', retryError);
                        document.getElementById('formCount').textContent = '?';
                        document.getElementById('fieldCount2').textContent = '?';
                    }
                }, 500);
            }

        } catch (error) {
            console.error('Scan error:', error);
            document.getElementById('formCount').textContent = '?';
            document.getElementById('fieldCount2').textContent = '?';
        }
    }

    showProgress(text, percent = 0) {
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        progressSection.style.display = 'block';
        progressFill.style.width = percent + '%';
        progressText.textContent = text;
    }

    hideProgress() {
        setTimeout(() => {
            document.getElementById('progressSection').style.display = 'none';
        }, 1000);
    }

    showResult(data) {
        const resultSection = document.getElementById('resultSection');
        const fieldCount = document.getElementById('fieldCount');
        const confidence = document.getElementById('confidence');
        
        resultSection.style.display = 'block';
        fieldCount.textContent = Object.keys(data.parsedData || {}).length;
        confidence.textContent = (data.confidenceScore || 0) + '%';
    }

    viewResult() {
        if (!this.parseResult) return;
        
        // 创建新窗口显示详细结果
        const resultWindow = window.open('', '_blank', 'width=600,height=400');
        resultWindow.document.write(`
            <html>
                <head>
                    <title>解析结果详情</title>
                    <style>
                        body { font-family: monospace; padding: 20px; }
                        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <h2>解析结果详情</h2>
                    <pre>${JSON.stringify(this.parseResult, null, 2)}</pre>
                </body>
            </html>
        `);
    }

    updateStatus(text, type = 'ready') {
        const statusText = document.querySelector('.status-text');
        const statusDot = document.querySelector('.status-dot');
        
        statusText.textContent = text;
        
        // 移除所有状态类
        statusDot.className = 'status-dot';
        
        // 添加对应状态类
        switch (type) {
            case 'processing':
                statusDot.style.background = '#ffc107';
                break;
            case 'success':
                statusDot.style.background = '#28a745';
                break;
            case 'error':
                statusDot.style.background = '#dc3545';
                break;
            default:
                statusDot.style.background = '#007bff';
        }
    }

    showError(message) {
        console.error('FreightParser Error:', message);

        // 更友好的错误提示
        if (message.includes('Could not establish connection')) {
            message = '页面连接失败，请刷新页面后重试';
        } else if (message.includes('Extension context invalidated')) {
            message = '扩展已更新，请刷新页面后重试';
        }

        // 可以后续改为更好的UI
        alert('错误: ' + message);
    }

    showSuccess(message) {
        console.log('FreightParser Success:', message);
        // 简单的成功提示，可以后续改为更好的UI
        alert('成功: ' + message);
    }

    async loadSettings() {
        try {
            const settings = await chrome.storage.sync.get(['apiBaseUrl']);
            if (settings.apiBaseUrl) {
                this.apiBaseUrl = settings.apiBaseUrl;
            }
        } catch (error) {
            console.error('Load settings error:', error);
        }
    }

    openSettings() {
        chrome.runtime.openOptionsPage();
    }

    openHelp() {
        chrome.tabs.create({
            url: 'https://github.com/your-repo/freight-parser-extension'
        });
    }

    /**
     * 刷新当前页面
     */
    async refreshCurrentPage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.reload(tab.id);

            // 关闭popup
            window.close();
        } catch (error) {
            console.error('Failed to refresh page:', error);
            this.showError('刷新页面失败');
        }
    }

    /**
     * 分析页面
     */
    async analyzePage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持分析');
                return;
            }

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'analyzePageElements'
            });

            if (response && response.success) {
                // 打开分析结果窗口
                const analysisWindow = window.open('', '_blank', 'width=900,height=700');
                analysisWindow.document.write(`
                    <html>
                        <head>
                            <title>页面元素分析</title>
                            <style>
                                body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                                .section { background: white; margin: 15px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                                .section h3 { color: #007bff; margin-top: 0; }
                                .stat-item { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
                                .stat-item:last-child { border-bottom: none; }
                                .count { font-weight: bold; color: #28a745; }
                                .framework { display: inline-block; background: #007bff; color: white; padding: 3px 8px; margin: 2px; border-radius: 12px; font-size: 12px; }
                                .element-sample { background: #f8f9fa; padding: 8px; margin: 5px 0; border-radius: 4px; font-size: 12px; }
                                .visible { color: #28a745; }
                                .hidden { color: #dc3545; }
                            </style>
                        </head>
                        <body>
                            <h2>🔍 页面元素分析报告</h2>
                            <div class="section">
                                <h3>📊 基本信息</h3>
                                <div class="stat-item"><span>页面标题:</span><span>${response.analysis.pageInfo.title}</span></div>
                                <div class="stat-item"><span>域名:</span><span>${response.analysis.pageInfo.domain}</span></div>
                                <div class="stat-item"><span>总元素数:</span><span class="count">${response.analysis.totalElements}</span></div>
                            </div>
                            <div class="section">
                                <h3>🛠️ 检测到的框架</h3>
                                ${response.analysis.frameworks.length > 0 ?
                                    response.analysis.frameworks.map(fw => `<span class="framework">${fw}</span>`).join('') :
                                    '<p>未检测到已知框架</p>'
                                }
                            </div>
                            <div class="section">
                                <h3>📝 输入元素统计</h3>
                                <div id="inputStats"></div>
                            </div>
                        </body>
                    </html>
                `);

                const inputStatsDiv = analysisWindow.document.getElementById('inputStats');
                Object.entries(response.analysis.inputElements).forEach(([type, data]) => {
                    if (data.error) {
                        inputStatsDiv.innerHTML += `<div class="stat-item"><span>${type}:</span><span style="color: red;">错误: ${data.error}</span></div>`;
                    } else {
                        inputStatsDiv.innerHTML += `
                            <div class="stat-item">
                                <span>${type}:</span>
                                <span class="count">${data.count}</span>
                            </div>
                            ${data.elements && data.elements.length > 0 ?
                                data.elements.map(el => `
                                    <div class="element-sample">
                                        &lt;${el.tag}${el.id ? ' id="' + el.id + '"' : ''}${el.name ? ' name="' + el.name + '"' : ''}&gt;
                                        <span class="${el.visible ? 'visible' : 'hidden'}">${el.visible ? '👁️ 可见' : '🙈 隐藏'}</span>
                                    </div>
                                `).join('') : ''
                            }
                        `;
                    }
                });

            } else {
                this.showError('页面分析失败');
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showError('分析失败: ' + error.message);
        }
    }

    /**
     * 强制使用截图检测
     */
    async forceScreenshotDetection() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持截图检测');
                return;
            }

            this.updateStatus('正在截图检测...', 'processing');

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'forceScreenshotDetection'
            });

            if (response && response.success) {
                document.getElementById('formCount').textContent = response.formCount || 0;
                document.getElementById('fieldCount2').textContent = response.fieldCount || 0;
                this.updateStatus(`截图检测完成，找到 ${response.fieldCount} 个字段`, 'success');
                this.showSuccess(`截图检测找到 ${response.fieldCount} 个字段`);
            } else {
                throw new Error(response?.error || '截图检测失败');
            }

        } catch (error) {
            console.error('Screenshot detection error:', error);
            this.showError('截图检测失败: ' + error.message);
            this.updateStatus('截图检测失败', 'error');
        }
    }

    /**
     * 调试字段识别
     */
    async debugFields() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('当前页面不支持字段调试');
                return;
            }

            // 发送调试消息
            try {
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'debugFields'
                });

                if (response && response.success) {
                    // 打开新窗口显示调试信息
                    const debugWindow = window.open('', '_blank', 'width=800,height=600');
                    debugWindow.document.write(`
                        <html>
                            <head>
                                <title>字段调试信息</title>
                                <style>
                                    body { font-family: monospace; padding: 20px; }
                                    .field-item {
                                        border: 1px solid #ddd;
                                        margin: 10px 0;
                                        padding: 10px;
                                        border-radius: 5px;
                                    }
                                    .field-label { font-weight: bold; color: #007bff; }
                                    .field-details { margin-top: 5px; font-size: 12px; color: #666; }
                                    .highlight-btn {
                                        background: #28a745;
                                        color: white;
                                        border: none;
                                        padding: 5px 10px;
                                        border-radius: 3px;
                                        cursor: pointer;
                                        margin-top: 5px;
                                    }
                                </style>
                            </head>
                            <body>
                                <h2>🐛 字段调试信息</h2>
                                <p>找到 <strong>${response.fields.length}</strong> 个字段：</p>
                                <div id="fields"></div>
                            </body>
                        </html>
                    `);

                    const fieldsDiv = debugWindow.document.getElementById('fields');
                    response.fields.forEach((field, index) => {
                        const fieldDiv = debugWindow.document.createElement('div');
                        fieldDiv.className = 'field-item';
                        fieldDiv.innerHTML = `
                            <div class="field-label">${index + 1}. ${field.label || field.name || field.id || '未知字段'}</div>
                            <div class="field-details">
                                <strong>标签:</strong> ${field.tagName}<br>
                                <strong>类型:</strong> ${field.type}<br>
                                <strong>ID:</strong> ${field.id}<br>
                                <strong>Name:</strong> ${field.name}<br>
                                <strong>Class:</strong> ${field.className}<br>
                                <strong>占位符:</strong> ${field.placeholder}<br>
                                <strong>当前值:</strong> ${field.value}<br>
                                <strong>匹配文本:</strong> ${field.textContent}
                            </div>
                            <button class="highlight-btn" onclick="highlightField(${index})">高亮此字段</button>
                        `;
                        fieldsDiv.appendChild(fieldDiv);
                    });

                    // 添加高亮功能
                    debugWindow.highlightField = async (index) => {
                        try {
                            await chrome.tabs.sendMessage(tab.id, {
                                action: 'highlightField',
                                fieldIndex: index
                            });
                        } catch (error) {
                            console.error('Failed to highlight field:', error);
                        }
                    };
                } else {
                    throw new Error('调试信息获取失败');
                }
            } catch (messageError) {
                console.log('Content script not ready for debug, injecting...');
                await this.injectContentScript(tab.id);

                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'debugFields'
                        });
                        // 处理响应...
                    } catch (retryError) {
                        this.showError('调试失败: ' + retryError.message);
                    }
                }, 500);
            }

        } catch (error) {
            console.error('Debug error:', error);
            this.showError('调试失败: ' + error.message);
        }
    }

    /**
     * 注入Content Script到指定标签页
     */
    async injectContentScript(tabId) {
        try {
            // 注入CSS
            await chrome.scripting.insertCSS({
                target: { tabId: tabId },
                files: ['content.css']
            });

            // 注入JavaScript
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            });

            console.log('Content script injected successfully');
        } catch (error) {
            console.error('Failed to inject content script:', error);
            throw error;
        }
    }

    /**
     * 检查Content Script是否已加载
     */
    async isContentScriptReady(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'ping'
            });
            return response && response.success;
        } catch (error) {
            return false;
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new FreightParserPopup();
});
