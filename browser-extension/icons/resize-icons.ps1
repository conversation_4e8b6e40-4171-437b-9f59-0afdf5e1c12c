# PowerShell脚本：批量生成不同尺寸的图标
# 使用方法：将原始PNG图片命名为 original.png，放在同一目录下，然后运行此脚本

param(
    [string]$InputFile = "original.png"
)

# 检查输入文件是否存在
if (-not (Test-Path $InputFile)) {
    Write-Host "错误：找不到输入文件 $InputFile" -ForegroundColor Red
    Write-Host "请将原始PNG图片重命名为 'original.png' 并放在此目录下" -ForegroundColor Yellow
    exit 1
}

# 定义目标尺寸
$sizes = @(
    @{Size = 16; Name = "icon16.png"},
    @{Size = 32; Name = "icon32.png"},
    @{Size = 48; Name = "icon48.png"},
    @{Size = 128; Name = "icon128.png"}
)

Write-Host "开始处理图标文件..." -ForegroundColor Green

# 加载System.Drawing程序集
Add-Type -AssemblyName System.Drawing

try {
    # 加载原始图片
    $originalImage = [System.Drawing.Image]::FromFile((Resolve-Path $InputFile).Path)
    Write-Host "原始图片尺寸: $($originalImage.Width)x$($originalImage.Height)" -ForegroundColor Cyan
    
    foreach ($sizeInfo in $sizes) {
        $size = $sizeInfo.Size
        $outputName = $sizeInfo.Name
        
        Write-Host "生成 ${size}x${size} 图标: $outputName" -ForegroundColor Yellow
        
        # 创建新的位图
        $resizedImage = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($resizedImage)
        
        # 设置高质量缩放
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        
        # 绘制缩放后的图片
        $graphics.DrawImage($originalImage, 0, 0, $size, $size)
        
        # 保存图片
        $resizedImage.Save($outputName, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # 清理资源
        $graphics.Dispose()
        $resizedImage.Dispose()
        
        Write-Host "✓ 已生成: $outputName" -ForegroundColor Green
    }
    
    # 清理原始图片资源
    $originalImage.Dispose()
    
    Write-Host "`n所有图标生成完成！" -ForegroundColor Green
    Write-Host "生成的文件：" -ForegroundColor Cyan
    foreach ($sizeInfo in $sizes) {
        if (Test-Path $sizeInfo.Name) {
            $fileSize = (Get-Item $sizeInfo.Name).Length
            Write-Host "  $($sizeInfo.Name) - $fileSize 字节" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "错误：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n使用说明：" -ForegroundColor Yellow
Write-Host "1. 将生成的图标文件放在扩展的icons目录下" -ForegroundColor White
Write-Host "2. 确保文件名正确：icon16.png, icon32.png, icon48.png, icon128.png" -ForegroundColor White
Write-Host "3. 重新加载浏览器扩展以应用新图标" -ForegroundColor White
