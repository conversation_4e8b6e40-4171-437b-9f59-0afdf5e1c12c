# 扩展图标说明

已创建了SVG格式的图标文件 `icon.svg`，你需要将其转换为以下尺寸的PNG图标：

## 图标尺寸
- icon16.png (16x16)
- icon32.png (32x32)
- icon48.png (48x48)
- icon128.png (128x128)

## SVG转PNG方法

### 方法一：在线转换工具
1. **Convertio** - https://convertio.co/svg-png/
2. **CloudConvert** - https://cloudconvert.com/svg-to-png
3. **Online-Convert** - https://image.online-convert.com/convert-to-png

### 方法二：使用Inkscape（推荐）
```bash
# 安装Inkscape
# Windows: 下载安装包 https://inkscape.org/
# macOS: brew install inkscape
# Linux: sudo apt install inkscape

# 转换命令
inkscape icon.svg --export-png=icon16.png --export-width=16 --export-height=16
inkscape icon.svg --export-png=icon32.png --export-width=32 --export-height=32
inkscape icon.svg --export-png=icon48.png --export-width=48 --export-height=48
inkscape icon.svg --export-png=icon128.png --export-width=128 --export-height=128
```

### 方法三：使用ImageMagick
```bash
# 安装ImageMagick
# Windows: 下载安装包 https://imagemagick.org/
# macOS: brew install imagemagick
# Linux: sudo apt install imagemagick

# 转换命令
convert icon.svg -resize 16x16 icon16.png
convert icon.svg -resize 32x32 icon32.png
convert icon.svg -resize 48x48 icon48.png
convert icon.svg -resize 128x128 icon128.png
```

### 方法四：批量转换脚本
创建 `convert-icons.sh` 脚本：
```bash
#!/bin/bash
sizes=(16 32 48 128)
for size in "${sizes[@]}"; do
    inkscape icon.svg --export-png=icon${size}.png --export-width=${size} --export-height=${size}
    echo "Generated icon${size}.png"
done
```

## 设计建议

### 主图标设计
- **主色调**：蓝色 (#007bff)
- **辅助色**：白色、灰色
- **图案**：船舶 🚢 + 文档 📄 的组合
- **风格**：现代、简洁、专业

### 具体设计元素
1. **背景**：圆角矩形，蓝色渐变
2. **主图案**：白色的船舶轮廓
3. **文档元素**：右下角小的文档图标
4. **可选元素**：AI/智能的象征（如小齿轮或闪电）

### 在线图标生成工具推荐
1. **Canva** - https://www.canva.com/
2. **Figma** - https://www.figma.com/
3. **GIMP** - 免费的图像编辑软件
4. **Photoshop** - 专业图像编辑软件

### 临时解决方案
如果暂时没有图标，可以使用以下方式：
1. 从网上下载免费的船舶/物流相关图标
2. 使用emoji转图标的在线工具
3. 使用简单的几何图形组合

### 图标文件放置
将生成的图标文件放在 `browser-extension/icons/` 目录下：
```
browser-extension/
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
```

### 注意事项
- 确保图标在不同尺寸下都清晰可见
- 使用PNG格式，支持透明背景
- 遵循Chrome扩展的图标设计规范
- 考虑深色主题下的显示效果
