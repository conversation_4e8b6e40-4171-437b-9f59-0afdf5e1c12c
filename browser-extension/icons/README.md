# 扩展图标说明

由于无法直接创建图片文件，这里提供图标的设计说明。你需要创建以下尺寸的PNG图标：

## 图标尺寸
- icon16.png (16x16)
- icon32.png (32x32) 
- icon48.png (48x48)
- icon128.png (128x128)

## 设计建议

### 主图标设计
- **主色调**：蓝色 (#007bff)
- **辅助色**：白色、灰色
- **图案**：船舶 🚢 + 文档 📄 的组合
- **风格**：现代、简洁、专业

### 具体设计元素
1. **背景**：圆角矩形，蓝色渐变
2. **主图案**：白色的船舶轮廓
3. **文档元素**：右下角小的文档图标
4. **可选元素**：AI/智能的象征（如小齿轮或闪电）

### 在线图标生成工具推荐
1. **Canva** - https://www.canva.com/
2. **Figma** - https://www.figma.com/
3. **GIMP** - 免费的图像编辑软件
4. **Photoshop** - 专业图像编辑软件

### 临时解决方案
如果暂时没有图标，可以使用以下方式：
1. 从网上下载免费的船舶/物流相关图标
2. 使用emoji转图标的在线工具
3. 使用简单的几何图形组合

### 图标文件放置
将生成的图标文件放在 `browser-extension/icons/` 目录下：
```
browser-extension/
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
```

### 注意事项
- 确保图标在不同尺寸下都清晰可见
- 使用PNG格式，支持透明背景
- 遵循Chrome扩展的图标设计规范
- 考虑深色主题下的显示效果
