/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    min-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 主内容区 */
.main-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 上传区域 */
.upload-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e0e0e0;
}

.upload-area {
    border: 2px dashed #007bff;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #0056b3;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.upload-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.upload-text div {
    font-weight: 500;
    margin-bottom: 4px;
}

.upload-text small {
    color: #666;
    font-size: 12px;
}

.file-info {
    margin-top: 12px;
    padding: 8px 12px;
    background: #e8f5e8;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.file-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: #666;
}

/* 选项区域 */
.options-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e0e0e0;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.option-group label {
    font-weight: 500;
    color: #555;
}

.option-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

/* 按钮样式 */
.actions {
    display: flex;
    gap: 8px;
}

.btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-icon {
    font-size: 16px;
}

/* 进度条 */
.progress-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e0e0e0;
}

.progress-bar {
    background: #e9ecef;
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    background: linear-gradient(90deg, #007bff, #0056b3);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
}

/* 结果区域 */
.result-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-left: 4px solid #28a745;
}

.result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.result-icon {
    font-size: 18px;
}

.result-title {
    font-weight: 600;
    color: #28a745;
}

.result-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.result-actions {
    text-align: center;
}

/* 页面信息 */
.page-info {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.page-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.stat-icon {
    font-size: 14px;
}

.stat-text {
    color: #666;
}

/* 底部 */
.footer {
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.links {
    display: flex;
    gap: 16px;
}

.links a {
    color: #666;
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s ease;
}

.links a:hover {
    color: #007bff;
}

.version {
    font-size: 11px;
    color: #999;
}

/* 响应式 */
@media (max-width: 400px) {
    body {
        width: 320px;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .actions {
        flex-direction: column;
    }
}
