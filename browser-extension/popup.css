/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 400px;
    min-height: 520px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #2c3e50;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    margin: 0;
    padding: 0;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.logo-text h1 {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.5px;
}

.logo-text .version {
    font-size: 11px;
    opacity: 0.8;
    font-weight: 400;
}

.status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 主内容区 */
.main-content {
    flex: 1;
    padding: 24px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 上传区域 */
.upload-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.upload-area {
    border: 2px dashed #007bff;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #0056b3;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.upload-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.upload-text div {
    font-weight: 500;
    margin-bottom: 4px;
}

.upload-text small {
    color: #666;
    font-size: 12px;
}

.file-info {
    margin-top: 12px;
    padding: 8px 12px;
    background: #e8f5e8;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.file-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: #666;
}

/* 选项区域 */
.options-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.option-group label {
    font-weight: 500;
    color: #555;
}

.option-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

/* 按钮样式 */
.actions {
    display: flex;
    gap: 8px;
}

.btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-outline {
    background: rgba(255, 255, 255, 0.8);
    color: #007bff;
    border: 1px solid rgba(0, 123, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
    transform: translateY(-1px);
}

.btn-small {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-icon {
    font-size: 16px;
}

/* 进度条 */
.progress-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.progress-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 10px;
    overflow: hidden;
    margin-bottom: 12px;
    position: relative;
}

.progress-fill {
    background: linear-gradient(90deg, #007bff, #0056b3, #007bff);
    background-size: 200% 100%;
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.progress-text {
    text-align: center;
    font-size: 13px;
    color: #495057;
    font-weight: 500;
}

/* 结果区域 */
.result-section {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(40, 167, 69, 0.15);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-left: 4px solid #28a745;
}

.result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.result-icon {
    font-size: 18px;
}

.result-title {
    font-weight: 600;
    color: #28a745;
}

.result-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.result-actions {
    text-align: center;
}

/* 页面信息 */
.page-info {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.page-info-header {
    margin-bottom: 16px;
}

.page-info-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.page-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
    font-size: 24px;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #007bff;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.page-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 底部 */
.footer {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 16px 20px;
    border-radius: 16px 16px 0 0;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.footer-link {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.footer-link:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    transform: translateY(-1px);
}

.link-icon {
    font-size: 14px;
}

.link-text {
    font-weight: 500;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.main-content > * {
    animation: slideInUp 0.4s ease-out;
}

.main-content > *:nth-child(1) { animation-delay: 0.1s; }
.main-content > *:nth-child(2) { animation-delay: 0.2s; }
.main-content > *:nth-child(3) { animation-delay: 0.3s; }
.main-content > *:nth-child(4) { animation-delay: 0.4s; }

.header {
    animation: fadeIn 0.6s ease-out;
}

.footer {
    animation: slideInUp 0.5s ease-out 0.5s both;
}

/* 响应式 */
@media (max-width: 400px) {
    body {
        width: 320px;
    }

    .main-content {
        padding: 16px;
    }

    .actions {
        flex-direction: column;
    }

    .page-stats {
        grid-template-columns: 1fr;
    }

    .page-actions {
        flex-direction: column;
    }
}
