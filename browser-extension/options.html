<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货代单证智能解析助手 - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <img src="icons/icon48.png" alt="Logo">
                    <h1>货代单证智能解析助手</h1>
                </div>
                <div class="version">v1.0.0</div>
            </div>
        </header>

        <!-- 主内容 -->
        <main class="main-content">
            <!-- 导航标签 -->
            <nav class="tabs">
                <button class="tab-button active" data-tab="general">🔧 常规设置</button>
                <button class="tab-button" data-tab="api">🌐 API配置</button>
                <button class="tab-button" data-tab="advanced">⚙️ 高级选项</button>
                <button class="tab-button" data-tab="about">ℹ️ 关于</button>
            </nav>

            <!-- 常规设置 -->
            <section class="tab-content active" id="general">
                <div class="section">
                    <h2>基础配置</h2>
                    
                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="autoDetectDocumentType">
                            <span class="checkmark"></span>
                            自动检测文档类型
                        </label>
                        <p class="setting-description">启用后将自动识别上传文档的类型，无需手动选择</p>
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="enableOcr">
                            <span class="checkmark"></span>
                            启用OCR文字识别
                        </label>
                        <p class="setting-description">对图片格式的文档进行文字识别</p>
                    </div>

                    <div class="setting-group">
                        <label for="ocrLanguage">OCR识别语言：</label>
                        <select id="ocrLanguage" class="setting-select">
                            <option value="chi_sim+eng">中英文混合</option>
                            <option value="eng">英文</option>
                            <option value="chi_sim">简体中文</option>
                            <option value="chi_tra">繁体中文</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="enableAutoFill">
                            <span class="checkmark"></span>
                            启用智能回填
                        </label>
                        <p class="setting-description">解析完成后自动匹配并回填表单字段</p>
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="enableNotifications">
                            <span class="checkmark"></span>
                            启用通知提醒
                        </label>
                        <p class="setting-description">在解析完成或出现错误时显示桌面通知</p>
                    </div>
                </div>

                <div class="section">
                    <h2>界面设置</h2>
                    
                    <div class="setting-group">
                        <label for="theme">主题样式：</label>
                        <select id="theme" class="setting-select">
                            <option value="light">浅色主题</option>
                            <option value="dark">深色主题</option>
                            <option value="auto">跟随系统</option>
                        </select>
                    </div>
                </div>
            </section>

            <!-- API配置 -->
            <section class="tab-content" id="api">
                <div class="section">
                    <h2>API服务配置</h2>
                    
                    <div class="setting-group">
                        <label for="apiBaseUrl">API服务地址：</label>
                        <input type="url" id="apiBaseUrl" class="setting-input" placeholder="http://localhost:8080/api">
                        <p class="setting-description">货代解析服务的API地址</p>
                    </div>

                    <div class="setting-group">
                        <label for="apiTimeout">请求超时时间（秒）：</label>
                        <input type="number" id="apiTimeout" class="setting-input" min="10" max="300" value="30">
                    </div>

                    <div class="setting-group">
                        <label for="maxRetries">最大重试次数：</label>
                        <input type="number" id="maxRetries" class="setting-input" min="0" max="5" value="3">
                    </div>

                    <div class="setting-actions">
                        <button id="testConnection" class="btn btn-primary">
                            <span class="btn-icon">🔗</span>
                            测试连接
                        </button>
                        <div id="connectionStatus" class="connection-status"></div>
                    </div>
                </div>

                <div class="section">
                    <h2>认证设置</h2>
                    
                    <div class="setting-group">
                        <label for="clientId">客户端ID：</label>
                        <input type="text" id="clientId" class="setting-input" placeholder="browser_extension">
                    </div>

                    <div class="setting-group">
                        <label for="apiKey">API密钥：</label>
                        <input type="password" id="apiKey" class="setting-input" placeholder="可选，如果服务需要认证">
                    </div>
                </div>
            </section>

            <!-- 高级选项 -->
            <section class="tab-content" id="advanced">
                <div class="section">
                    <h2>字段映射</h2>
                    
                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="enableCustomMapping">
                            <span class="checkmark"></span>
                            启用自定义字段映射
                        </label>
                        <p class="setting-description">允许自定义字段名称和别名的映射关系</p>
                    </div>

                    <div class="setting-group">
                        <button id="editMappings" class="btn btn-outline">
                            <span class="btn-icon">📝</span>
                            编辑字段映射
                        </button>
                    </div>
                </div>

                <div class="section">
                    <h2>调试选项</h2>
                    
                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="enableDebugMode">
                            <span class="checkmark"></span>
                            启用调试模式
                        </label>
                        <p class="setting-description">在控制台输出详细的调试信息</p>
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">
                            <input type="checkbox" id="saveDebugLogs">
                            <span class="checkmark"></span>
                            保存调试日志
                        </label>
                        <p class="setting-description">将调试信息保存到本地存储</p>
                    </div>
                </div>

                <div class="section">
                    <h2>数据管理</h2>
                    
                    <div class="setting-actions">
                        <button id="exportSettings" class="btn btn-outline">
                            <span class="btn-icon">📤</span>
                            导出设置
                        </button>
                        <button id="importSettings" class="btn btn-outline">
                            <span class="btn-icon">📥</span>
                            导入设置
                        </button>
                        <button id="resetSettings" class="btn btn-danger">
                            <span class="btn-icon">🔄</span>
                            重置设置
                        </button>
                    </div>
                    <input type="file" id="importFile" accept=".json" style="display: none;">
                </div>
            </section>

            <!-- 关于 -->
            <section class="tab-content" id="about">
                <div class="section">
                    <h2>关于扩展</h2>
                    
                    <div class="about-info">
                        <div class="info-item">
                            <strong>版本：</strong> 1.0.0
                        </div>
                        <div class="info-item">
                            <strong>开发者：</strong> AI Assistant
                        </div>
                        <div class="info-item">
                            <strong>更新日期：</strong> 2024-01-15
                        </div>
                        <div class="info-item">
                            <strong>许可证：</strong> MIT License
                        </div>
                    </div>

                    <div class="description">
                        <p>货代单证智能解析助手是一个专为货代行业设计的浏览器扩展，通过AI技术实现单证的智能解析和表单自动回填，大幅提升工作效率。</p>
                    </div>

                    <div class="features">
                        <h3>主要功能：</h3>
                        <ul>
                            <li>🚢 支持多种货代单证类型（提单、发票、装箱单等）</li>
                            <li>🤖 AI智能解析，准确率高达90%以上</li>
                            <li>📝 智能表单回填，自动匹配字段</li>
                            <li>🔍 OCR文字识别，支持图片格式文档</li>
                            <li>⚡ 零配置使用，安装即可使用</li>
                            <li>🔒 数据安全，本地处理</li>
                        </ul>
                    </div>

                    <div class="links">
                        <a href="#" class="link-button" id="helpLink">
                            <span class="btn-icon">❓</span>
                            使用帮助
                        </a>
                        <a href="#" class="link-button" id="feedbackLink">
                            <span class="btn-icon">💬</span>
                            意见反馈
                        </a>
                        <a href="#" class="link-button" id="githubLink">
                            <span class="btn-icon">🐙</span>
                            GitHub
                        </a>
                    </div>
                </div>

                <div class="section">
                    <h2>使用统计</h2>
                    <div id="usageStats" class="usage-stats">
                        <div class="stat-item">
                            <span class="stat-label">总解析次数：</span>
                            <span class="stat-value" id="totalParses">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功回填次数：</span>
                            <span class="stat-value" id="totalFills">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">本月使用次数：</span>
                            <span class="stat-value" id="monthlyUsage">0</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部操作栏 -->
        <footer class="footer">
            <div class="footer-actions">
                <button id="saveSettings" class="btn btn-primary">
                    <span class="btn-icon">💾</span>
                    保存设置
                </button>
                <button id="cancelSettings" class="btn btn-outline">
                    <span class="btn-icon">❌</span>
                    取消
                </button>
            </div>
            <div class="save-status" id="saveStatus"></div>
        </footer>
    </div>

    <script src="options.js"></script>
</body>
</html>
