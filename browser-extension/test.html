<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货代单证智能解析扩展 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status-processing {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-success {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .test-results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .field-highlight {
            background-color: #fff3cd !important;
            border-color: #ffc107 !important;
            transition: all 0.3s ease;
        }
        
        .field-filled {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚢 货代单证智能解析扩展测试页面</h1>
        <p>这是一个专门用于测试浏览器扩展功能的页面，包含了各种货代业务表单</p>
    </div>

    <div class="instructions">
        <h3>📋 测试说明</h3>
        <ol>
            <li><strong>安装扩展</strong>：确保已正确安装货代单证智能解析扩展</li>
            <li><strong>启动后端</strong>：确保后端API服务正在运行（http://localhost:8080）</li>
            <li><strong>点击扩展图标</strong>：在浏览器工具栏中点击扩展图标</li>
            <li><strong>上传文档</strong>：选择货代单证文件进行解析</li>
            <li><strong>智能回填</strong>：解析完成后点击"智能回填"按钮</li>
            <li><strong>验证结果</strong>：检查表单字段是否正确填写</li>
        </ol>
    </div>

    <!-- 海运提单表单 -->
    <div class="test-section">
        <h2>🚢 海运提单 (Bill of Lading)</h2>
        <form id="blForm">
            <div class="form-grid">
                <div>
                    <div class="form-group">
                        <label for="blNo">提单号 *</label>
                        <input type="text" id="blNo" name="BL_NO" placeholder="请输入提单号">
                    </div>
                    <div class="form-group">
                        <label for="vesselVoyage">船名航次</label>
                        <input type="text" id="vesselVoyage" name="VESSEL_VOYAGE" placeholder="请输入船名航次">
                    </div>
                    <div class="form-group">
                        <label for="pol">起运港</label>
                        <input type="text" id="pol" name="PORT_OF_LOADING" placeholder="请输入起运港">
                    </div>
                    <div class="form-group">
                        <label for="pod">目的港</label>
                        <input type="text" id="pod" name="PORT_OF_DISCHARGE" placeholder="请输入目的港">
                    </div>
                </div>
                <div>
                    <div class="form-group">
                        <label for="shipper">发货人 *</label>
                        <textarea id="shipper" name="SHIPPER" placeholder="请输入发货人信息"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="consignee">收货人 *</label>
                        <textarea id="consignee" name="CONSIGNEE" placeholder="请输入收货人信息"></textarea>
                    </div>
                </div>
                <div>
                    <div class="form-group">
                        <label for="goodsDescription">货物描述</label>
                        <textarea id="goodsDescription" name="GOODS_DESCRIPTION" placeholder="请输入货物描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="containerNo">集装箱号</label>
                        <input type="text" id="containerNo" name="CONTAINER_NO" placeholder="请输入集装箱号">
                    </div>
                </div>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label for="packages">件数</label>
                    <input type="number" id="packages" name="PACKAGES" placeholder="件数">
                </div>
                <div class="form-group">
                    <label for="grossWeight">毛重 (KG)</label>
                    <input type="number" id="grossWeight" name="GROSS_WEIGHT" placeholder="毛重">
                </div>
                <div class="form-group">
                    <label for="measurement">体积 (CBM)</label>
                    <input type="number" step="0.01" id="measurement" name="MEASUREMENT" placeholder="体积">
                </div>
            </div>
            <button type="button" class="btn" onclick="clearForm('blForm')">清空表单</button>
            <button type="button" class="btn btn-success" onclick="highlightFields('blForm')">高亮字段</button>
        </form>
    </div>

    <!-- 商业发票表单 -->
    <div class="test-section">
        <h2>💰 商业发票 (Commercial Invoice)</h2>
        <form id="invoiceForm">
            <div class="form-grid">
                <div>
                    <div class="form-group">
                        <label for="invoiceNo">发票号 *</label>
                        <input type="text" id="invoiceNo" name="INVOICE_NO" placeholder="请输入发票号">
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">发票日期</label>
                        <input type="date" id="invoiceDate" name="INVOICE_DATE">
                    </div>
                    <div class="form-group">
                        <label for="totalAmount">总金额</label>
                        <input type="number" step="0.01" id="totalAmount" name="TOTAL_AMOUNT" placeholder="总金额">
                    </div>
                    <div class="form-group">
                        <label for="currency">币种</label>
                        <select id="currency" name="CURRENCY">
                            <option value="">请选择币种</option>
                            <option value="USD">美元 (USD)</option>
                            <option value="EUR">欧元 (EUR)</option>
                            <option value="CNY">人民币 (CNY)</option>
                            <option value="JPY">日元 (JPY)</option>
                        </select>
                    </div>
                </div>
                <div>
                    <div class="form-group">
                        <label for="seller">卖方</label>
                        <textarea id="seller" name="SELLER" placeholder="请输入卖方信息"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="buyer">买方</label>
                        <textarea id="buyer" name="BUYER" placeholder="请输入买方信息"></textarea>
                    </div>
                </div>
            </div>
            <button type="button" class="btn" onclick="clearForm('invoiceForm')">清空表单</button>
            <button type="button" class="btn btn-success" onclick="highlightFields('invoiceForm')">高亮字段</button>
        </form>
    </div>

    <!-- 装箱单表单 -->
    <div class="test-section">
        <h2>📦 装箱单 (Packing List)</h2>
        <form id="packingForm">
            <div class="form-grid">
                <div class="form-group">
                    <label for="packingListNo">装箱单号</label>
                    <input type="text" id="packingListNo" name="PACKING_LIST_NO" placeholder="请输入装箱单号">
                </div>
                <div class="form-group">
                    <label for="totalPackages">总件数</label>
                    <input type="number" id="totalPackages" name="TOTAL_PACKAGES" placeholder="总件数">
                </div>
                <div class="form-group">
                    <label for="totalGrossWeight">总毛重</label>
                    <input type="number" step="0.01" id="totalGrossWeight" name="TOTAL_GROSS_WEIGHT" placeholder="总毛重">
                </div>
                <div class="form-group">
                    <label for="totalNetWeight">总净重</label>
                    <input type="number" step="0.01" id="totalNetWeight" name="TOTAL_NET_WEIGHT" placeholder="总净重">
                </div>
                <div class="form-group">
                    <label for="totalVolume">总体积</label>
                    <input type="number" step="0.01" id="totalVolume" name="TOTAL_VOLUME" placeholder="总体积">
                </div>
            </div>
            <button type="button" class="btn" onclick="clearForm('packingForm')">清空表单</button>
            <button type="button" class="btn btn-success" onclick="highlightFields('packingForm')">高亮字段</button>
        </form>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-section">
        <h2>🔧 测试控制面板</h2>
        <div>
            <button class="btn" onclick="testExtensionPresence()">检测扩展是否安装</button>
            <button class="btn" onclick="testApiConnection()">测试API连接</button>
            <button class="btn" onclick="simulateFormFill()">模拟表单填写</button>
            <button class="btn btn-danger" onclick="clearAllForms()">清空所有表单</button>
            <span id="testStatus" class="status-indicator status-ready">就绪</span>
        </div>
        
        <div class="test-results" id="testResults">
            <strong>测试日志：</strong><br>
            页面加载完成，等待测试操作...
        </div>
    </div>

    <script>
        // 测试日志
        function addLog(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            results.textContent += logEntry;
            results.scrollTop = results.scrollHeight;
            
            console.log(`[FreightParser Test] ${message}`);
        }

        // 更新状态
        function updateStatus(text, type = 'ready') {
            const status = document.getElementById('testStatus');
            status.textContent = text;
            status.className = `status-indicator status-${type}`;
        }

        // 检测扩展是否安装
        function testExtensionPresence() {
            addLog('检测扩展是否安装...');
            updateStatus('检测中...', 'processing');
            
            // 检查是否有content script注入
            if (window.freightParserContent) {
                addLog('✅ 扩展已安装并正常运行');
                updateStatus('扩展已安装', 'success');
            } else {
                addLog('❌ 未检测到扩展，请确认是否正确安装');
                updateStatus('扩展未安装', 'error');
            }
        }

        // 测试API连接
        async function testApiConnection() {
            addLog('测试API连接...');
            updateStatus('连接中...', 'processing');
            
            try {
                const response = await fetch('http://localhost:8080/api/document/health');
                if (response.ok) {
                    const data = await response.json();
                    addLog('✅ API连接成功: ' + JSON.stringify(data));
                    updateStatus('API连接正常', 'success');
                } else {
                    addLog('❌ API连接失败: HTTP ' + response.status);
                    updateStatus('API连接失败', 'error');
                }
            } catch (error) {
                addLog('❌ API连接错误: ' + error.message);
                updateStatus('API连接错误', 'error');
            }
        }

        // 模拟表单填写
        function simulateFormFill() {
            addLog('模拟表单填写...');
            updateStatus('填写中...', 'processing');
            
            const mockData = {
                'BL_NO': 'MOCK123456789',
                'SHIPPER': '测试发货公司\n地址：测试地址123号',
                'CONSIGNEE': '测试收货公司\n地址：测试地址456号',
                'PORT_OF_LOADING': 'CNSHA',
                'PORT_OF_DISCHARGE': 'USLAX',
                'VESSEL_VOYAGE': 'TEST VESSEL / 001E',
                'CONTAINER_NO': 'MOCK1234567',
                'GOODS_DESCRIPTION': '测试货物描述',
                'PACKAGES': '100',
                'GROSS_WEIGHT': '1000',
                'MEASUREMENT': '50',
                'INVOICE_NO': 'INV-2024-001',
                'TOTAL_AMOUNT': '50000',
                'CURRENCY': 'USD'
            };
            
            let filledCount = 0;
            Object.entries(mockData).forEach(([key, value]) => {
                const field = document.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = value;
                    field.classList.add('field-filled');
                    filledCount++;
                    
                    // 触发事件
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                }
            });
            
            addLog(`✅ 模拟填写完成，填写了 ${filledCount} 个字段`);
            updateStatus('填写完成', 'success');
            
            // 3秒后移除高亮
            setTimeout(() => {
                document.querySelectorAll('.field-filled').forEach(field => {
                    field.classList.remove('field-filled');
                });
            }, 3000);
        }

        // 清空表单
        function clearForm(formId) {
            const form = document.getElementById(formId);
            form.reset();
            addLog(`清空表单: ${formId}`);
        }

        // 清空所有表单
        function clearAllForms() {
            document.querySelectorAll('form').forEach(form => {
                form.reset();
            });
            document.querySelectorAll('.field-highlight, .field-filled').forEach(field => {
                field.classList.remove('field-highlight', 'field-filled');
            });
            addLog('清空所有表单');
            updateStatus('已清空', 'ready');
        }

        // 高亮字段
        function highlightFields(formId) {
            const form = document.getElementById(formId);
            const fields = form.querySelectorAll('input, textarea, select');
            
            fields.forEach(field => {
                field.classList.add('field-highlight');
            });
            
            addLog(`高亮表单字段: ${formId} (${fields.length} 个字段)`);
            
            // 5秒后移除高亮
            setTimeout(() => {
                fields.forEach(field => {
                    field.classList.remove('field-highlight');
                });
            }, 5000);
        }

        // 监听扩展消息
        document.addEventListener('freightParser:initialized', function(e) {
            addLog('🎉 扩展初始化完成: ' + JSON.stringify(e.detail));
        });

        document.addEventListener('freightParser:parseComplete', function(e) {
            addLog('📄 文档解析完成: ' + Object.keys(e.detail.parsedData || {}).length + ' 个字段');
        });

        document.addEventListener('freightParser:fillComplete', function(e) {
            addLog('📝 表单回填完成: ' + e.detail.filledFields.length + ' 个字段');
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            addLog('测试页面加载完成');
            addLog('请点击浏览器工具栏中的扩展图标开始测试');
            
            // 自动检测扩展
            setTimeout(() => {
                testExtensionPresence();
            }, 1000);
        });
    </script>
</body>
</html>
