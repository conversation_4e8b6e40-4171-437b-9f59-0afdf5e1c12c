# 货代单证智能解析浏览器扩展 - 安装部署指南

## 📋 系统要求

### 浏览器要求
- **Chrome浏览器** 版本 ≥ 88
- **Microsoft Edge** 版本 ≥ 88 (基于Chromium)
- **其他Chromium内核浏览器** 支持Manifest V3

### 后端服务要求
- **Java** 版本 ≥ 17
- **MySQL** 版本 ≥ 8.0
- **Maven** 版本 ≥ 3.6

## 🚀 快速安装

### 第一步：准备图标文件

1. **转换SVG图标为PNG**
   ```bash
   cd browser-extension/icons
   
   # 使用Inkscape转换（推荐）
   inkscape icon.svg --export-png=icon16.png --export-width=16 --export-height=16
   inkscape icon.svg --export-png=icon32.png --export-width=32 --export-height=32
   inkscape icon.svg --export-png=icon48.png --export-width=48 --export-height=48
   inkscape icon.svg --export-png=icon128.png --export-width=128 --export-height=128
   ```

2. **验证图标文件**
   确保以下文件存在：
   ```
   browser-extension/icons/
   ├── icon16.png
   ├── icon32.png
   ├── icon48.png
   └── icon128.png
   ```

### 第二步：启动后端服务

1. **启动Spring Boot应用**
   ```bash
   cd /path/to/compute_view_input
   mvn spring-boot:run
   ```

2. **验证服务运行**
   ```bash
   curl http://localhost:8080/api/document/health
   ```
   
   应该返回类似：
   ```json
   {
     "success": true,
     "data": {
       "status": "UP",
       "timestamp": 1705123456789,
       "service": "document-parse-service"
     }
   }
   ```

### 第三步：安装浏览器扩展

1. **打开Chrome扩展管理页面**
   - 方法一：地址栏输入 `chrome://extensions/`
   - 方法二：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `browser-extension` 文件夹
   - 点击"选择文件夹"

4. **验证安装成功**
   - 扩展列表中出现"货代单证智能解析助手"
   - 浏览器工具栏出现扩展图标
   - 状态显示为"已启用"

### 第四步：配置扩展

1. **点击扩展图标**，打开弹窗面板

2. **点击设置按钮**（⚙️），打开设置页面

3. **配置API服务**
   - API服务地址：`http://localhost:8080/api`
   - 点击"测试连接"验证服务可用性

4. **保存设置**

## 🔧 详细配置

### 后端服务配置

1. **数据库配置**
   ```properties
   # application.properties
   spring.datasource.url=******************************************
   spring.datasource.username=your_username
   spring.datasource.password=your_password
   ```

2. **CORS配置**
   ```properties
   # 允许扩展访问
   app.cors.allowed-origins=chrome-extension://*,moz-extension://*
   ```

3. **文件上传配置**
   ```properties
   # 文件大小限制
   spring.servlet.multipart.max-file-size=20MB
   spring.servlet.multipart.max-request-size=20MB
   ```

### 扩展高级配置

1. **自定义API地址**
   - 如果后端部署在其他服务器，修改API地址
   - 支持HTTPS：`https://your-domain.com/api`

2. **OCR语言设置**
   - 中英文混合：`chi_sim+eng`（默认）
   - 纯英文：`eng`
   - 纯中文：`chi_sim`

3. **调试模式**
   - 启用调试模式查看详细日志
   - 在浏览器开发者工具中查看控制台输出

## 🌐 生产环境部署

### 后端服务部署

1. **打包应用**
   ```bash
   mvn clean package -DskipTests
   ```

2. **Docker部署**
   ```dockerfile
   FROM openjdk:17-jre-slim
   COPY target/compute_view_input-1.0.0.jar app.jar
   EXPOSE 8080
   ENTRYPOINT ["java", "-jar", "/app.jar"]
   ```

3. **Nginx反向代理**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location /api/ {
           proxy_pass http://localhost:8080/api/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### 扩展发布

1. **打包扩展**
   - 在Chrome扩展管理页面点击"打包扩展程序"
   - 选择 `browser-extension` 文件夹
   - 生成 `.crx` 文件和 `.pem` 私钥文件

2. **Chrome Web Store发布**
   - 注册Chrome开发者账号
   - 上传扩展包
   - 填写扩展信息和截图
   - 提交审核

3. **企业内部分发**
   - 将 `.crx` 文件分发给用户
   - 用户拖拽到扩展管理页面安装

## 🔒 安全配置

### 后端安全

1. **API认证**
   ```java
   @RestController
   @RequestMapping("/api")
   public class DocumentController {
       
       @PostMapping("/parse")
       public ResponseEntity<?> parse(
           @RequestHeader("X-Client-ID") String clientId,
           @RequestParam("file") MultipartFile file) {
           // 验证客户端ID
           if (!isValidClient(clientId)) {
               return ResponseEntity.status(403).build();
           }
           // 处理请求...
       }
   }
   ```

2. **CORS限制**
   ```properties
   # 限制允许的域名
   app.cors.allowed-origins=chrome-extension://your-extension-id
   ```

3. **文件类型验证**
   ```java
   private boolean isValidFileType(String contentType) {
       return Arrays.asList(
           "application/pdf",
           "image/jpeg",
           "image/png"
       ).contains(contentType);
   }
   ```

### 扩展安全

1. **权限最小化**
   ```json
   {
     "permissions": [
       "activeTab",
       "storage"
     ],
     "host_permissions": [
       "https://your-api-domain.com/*"
     ]
   }
   ```

2. **内容安全策略**
   ```json
   {
     "content_security_policy": {
       "extension_pages": "script-src 'self'; object-src 'self'"
     }
   }
   ```

## 🐛 故障排除

### 常见问题

1. **扩展无法加载**
   ```
   错误：Manifest file is missing or unreadable
   解决：检查manifest.json文件是否存在且格式正确
   ```

2. **API连接失败**
   ```
   错误：Failed to fetch
   解决：检查后端服务是否运行，CORS配置是否正确
   ```

3. **文件上传失败**
   ```
   错误：File too large
   解决：检查文件大小限制配置
   ```

4. **OCR识别失败**
   ```
   错误：Tesseract not found
   解决：确保Tesseract已正确安装和配置
   ```

### 调试方法

1. **启用扩展调试**
   - 在扩展管理页面点击"详细信息"
   - 启用"收集错误"
   - 查看错误日志

2. **查看网络请求**
   - 打开开发者工具（F12）
   - 切换到Network标签
   - 执行操作查看请求状态

3. **检查控制台日志**
   - 在开发者工具Console标签查看日志
   - 搜索"FreightParser"相关信息

## 📞 技术支持

### 获取帮助

1. **文档资源**
   - [用户手册](README.md)
   - [API文档](../docs/API文档.md)
   - [常见问题](FAQ.md)

2. **社区支持**
   - GitHub Issues：报告Bug和功能请求
   - 邮件支持：<EMAIL>

3. **商业支持**
   - 企业级技术支持
   - 定制开发服务
   - 培训和咨询

### 反馈渠道

- **Bug报告**：https://github.com/your-repo/issues
- **功能建议**：https://github.com/your-repo/discussions
- **安全问题**：<EMAIL>

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。
