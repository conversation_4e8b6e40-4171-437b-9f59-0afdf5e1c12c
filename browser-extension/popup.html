<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货代单证智能解析</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">🚢</div>
                    <div class="logo-text">
                        <h1>货代智能解析</h1>
                        <span class="version">v1.0.0</span>
                    </div>
                </div>
                <div class="status" id="status">
                    <span class="status-dot"></span>
                    <span class="status-text">就绪</span>
                </div>
            </div>
        </div>

        <!-- 主要功能区 -->
        <div class="main-content">
            <!-- 文件上传区 -->
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <div>点击上传单证文件</div>
                        <small>支持 PDF、JPG、PNG 格式</small>
                    </div>
                    <input type="file" id="fileInput" accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                </div>
                
                <div class="file-info" id="fileInfo" style="display: none;">
                    <div class="file-name" id="fileName"></div>
                    <div class="file-size" id="fileSize"></div>
                </div>
            </div>

            <!-- 配置选项 -->
            <div class="options-section">
                <div class="option-group">
                    <label for="documentType">单证类型：</label>
                    <select id="documentType">
                        <option value="AUTO_DETECT">🔍 自动识别</option>
                        <option value="BILL_OF_LADING">🚢 海运提单</option>
                        <option value="COMMERCIAL_INVOICE">💰 商业发票</option>
                        <option value="PACKING_LIST">📦 装箱单</option>
                        <option value="CUSTOMS_DECLARATION">📋 报关单</option>
                        <option value="FREIGHT_INVOICE">💳 货代费用单</option>
                        <option value="CERTIFICATE_OF_ORIGIN">📜 原产地证</option>
                    </select>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="actions">
                <button id="parseBtn" class="btn btn-primary" disabled>
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">开始解析</span>
                </button>
                <button id="fillBtn" class="btn btn-success" disabled>
                    <span class="btn-icon">📝</span>
                    <span class="btn-text">智能回填</span>
                </button>
            </div>

            <!-- 进度显示 -->
            <div class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备中...</div>
            </div>

            <!-- 结果显示 -->
            <div class="result-section" id="resultSection" style="display: none;">
                <div class="result-header">
                    <span class="result-icon">✅</span>
                    <span class="result-title">解析完成</span>
                </div>
                <div class="result-stats">
                    <div class="stat-item">
                        <span class="stat-label">提取字段：</span>
                        <span class="stat-value" id="fieldCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">置信度：</span>
                        <span class="stat-value" id="confidence">0%</span>
                    </div>
                </div>
                <div class="result-actions">
                    <button id="viewResultBtn" class="btn btn-outline">查看详情</button>
                </div>
            </div>

            <!-- 页面检测 -->
            <div class="page-info">
                <div class="page-info-header">
                    <h3>📊 页面分析</h3>
                </div>
                <div class="page-stats">
                    <div class="stat-card">
                        <div class="stat-icon">📄</div>
                        <div class="stat-content">
                            <div class="stat-number" id="formCount">0</div>
                            <div class="stat-label">表单</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📝</div>
                        <div class="stat-content">
                            <div class="stat-number" id="fieldCount2">0</div>
                            <div class="stat-label">字段</div>
                        </div>
                    </div>
                </div>
                <div class="page-actions">
                    <button id="scanPageBtn" class="btn btn-outline btn-small">
                        <span class="btn-icon">🔍</span>
                        <span class="btn-text">重新扫描</span>
                    </button>
                    <button id="refreshPageBtn" class="btn btn-outline btn-small">
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">刷新页面</span>
                    </button>
                    <button id="debugFieldsBtn" class="btn btn-outline btn-small">
                        <span class="btn-icon">🐛</span>
                        <span class="btn-text">调试字段</span>
                    </button>
                    <button id="analyzePageBtn" class="btn btn-outline btn-small">
                        <span class="btn-icon">🔍</span>
                        <span class="btn-text">页面分析</span>
                    </button>
                    <button id="forceScreenshotBtn" class="btn btn-outline btn-small">
                        <span class="btn-icon">📸</span>
                        <span class="btn-text">截图检测</span>
                    </button>
                    <button id="deepDebugBtn" class="btn btn-outline btn-small">
                        <span class="btn-icon">🔬</span>
                        <span class="btn-text">深度调试</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer">
            <div class="footer-links">
                <button class="footer-link" id="settingsLink">
                    <span class="link-icon">⚙️</span>
                    <span class="link-text">设置</span>
                </button>
                <button class="footer-link" id="helpLink">
                    <span class="link-icon">❓</span>
                    <span class="link-text">帮助</span>
                </button>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
