/**
 * 货代单证智能解析浏览器扩展 - Content Script
 * 负责页面交互、表单扫描和智能回填
 */

class FreightParserContent {
    constructor() {
        this.formFields = [];
        this.isInitialized = false;
        this.detectionMethod = 'vision'; // 'dom', 'vision', 'ml'

        // 初始化检测器
        this.visionDetector = null;
        this.mlDetector = null;

        this.init();
    }

    async init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 初始化检测器
        await this.initDetectors();

        // 页面加载完成后扫描表单
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.scanPage());
        } else {
            this.scanPage();
        }

        this.isInitialized = true;
        console.log('FreightParser Content Script initialized with', this.detectionMethod, 'detection');
    }

    /**
     * 初始化检测器
     */
    async initDetectors() {
        try {
            // 动态加载检测器
            if (this.detectionMethod === 'vision') {
                // 加载视觉检测器
                await this.loadScript('vision-field-detector.js');
                this.visionDetector = new VisionFieldDetector();
            } else if (this.detectionMethod === 'ml') {
                // 加载ML检测器
                await this.loadScript('ml-field-detector.js');
                this.mlDetector = new MLFieldDetector();
            }
        } catch (error) {
            console.error('Failed to initialize detectors:', error);
            // 降级到DOM检测
            this.detectionMethod = 'dom';
        }
    }

    /**
     * 动态加载脚本
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL(src);
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'ping':
                    sendResponse({ success: true, ready: true });
                    break;

                case 'scanPage':
                    const scanResult = this.scanPage();
                    sendResponse(scanResult);
                    break;

                case 'fillForm':
                    const fillResult = await this.fillForm(request.data);
                    sendResponse(fillResult);
                    break;

                case 'highlightFields':
                    this.highlightFields();
                    sendResponse({ success: true });
                    break;

                case 'debugFields':
                    const debugResult = this.debugFields();
                    sendResponse(debugResult);
                    break;

                case 'highlightField':
                    this.highlightSingleField(request.fieldIndex);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action: ' + request.action });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * 扫描页面表单和字段
     */
    async scanPage() {
        console.log('Scanning page for forms and fields using', this.detectionMethod, 'method...');

        let result;

        try {
            if (this.detectionMethod === 'vision' && this.visionDetector) {
                result = await this.scanWithVision();
            } else if (this.detectionMethod === 'ml' && this.mlDetector) {
                result = await this.scanWithML();
            } else {
                result = await this.scanWithDOM();
            }
        } catch (error) {
            console.error('Advanced scanning failed, falling back to DOM:', error);
            result = await this.scanWithDOM();
        }

        console.log('Page scan result:', result);
        return result;
    }

    /**
     * 使用视觉检测扫描
     */
    async scanWithVision() {
        const visionResult = await this.visionDetector.detectFields();

        if (visionResult.success) {
            this.formFields = visionResult.fields;
            return {
                formCount: 1, // 视觉检测不区分表单
                fieldCount: visionResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'vision',
                confidence: visionResult.confidence
            };
        } else {
            throw new Error('Vision detection failed: ' + visionResult.error);
        }
    }

    /**
     * 使用机器学习扫描
     */
    async scanWithML() {
        const mlResult = await this.mlDetector.detectFields();

        if (mlResult.success) {
            this.formFields = mlResult.fields;
            return {
                formCount: 1,
                fieldCount: mlResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'ml',
                confidence: mlResult.confidence
            };
        } else {
            throw new Error('ML detection failed: ' + mlResult.error);
        }
    }

    /**
     * 使用DOM扫描（原有方法）
     */
    async scanWithDOM() {
        // 扫描表单
        const forms = document.querySelectorAll('form');

        // 扫描所有可填写的字段
        this.formFields = this.scanFormFields();

        return {
            formCount: forms.length,
            fieldCount: this.formFields.length,
            url: window.location.href,
            title: document.title,
            method: 'dom'
        };
    }

    /**
     * 扫描表单字段
     */
    scanFormFields() {
        const fields = [];

        // 扩展的字段选择器，包含更多可能的输入控件
        const selectors = [
            // 标准HTML输入控件
            'input[type="text"]',
            'input[type="email"]',
            'input[type="tel"]',
            'input[type="number"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'input[type="time"]',
            'input[type="url"]',
            'input[type="search"]',
            'input[type="password"]',
            'input:not([type])', // 默认为text类型
            'textarea',
            'select',

            // 常见的UI框架控件
            '[contenteditable="true"]', // 可编辑div
            '.ant-input', // Ant Design
            '.el-input__inner', // Element UI
            '.form-control', // Bootstrap
            '.layui-input', // Layui
            '.ivu-input', // iView
            '.van-field__control', // Vant

            // 自定义输入控件（通过常见的class名称）
            '[class*="input"]',
            '[class*="field"]',
            '[class*="form"]',
            '[class*="text"]',
            '[class*="edit"]',

            // 通过角色和属性识别
            '[role="textbox"]',
            '[role="combobox"]',
            '[role="spinbutton"]',
            '[data-field]',
            '[data-name]',
            '[name]'
        ];

        // 使用Set避免重复
        const foundElements = new Set();

        selectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(element => {
                    if (!foundElements.has(element) && this.isValidFormField(element)) {
                        const fieldInfo = this.extractFieldInfo(element);
                        if (fieldInfo) {
                            fields.push(fieldInfo);
                            foundElements.add(element);
                        }
                    }
                });
            } catch (error) {
                console.warn('Error with selector:', selector, error);
            }
        });

        console.log(`Found ${fields.length} form fields:`, fields);
        return fields;
    }

    /**
     * 检查是否为有效的表单字段
     */
    isValidFormField(element) {
        // 排除明显的隐藏字段
        if (element.type === 'hidden') {
            return false;
        }

        // 检查元素是否在DOM中
        if (!element.isConnected) {
            return false;
        }

        // 获取计算样式
        const style = window.getComputedStyle(element);

        // 排除完全不可见的字段（但允许部分透明）
        if (style.display === 'none' ||
            style.visibility === 'hidden') {
            return false;
        }

        // 检查元素尺寸（排除0尺寸的元素）
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 && rect.height === 0) {
            return false;
        }

        // 排除密码字段（安全考虑）
        if (element.type === 'password') {
            return false;
        }

        // 排除按钮类型
        if (element.type === 'button' ||
            element.type === 'submit' ||
            element.type === 'reset' ||
            element.type === 'image') {
            return false;
        }

        // 排除文件上传（避免冲突）
        if (element.type === 'file') {
            return false;
        }

        // 检查是否为可交互元素
        const tagName = element.tagName.toLowerCase();
        const isInteractive = [
            'input', 'textarea', 'select'
        ].includes(tagName) ||
        element.contentEditable === 'true' ||
        element.hasAttribute('role');

        if (!isInteractive) {
            // 对于非标准控件，检查是否有输入相关的class或属性
            const className = element.className.toLowerCase();
            const hasInputClass = [
                'input', 'field', 'form', 'text', 'edit', 'control'
            ].some(keyword => className.includes(keyword));

            if (!hasInputClass && !element.hasAttribute('data-field') && !element.hasAttribute('name')) {
                return false;
            }
        }

        return true;
    }

    /**
     * 提取字段信息
     */
    extractFieldInfo(element) {
        const info = {
            element: element,
            id: element.id || '',
            name: element.name || '',
            type: element.type || element.tagName.toLowerCase(),
            tagName: element.tagName.toLowerCase(),
            placeholder: element.placeholder || '',
            value: this.getElementValue(element),
            className: element.className || '',
            label: this.findFieldLabel(element),
            xpath: this.getElementXPath(element),
            // 添加更多属性
            dataField: element.getAttribute('data-field') || '',
            dataName: element.getAttribute('data-name') || '',
            title: element.title || '',
            ariaLabel: element.getAttribute('aria-label') || '',
            role: element.getAttribute('role') || ''
        };

        // 生成字段的文本描述（用于匹配）
        info.textContent = [
            info.id,
            info.name,
            info.label,
            info.placeholder,
            info.className,
            info.dataField,
            info.dataName,
            info.title,
            info.ariaLabel
        ].filter(Boolean).join(' ').toLowerCase();

        // 添加调试信息
        console.log('Extracted field info:', {
            tag: info.tagName,
            id: info.id,
            name: info.name,
            label: info.label,
            className: info.className
        });

        return info;
    }

    /**
     * 获取元素的值
     */
    getElementValue(element) {
        if (element.tagName.toLowerCase() === 'select') {
            return element.selectedOptions.length > 0 ? element.selectedOptions[0].text : '';
        } else if (element.contentEditable === 'true') {
            return element.textContent || element.innerText || '';
        } else {
            return element.value || '';
        }
    }

    /**
     * 查找字段标签
     */
    findFieldLabel(element) {
        let label = '';

        // 1. 通过for属性关联的label
        if (element.id) {
            const labelElement = document.querySelector(`label[for="${element.id}"]`);
            if (labelElement) {
                label = labelElement.textContent.trim();
                if (label) return this.cleanLabelText(label);
            }
        }

        // 2. 父级label
        const parentLabel = element.closest('label');
        if (parentLabel) {
            label = parentLabel.textContent.replace(element.value || '', '').trim();
            if (label) return this.cleanLabelText(label);
        }

        // 3. aria-label属性
        if (element.getAttribute('aria-label')) {
            return this.cleanLabelText(element.getAttribute('aria-label'));
        }

        // 4. title属性
        if (element.title) {
            return this.cleanLabelText(element.title);
        }

        // 5. placeholder作为标签
        if (element.placeholder && element.placeholder.length < 30) {
            return this.cleanLabelText(element.placeholder);
        }

        // 6. 查找同一行或附近的文本标签
        label = this.findNearbyLabel(element);
        if (label) return this.cleanLabelText(label);

        // 7. 查找表格中的表头
        label = this.findTableHeader(element);
        if (label) return this.cleanLabelText(label);

        // 8. 查找父级容器中的标签文本
        label = this.findContainerLabel(element);
        if (label) return this.cleanLabelText(label);

        return '';
    }

    /**
     * 清理标签文本
     */
    cleanLabelText(text) {
        return text
            .replace(/[：:*]/g, '') // 移除冒号和星号
            .replace(/\s+/g, ' ') // 合并空白字符
            .trim();
    }

    /**
     * 查找附近的标签
     */
    findNearbyLabel(element) {
        // 查找前面的兄弟元素
        let prev = element.previousElementSibling;
        let attempts = 0;
        while (prev && attempts < 3) {
            const text = prev.textContent?.trim();
            if (text && text.length < 50 && text.length > 1) {
                // 排除纯数字或特殊字符
                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                    return text;
                }
            }
            prev = prev.previousElementSibling;
            attempts++;
        }

        // 查找后面的兄弟元素（某些布局中标签在后面）
        let next = element.nextElementSibling;
        attempts = 0;
        while (next && attempts < 2) {
            const text = next.textContent?.trim();
            if (text && text.length < 30 && text.length > 1) {
                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                    return text;
                }
            }
            next = next.nextElementSibling;
            attempts++;
        }

        return '';
    }

    /**
     * 查找表格表头
     */
    findTableHeader(element) {
        const cell = element.closest('td, th');
        if (!cell) return '';

        const table = cell.closest('table');
        if (!table) return '';

        const cellIndex = Array.from(cell.parentNode.children).indexOf(cell);
        const headerRow = table.querySelector('thead tr, tr:first-child');

        if (headerRow) {
            const headerCell = headerRow.children[cellIndex];
            if (headerCell) {
                const text = headerCell.textContent?.trim();
                if (text && text.length < 50) {
                    return text;
                }
            }
        }

        return '';
    }

    /**
     * 查找容器标签
     */
    findContainerLabel(element) {
        // 查找父级容器中的标签文本
        let parent = element.parentElement;
        let level = 0;

        while (parent && level < 3) {
            // 查找容器中的直接文本节点
            const textNodes = this.getDirectTextNodes(parent);
            for (const node of textNodes) {
                const text = node.textContent?.trim();
                if (text && text.length < 50 && text.length > 1) {
                    if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                        return text;
                    }
                }
            }

            parent = parent.parentElement;
            level++;
        }

        return '';
    }

    /**
     * 获取直接文本节点（不包括子元素的文本）
     */
    getDirectTextNodes(element) {
        const textNodes = [];
        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        return textNodes;
    }

    /**
     * 获取元素的XPath
     */
    getElementXPath(element) {
        if (element.id) {
            return `//*[@id="${element.id}"]`;
        }
        
        const parts = [];
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let index = 0;
            let sibling = element.previousSibling;
            
            while (sibling) {
                if (sibling.nodeType === Node.ELEMENT_NODE && sibling.tagName === element.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            const tagName = element.tagName.toLowerCase();
            const pathIndex = index > 0 ? `[${index + 1}]` : '';
            parts.unshift(`${tagName}${pathIndex}`);
            
            element = element.parentNode;
        }
        
        return parts.length ? '/' + parts.join('/') : '';
    }

    /**
     * 获取元素中的文本节点
     */
    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        
        return textNodes;
    }

    /**
     * 智能回填表单
     */
    async fillForm(parsedData) {
        console.log('Starting intelligent form fill with data:', parsedData);
        
        if (!parsedData || Object.keys(parsedData).length === 0) {
            throw new Error('没有可用的解析数据');
        }

        // 重新扫描页面（可能有动态字段）
        this.formFields = this.scanFormFields();
        
        const filledFields = [];
        const fieldMapping = this.getFieldMapping();

        // 遍历解析出的数据
        for (const [dataKey, dataValue] of Object.entries(parsedData)) {
            if (!dataValue || dataValue.toString().trim() === '') {
                continue;
            }

            // 查找匹配的字段
            const matchedField = this.findMatchingField(dataKey, dataValue, fieldMapping);
            
            if (matchedField) {
                try {
                    await this.fillField(matchedField, dataValue);
                    filledFields.push({
                        dataKey,
                        dataValue,
                        fieldInfo: {
                            id: matchedField.id,
                            name: matchedField.name,
                            label: matchedField.label
                        }
                    });
                } catch (error) {
                    console.error(`Failed to fill field ${dataKey}:`, error);
                }
            } else {
                console.warn(`No matching field found for ${dataKey}: ${dataValue}`);
            }
        }

        console.log(`Form fill completed. Filled ${filledFields.length} fields:`, filledFields);

        return {
            success: true,
            filledCount: filledFields.length,
            filledFields: filledFields,
            totalDataFields: Object.keys(parsedData).length
        };
    }

    /**
     * 获取字段映射配置
     */
    getFieldMapping() {
        return {
            // 提单相关
            'BL_NO': ['提单号', 'bill', 'bl', 'blno', 'bill_number', 'bl_number'],
            'SHIPPER': ['发货人', 'shipper', 'consignor', 'sender', 'from'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver', 'to'],
            'NOTIFY_PARTY': ['通知方', 'notify', 'notify_party'],
            'PORT_OF_LOADING': ['起运港', 'pol', 'loading', 'origin', 'from_port'],
            'PORT_OF_DISCHARGE': ['目的港', 'pod', 'discharge', 'destination', 'to_port'],
            'VESSEL_VOYAGE': ['船名', 'vessel', 'voyage', 'ship'],
            'CONTAINER_NO': ['集装箱', 'container', 'cntr', 'box'],
            'SEAL_NO': ['封条', 'seal'],
            'GOODS_DESCRIPTION': ['货物', 'goods', 'cargo', 'commodity', 'description'],
            'PACKAGES': ['件数', 'packages', 'pkgs', 'pieces'],
            'GROSS_WEIGHT': ['毛重', 'weight', 'gross'],
            'MEASUREMENT': ['体积', 'volume', 'cbm', 'measurement'],
            'ETD': ['开船', 'etd', 'departure'],
            'ETA': ['到港', 'eta', 'arrival'],
            'FREIGHT_TERMS': ['运费', 'freight', 'payment'],

            // 发票相关
            'INVOICE_NO': ['发票号', 'invoice', 'inv'],
            'INVOICE_DATE': ['发票日期', 'invoice_date', 'date'],
            'TOTAL_AMOUNT': ['金额', 'amount', 'total', 'value'],
            'CURRENCY': ['币种', 'currency'],
            'SELLER': ['卖方', 'seller', 'vendor'],
            'BUYER': ['买方', 'buyer', 'customer'],
            'TRADE_TERMS': ['贸易条款', 'terms', 'incoterms'],

            // 装箱单相关
            'PACKING_LIST_NO': ['装箱单', 'packing', 'pl'],
            'TOTAL_PACKAGES': ['总件数', 'total_packages'],
            'TOTAL_GROSS_WEIGHT': ['总毛重', 'total_weight'],
            'TOTAL_NET_WEIGHT': ['总净重', 'net_weight'],
            'TOTAL_VOLUME': ['总体积', 'total_volume']
        };
    }

    /**
     * 查找匹配的字段
     */
    findMatchingField(dataKey, dataValue, fieldMapping) {
        const aliases = fieldMapping[dataKey] || [dataKey.toLowerCase()];
        let bestMatch = null;
        let bestScore = 0;

        for (const field of this.formFields) {
            let score = 0;

            // 精确匹配字段名或ID
            if (field.name === dataKey || field.id === dataKey) {
                score += 100;
            }

            // 别名匹配
            for (const alias of aliases) {
                if (field.textContent.includes(alias)) {
                    score += 50;
                }
            }

            // 字段类型匹配
            if (this.isFieldTypeMatch(dataKey, dataValue, field)) {
                score += 20;
            }

            // 位置权重（表单中靠前的字段优先级更高）
            const formIndex = this.formFields.indexOf(field);
            score += Math.max(0, 10 - formIndex * 0.1);

            if (score > bestScore) {
                bestScore = score;
                bestMatch = field;
            }
        }

        // 只返回置信度足够高的匹配
        return bestScore > 30 ? bestMatch : null;
    }

    /**
     * 检查字段类型是否匹配
     */
    isFieldTypeMatch(dataKey, dataValue, field) {
        // 数字字段
        if (['PACKAGES', 'GROSS_WEIGHT', 'MEASUREMENT', 'TOTAL_AMOUNT'].includes(dataKey)) {
            return field.type === 'number' || /number|amount|weight|volume|quantity/i.test(field.textContent);
        }

        // 日期字段
        if (['ETD', 'ETA', 'INVOICE_DATE'].includes(dataKey)) {
            return field.type === 'date' || /date|time/i.test(field.textContent);
        }

        // 邮箱字段
        if (/email/i.test(dataKey)) {
            return field.type === 'email' || /email|mail/i.test(field.textContent);
        }

        // 长文本字段
        if (['SHIPPER', 'CONSIGNEE', 'GOODS_DESCRIPTION'].includes(dataKey)) {
            return field.tagName === 'textarea' || /address|description|detail/i.test(field.textContent);
        }

        return true;
    }

    /**
     * 填充字段
     */
    async fillField(field, value) {
        const element = field.element;
        
        // 聚焦字段
        element.focus();
        
        // 清空现有值
        element.value = '';
        
        // 设置新值
        if (field.tagName === 'select') {
            // 下拉框处理
            this.selectOption(element, value);
        } else {
            // 输入框处理
            element.value = value.toString();
        }
        
        // 触发事件
        this.triggerEvents(element);
        
        // 添加视觉效果
        this.addFillEffect(element);
        
        // 短暂延迟，模拟人工输入
        await this.sleep(100);
    }

    /**
     * 选择下拉框选项
     */
    selectOption(selectElement, value) {
        const options = selectElement.querySelectorAll('option');
        const valueStr = value.toString().toLowerCase();
        
        for (const option of options) {
            const optionText = option.textContent.toLowerCase();
            const optionValue = option.value.toLowerCase();
            
            if (optionValue === valueStr || 
                optionText === valueStr || 
                optionText.includes(valueStr) || 
                valueStr.includes(optionText)) {
                option.selected = true;
                selectElement.value = option.value;
                break;
            }
        }
    }

    /**
     * 触发相关事件
     */
    triggerEvents(element) {
        const events = ['input', 'change', 'blur', 'keyup'];
        
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            element.dispatchEvent(event);
        });
    }

    /**
     * 添加填充效果
     */
    addFillEffect(element) {
        const originalStyle = {
            backgroundColor: element.style.backgroundColor,
            borderColor: element.style.borderColor,
            transition: element.style.transition
        };
        
        // 添加高亮效果
        element.style.transition = 'all 0.3s ease';
        element.style.backgroundColor = '#e8f5e8';
        element.style.borderColor = '#28a745';
        
        // 3秒后恢复原样
        setTimeout(() => {
            element.style.backgroundColor = originalStyle.backgroundColor;
            element.style.borderColor = originalStyle.borderColor;
            element.style.transition = originalStyle.transition;
        }, 3000);
    }

    /**
     * 高亮显示所有可填写字段
     */
    highlightFields() {
        this.formFields.forEach(field => {
            const element = field.element;
            element.style.outline = '2px solid #007bff';
            element.style.outlineOffset = '2px';
            
            // 添加提示
            const tooltip = document.createElement('div');
            tooltip.textContent = field.label || field.name || field.id || '未知字段';
            tooltip.style.cssText = `
                position: absolute;
                background: #333;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 10000;
                pointer-events: none;
            `;
            
            const rect = element.getBoundingClientRect();
            tooltip.style.left = (rect.left + window.scrollX) + 'px';
            tooltip.style.top = (rect.top + window.scrollY - 30) + 'px';
            
            document.body.appendChild(tooltip);
            
            // 5秒后移除高亮
            setTimeout(() => {
                element.style.outline = '';
                element.style.outlineOffset = '';
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 5000);
        });
    }

    /**
     * 调试字段识别
     */
    debugFields() {
        console.log('Starting field debug...');

        // 重新扫描字段
        this.formFields = this.scanFormFields();

        // 返回详细的字段信息
        const debugInfo = {
            success: true,
            fields: this.formFields.map(field => ({
                tagName: field.tagName,
                type: field.type,
                id: field.id,
                name: field.name,
                className: field.className,
                label: field.label,
                placeholder: field.placeholder,
                value: field.value,
                textContent: field.textContent,
                xpath: field.xpath
            }))
        };

        console.log('Debug info:', debugInfo);
        return debugInfo;
    }

    /**
     * 高亮单个字段
     */
    highlightSingleField(fieldIndex) {
        if (fieldIndex >= 0 && fieldIndex < this.formFields.length) {
            const field = this.formFields[fieldIndex];
            const element = field.element;

            // 清除之前的高亮
            document.querySelectorAll('.freight-parser-debug-highlight').forEach(el => {
                el.classList.remove('freight-parser-debug-highlight');
            });

            // 高亮选中的字段
            element.classList.add('freight-parser-debug-highlight');
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 5秒后移除高亮
            setTimeout(() => {
                element.classList.remove('freight-parser-debug-highlight');
            }, 5000);
        }
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化Content Script
if (typeof window !== 'undefined') {
    // 避免重复初始化
    if (!window.freightParserContent) {
        window.freightParserContent = new FreightParserContent();
        console.log('FreightParser Content Script initialized');
    } else {
        console.log('FreightParser Content Script already exists');
    }
}
