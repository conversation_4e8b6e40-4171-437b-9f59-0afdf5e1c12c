/**
 * 货代单证智能解析浏览器扩展 - Content Script
 * 负责页面交互、表单扫描和智能回填
 */

// 内嵌截图检测器类
class EmbeddedScreenshotDetector {
    constructor() {
        console.log('EmbeddedScreenshotDetector initialized');
    }

    async detectFields() {
        try {
            console.log('🔍 Starting embedded screenshot detection...');

            // 直接从DOM中查找输入元素，但使用更智能的方法
            const inputRegions = await this.findInputRegions();
            const textLabels = await this.findTextLabels();
            const matchedFields = await this.matchFieldsAndLabels(inputRegions, textLabels);

            console.log(`✅ Embedded detection found ${matchedFields.length} fields`);

            return {
                success: true,
                fields: matchedFields,
                method: 'embedded_screenshot',
                confidence: this.calculateConfidence(matchedFields)
            };

        } catch (error) {
            console.error('❌ Embedded detection failed:', error);
            return {
                success: false,
                error: error.message,
                fields: []
            };
        }
    }

    async findInputRegions() {
        const regions = [];

        console.log('🔍 Starting comprehensive input region scan...');

        // 1. 扫描主文档
        console.log('📄 Scanning main document...');
        const mainDocRegions = await this.scanDocumentForInputs(document);
        regions.push(...mainDocRegions);

        // 2. 扫描所有iframe
        console.log('🖼️ Scanning iframes...');
        const iframes = document.querySelectorAll('iframe');
        console.log(`Found ${iframes.length} iframes`);

        for (let i = 0; i < iframes.length; i++) {
            try {
                const iframe = iframes[i];
                console.log(`Scanning iframe ${i + 1}: ${iframe.src || 'about:blank'}`);

                // 尝试访问iframe内容
                if (iframe.contentDocument) {
                    const iframeRegions = await this.scanDocumentForInputs(iframe.contentDocument);
                    console.log(`Iframe ${i + 1} found ${iframeRegions.length} regions`);
                    regions.push(...iframeRegions);
                } else {
                    console.log(`Cannot access iframe ${i + 1} content (cross-origin)`);
                }
            } catch (error) {
                console.warn(`Error scanning iframe ${i + 1}:`, error);
            }
        }

        // 3. 扫描Shadow DOM
        console.log('👤 Scanning Shadow DOM...');
        const shadowRegions = await this.scanShadowDOMs();
        regions.push(...shadowRegions);

        // 4. 等待动态内容加载
        console.log('⏳ Waiting for dynamic content...');
        await this.waitForDynamicContent();

        // 5. 再次扫描（捕获动态加载的内容）
        console.log('🔄 Re-scanning for dynamic content...');
        const dynamicRegions = await this.scanDocumentForInputs(document);

        // 合并结果并去重
        const allRegions = [...regions, ...dynamicRegions];
        const uniqueRegions = this.deduplicateRegions(allRegions);

        console.log(`📦 Total found ${uniqueRegions.length} unique input regions`);
        return uniqueRegions;
    }

    async scanDocumentForInputs(doc) {
        const regions = [];

        // 更全面的选择器
        const inputSelectors = [
            // 标准HTML控件
            'input[type="text"]', 'input[type="email"]', 'input[type="tel"]', 'input[type="number"]',
            'input[type="date"]', 'input[type="url"]', 'input[type="search"]', 'input:not([type])',
            'textarea', 'select',

            // 可编辑元素
            '[contenteditable="true"]', '[contenteditable=""]',

            // 常见UI框架
            '.ant-input', '.ant-input-affix-wrapper input', '.el-input__inner', '.form-control',
            '.layui-input', '.ivu-input', '.van-field__control', '.weui-input', '.mui-input',

            // 通用模式
            '[class*="input"]:not(div):not(span):not(label)',
            '[class*="field"]:not(div):not(span):not(label)',
            '[class*="text"]:not(div):not(span):not(label)',

            // 属性识别
            '[role="textbox"]', '[role="combobox"]', '[role="spinbutton"]',
            '[data-field]', '[data-name]', '[name]:not([type="hidden"])',

            // 更激进的搜索
            'input', // 所有input元素
        ];

        const foundElements = new Set();

        for (const selector of inputSelectors) {
            try {
                const elements = doc.querySelectorAll(selector);
                console.log(`  Selector "${selector}" found ${elements.length} elements in document`);

                elements.forEach(element => {
                    if (!foundElements.has(element) && this.isValidElement(element)) {
                        const rect = element.getBoundingClientRect();

                        // 更宽松的尺寸检查
                        if (rect.width > 5 && rect.height > 5) {
                            regions.push({
                                x: rect.left + window.scrollX,
                                y: rect.top + window.scrollY,
                                width: rect.width,
                                height: rect.height,
                                element: element,
                                score: 0.8,
                                selector: selector,
                                document: doc === document ? 'main' : 'iframe'
                            });
                            foundElements.add(element);
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with selector ${selector}:`, error);
            }
        }

        return regions;
    }

    async scanShadowDOMs() {
        const regions = [];

        // 查找所有可能包含Shadow DOM的元素
        const allElements = document.querySelectorAll('*');

        for (const element of allElements) {
            if (element.shadowRoot) {
                console.log('Found Shadow DOM in:', element.tagName);
                try {
                    const shadowRegions = await this.scanDocumentForInputs(element.shadowRoot);
                    regions.push(...shadowRegions);
                } catch (error) {
                    console.warn('Error scanning Shadow DOM:', error);
                }
            }
        }

        return regions;
    }

    async waitForDynamicContent() {
        // 等待可能的动态内容加载
        return new Promise(resolve => {
            let attempts = 0;
            const maxAttempts = 10;

            const checkForNewContent = () => {
                attempts++;

                // 检查是否有新的输入元素出现
                const currentInputs = document.querySelectorAll('input, textarea, select').length;

                if (attempts >= maxAttempts) {
                    console.log('Dynamic content wait completed');
                    resolve();
                } else {
                    setTimeout(checkForNewContent, 200);
                }
            };

            checkForNewContent();
        });
    }

    deduplicateRegions(regions) {
        const unique = [];
        const threshold = 10; // 位置差异阈值

        for (const region of regions) {
            const isDuplicate = unique.some(existing => {
                const xDiff = Math.abs(existing.x - region.x);
                const yDiff = Math.abs(existing.y - region.y);
                return xDiff < threshold && yDiff < threshold;
            });

            if (!isDuplicate) {
                unique.push(region);
            }
        }

        return unique;
    }

    isValidElement(element) {
        if (!element || !element.isConnected) {
            console.log(`  ❌ Element not connected: ${element?.tagName}`);
            return false;
        }

        // 只排除明确不需要的类型
        const excludeTypes = ['hidden', 'button', 'submit', 'reset', 'image'];
        if (excludeTypes.includes(element.type)) {
            console.log(`  ❌ Excluded type: ${element.type}`);
            return false;
        }

        // 非常宽松的可见性检查
        try {
            const style = window.getComputedStyle(element);
            if (style.display === 'none') {
                console.log(`  ❌ Display none: ${element.tagName}#${element.id}`);
                return false;
            }

            // 检查元素是否在视口内或附近
            const rect = element.getBoundingClientRect();
            if (rect.width === 0 && rect.height === 0) {
                console.log(`  ❌ Zero size: ${element.tagName}#${element.id}`);
                return false;
            }

            // 允许不在当前视口但存在的元素
            console.log(`  ✅ Valid element: ${element.tagName}#${element.id || element.name || element.className.split(' ')[0]}`);
            return true;

        } catch (error) {
            console.log(`  ⚠️ Style check error for ${element.tagName}: ${error.message}`);
            // 如果无法获取样式，仍然认为是有效的
            return true;
        }
    }

    async findTextLabels() {
        const labels = [];

        const textSelectors = [
            'label', 'span', 'div', 'td', 'th', 'p', 'strong', 'b',
            '.form-label', '.field-label', '.label', '.ant-form-item-label'
        ];

        for (const selector of textSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const text = element.textContent.trim();
                    if (text && text.length > 1 && text.length < 100) {
                        // 过滤掉纯数字和特殊字符
                        if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                            const rect = element.getBoundingClientRect();
                            if (rect.width > 0 && rect.height > 0) {
                                labels.push({
                                    text: this.cleanText(text),
                                    x: rect.left + window.scrollX,
                                    y: rect.top + window.scrollY,
                                    width: rect.width,
                                    height: rect.height,
                                    element: element
                                });
                            }
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with text selector ${selector}:`, error);
            }
        }

        console.log(`📝 Found ${labels.length} text labels`);
        return labels;
    }

    cleanText(text) {
        return text.replace(/[：:*]/g, '').replace(/\s+/g, ' ').trim();
    }

    async matchFieldsAndLabels(inputRegions, textLabels) {
        const matchedFields = [];

        for (const region of inputRegions) {
            const nearbyLabels = this.findNearbyLabels(region, textLabels);

            let fieldType = 'UNKNOWN';
            let confidence = 0.3;
            let labelText = 'Unknown';

            if (nearbyLabels.length > 0) {
                const bestLabel = nearbyLabels[0];
                const typeGuess = this.guessFieldType(bestLabel.text);
                fieldType = typeGuess.type;
                confidence = typeGuess.confidence * region.score;
                labelText = bestLabel.text;
            }

            // 也尝试从元素属性中猜测类型
            const attrGuess = this.guessFieldTypeFromElement(region.element);
            if (attrGuess.confidence > confidence) {
                fieldType = attrGuess.type;
                confidence = attrGuess.confidence;
            }

            matchedFields.push({
                element: region.element,
                fieldType: fieldType,
                confidence: confidence,
                coordinates: {
                    x: region.x,
                    y: region.y,
                    width: region.width,
                    height: region.height
                },
                label: labelText,
                method: 'embedded_screenshot',
                selector: region.selector
            });
        }

        console.log(`🔗 Matched ${matchedFields.length} fields`);
        return matchedFields;
    }

    findNearbyLabels(region, textLabels) {
        const maxDistance = 300; // 增加搜索范围
        const nearbyLabels = [];

        for (const label of textLabels) {
            const distance = this.calculateDistance(region, label);
            if (distance < maxDistance) {
                nearbyLabels.push({
                    ...label,
                    distance: distance
                });
            }
        }

        return nearbyLabels.sort((a, b) => a.distance - b.distance);
    }

    calculateDistance(region, label) {
        const regionCenterX = region.x + region.width / 2;
        const regionCenterY = region.y + region.height / 2;
        const labelCenterX = label.x + label.width / 2;
        const labelCenterY = label.y + label.height / 2;

        return Math.sqrt(
            Math.pow(regionCenterX - labelCenterX, 2) +
            Math.pow(regionCenterY - labelCenterY, 2)
        );
    }

    guessFieldType(labelText) {
        const fieldPatterns = {
            'BL_NO': ['提单号', '提单', 'bl', 'bill', 'lading', '单号'],
            'SHIPPER': ['发货人', '托运人', 'shipper', 'consignor', '发货', '客户名', '客户'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver', '收货', '联系人'],
            'PORT_OF_LOADING': ['起运港', '装货港', 'pol', 'loading', '起运', '装运港'],
            'PORT_OF_DISCHARGE': ['目的港', '卸货港', 'pod', 'discharge', '目的', '到港'],
            'VESSEL_VOYAGE': ['船名', '航次', 'vessel', 'voyage', '船', '航班'],
            'CONTAINER_NO': ['集装箱', '箱号', 'container', 'cntr', '集装', '柜号'],
            'INVOICE_NO': ['发票号', '发票', 'invoice', 'inv', '账单'],
            'TOTAL_AMOUNT': ['金额', '总额', 'amount', 'total', 'value', '价格', '费用'],
            'DATE': ['日期', 'date', '时间', '开船', '到港', 'etd', 'eta'],
            'QUANTITY': ['数量', '件数', 'quantity', 'pieces', 'pkgs', '件', '包装'],
            'WEIGHT': ['重量', '毛重', 'weight', 'gross', '净重', 'kg'],
            'VOLUME': ['体积', '立方', 'volume', 'cbm', '尺寸'],
            'COMPANY': ['公司', 'company', '企业'],
            'CONTACT': ['联系', 'contact', '电话', 'phone', 'tel'],
            'ADDRESS': ['地址', 'address', '住址'],
            'EMAIL': ['邮箱', 'email', 'mail'],
            'REMARK': ['备注', 'remark', '说明', 'note', '注释'],
            'GOODS': ['货物', 'goods', '商品', '品名', '货品'],
            'PACKAGE': ['包装', 'package', '包装方式', '装'],
            'TERMS': ['条款', 'terms', '贸易条款']
        };

        const text = labelText.toLowerCase();

        // 精确匹配优先
        for (const [fieldType, keywords] of Object.entries(fieldPatterns)) {
            for (const keyword of keywords) {
                if (text === keyword.toLowerCase()) {
                    return { type: fieldType, confidence: 0.95 };
                }
            }
        }

        // 包含匹配
        for (const [fieldType, keywords] of Object.entries(fieldPatterns)) {
            for (const keyword of keywords) {
                if (text.includes(keyword.toLowerCase())) {
                    return { type: fieldType, confidence: 0.8 };
                }
            }
        }

        // 特殊模式匹配
        if (/\d+/.test(text) && text.length < 10) {
            return { type: 'NUMBER_FIELD', confidence: 0.6 };
        }

        if (text.includes('选择') || text.includes('请选择')) {
            return { type: 'SELECT_FIELD', confidence: 0.7 };
        }

        return { type: 'UNKNOWN', confidence: 0.3 };
    }

    guessFieldTypeFromElement(element) {
        const elementText = [
            element.id || '',
            element.name || '',
            element.className || '',
            element.placeholder || '',
            element.title || ''
        ].join(' ').toLowerCase();

        const fieldPatterns = {
            'BL_NO': ['bl', 'bill', 'lading'],
            'SHIPPER': ['shipper', 'consignor'],
            'CONSIGNEE': ['consignee', 'receiver'],
            'PORT_OF_LOADING': ['pol', 'loading'],
            'PORT_OF_DISCHARGE': ['pod', 'discharge'],
            'VESSEL_VOYAGE': ['vessel', 'voyage'],
            'CONTAINER_NO': ['container', 'cntr'],
            'INVOICE_NO': ['invoice', 'inv'],
            'TOTAL_AMOUNT': ['amount', 'total', 'value']
        };

        for (const [fieldType, keywords] of Object.entries(fieldPatterns)) {
            for (const keyword of keywords) {
                if (elementText.includes(keyword)) {
                    return { type: fieldType, confidence: 0.6 };
                }
            }
        }

        return { type: 'UNKNOWN', confidence: 0.2 };
    }

    calculateConfidence(fields) {
        if (fields.length === 0) return 0;

        const totalConfidence = fields.reduce((sum, field) => sum + field.confidence, 0);
        return totalConfidence / fields.length;
    }
}

class FreightParserContent {
    constructor() {
        this.formFields = [];
        this.isInitialized = false;
        this.detectionMethod = 'screenshot'; // 'dom', 'vision', 'ml', 'screenshot'

        // 高亮相关属性
        this.highlightedElements = [];
        this.autoCleanupTimer = null;

        // 初始化检测器
        this.visionDetector = null;
        this.mlDetector = null;
        this.screenshotDetector = null;

        this.init();
    }

    async init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 初始化检测器
        await this.initDetectors();

        // 页面加载完成后扫描表单
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.scanPage());
        } else {
            this.scanPage();
        }

        this.isInitialized = true;
        console.log('FreightParser Content Script initialized with', this.detectionMethod, 'detection');
    }

    /**
     * 初始化检测器
     */
    async initDetectors() {
        try {
            console.log('Initializing detectors with method:', this.detectionMethod);

            if (this.detectionMethod === 'vision') {
                // 检查是否已经加载了VisionFieldDetector
                if (typeof VisionFieldDetector === 'undefined') {
                    console.log('Loading vision detector...');
                    // 优先尝试简化版检测器
                    await this.loadDetectorScript('vision-field-detector-simple.js');
                }

                if (typeof VisionFieldDetector !== 'undefined') {
                    this.visionDetector = new VisionFieldDetector();
                    console.log('Vision detector initialized');
                } else {
                    throw new Error('VisionFieldDetector not available');
                }

            } else if (this.detectionMethod === 'ml') {
                // 检查是否已经加载了MLFieldDetector
                if (typeof MLFieldDetector === 'undefined') {
                    console.log('Loading ML detector...');
                    await this.loadDetectorScript('ml-field-detector.js');
                }

                if (typeof MLFieldDetector !== 'undefined') {
                    this.mlDetector = new MLFieldDetector();
                    console.log('ML detector initialized');
                } else {
                    throw new Error('MLFieldDetector not available');
                }
            } else if (this.detectionMethod === 'screenshot') {
                // 检查是否已经加载了ScreenshotFieldDetector
                if (typeof ScreenshotFieldDetector === 'undefined') {
                    console.log('Loading screenshot detector...');
                    await this.loadDetectorScript('screenshot-detector.js');
                }

                if (typeof ScreenshotFieldDetector !== 'undefined') {
                    this.screenshotDetector = new ScreenshotFieldDetector();
                    console.log('Screenshot detector initialized');
                } else {
                    throw new Error('ScreenshotFieldDetector not available');
                }
            }

            console.log('Detectors initialization completed');

        } catch (error) {
            console.error('Failed to initialize detectors:', error);
            console.log('Falling back to DOM detection method');
            // 降级到DOM检测
            this.detectionMethod = 'dom';
        }
    }

    /**
     * 加载检测器脚本
     */
    async loadDetectorScript(scriptName) {
        return new Promise((resolve, reject) => {
            // 检查脚本是否已经存在
            const existingScript = document.querySelector(`script[src*="${scriptName}"]`);
            if (existingScript) {
                resolve();
                return;
            }

            const script = document.createElement('script');

            // 尝试不同的路径
            const possiblePaths = [
                chrome.runtime.getURL(scriptName),
                chrome.runtime.getURL(`js/${scriptName}`),
                chrome.runtime.getURL(`scripts/${scriptName}`)
            ];

            let pathIndex = 0;

            const tryLoadScript = () => {
                if (pathIndex >= possiblePaths.length) {
                    reject(new Error(`Failed to load ${scriptName} from all possible paths`));
                    return;
                }

                script.src = possiblePaths[pathIndex];
                console.log(`Trying to load script from: ${script.src}`);

                script.onload = () => {
                    console.log(`Successfully loaded: ${script.src}`);
                    resolve();
                };

                script.onerror = (error) => {
                    console.warn(`Failed to load from ${script.src}, trying next path...`);
                    pathIndex++;
                    tryLoadScript();
                };

                document.head.appendChild(script);
            };

            tryLoadScript();
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'ping':
                    sendResponse({ success: true, ready: true });
                    break;

                case 'scanPage':
                    const scanResult = this.scanPage();
                    sendResponse(scanResult);
                    break;

                case 'fillForm':
                    const fillResult = await this.fillForm(request.data);
                    sendResponse(fillResult);
                    break;

                case 'highlightFields':
                    this.highlightFields();
                    sendResponse({ success: true });
                    break;

                case 'clearHighlights':
                    this.clearHighlights();
                    sendResponse({ success: true });
                    break;

                case 'debugFields':
                    const debugResult = this.debugFields();
                    sendResponse(debugResult);
                    break;

                case 'analyzePageElements':
                    const analysisResult = this.analyzePageElements();
                    sendResponse(analysisResult);
                    break;

                case 'forceScreenshotDetection':
                    const screenshotResult = await this.forceScreenshotDetection();
                    sendResponse(screenshotResult);
                    break;

                case 'deepDebug':
                    const deepDebugResult = await this.performDeepDebug();
                    sendResponse(deepDebugResult);
                    break;

                case 'aggressiveScan':
                    const aggressiveResult = await this.performAggressiveScan();
                    sendResponse(aggressiveResult);
                    break;

                case 'highlightAllFields':
                    const highlightResult = await this.highlightAllFields();
                    sendResponse(highlightResult);
                    break;

                case 'generateFieldReport':
                    const reportResult = await this.generateFieldReport();
                    sendResponse(reportResult);
                    break;

                case 'highlightField':
                    this.highlightSingleField(request.fieldIndex);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action: ' + request.action });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * 扫描页面表单和字段
     */
    async scanPage() {
        console.log('Scanning page for forms and fields using', this.detectionMethod, 'method...');

        // 清除之前的高亮
        this.clearHighlights();

        let result;

        try {
            if (this.detectionMethod === 'vision' && this.visionDetector) {
                result = await this.scanWithVision();
            } else if (this.detectionMethod === 'ml' && this.mlDetector) {
                result = await this.scanWithML();
            } else if (this.detectionMethod === 'screenshot' && this.screenshotDetector) {
                result = await this.scanWithScreenshot();
            } else {
                result = await this.scanWithDOM();
            }
        } catch (error) {
            console.error('Advanced scanning failed, falling back to DOM:', error);
            result = await this.scanWithDOM();
        }

        console.log('Page scan result:', result);
        return result;
    }

    /**
     * 使用视觉检测扫描
     */
    async scanWithVision() {
        const visionResult = await this.visionDetector.detectFields();

        if (visionResult.success) {
            this.formFields = visionResult.fields;
            return {
                formCount: 1, // 视觉检测不区分表单
                fieldCount: visionResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'vision',
                confidence: visionResult.confidence
            };
        } else {
            throw new Error('Vision detection failed: ' + visionResult.error);
        }
    }

    /**
     * 使用机器学习扫描
     */
    async scanWithML() {
        const mlResult = await this.mlDetector.detectFields();

        if (mlResult.success) {
            this.formFields = mlResult.fields;
            return {
                formCount: 1,
                fieldCount: mlResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'ml',
                confidence: mlResult.confidence
            };
        } else {
            throw new Error('ML detection failed: ' + mlResult.error);
        }
    }

    /**
     * 使用截图扫描
     */
    async scanWithScreenshot() {
        // 使用内嵌的检测器
        const detector = new EmbeddedScreenshotDetector();
        const screenshotResult = await detector.detectFields();

        if (screenshotResult.success) {
            this.formFields = screenshotResult.fields;
            return {
                formCount: 1,
                fieldCount: screenshotResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'embedded_screenshot',
                confidence: screenshotResult.confidence
            };
        } else {
            throw new Error('Screenshot detection failed: ' + screenshotResult.error);
        }
    }

    /**
     * 使用DOM扫描（原有方法）
     */
    async scanWithDOM() {
        // 扫描表单
        const forms = document.querySelectorAll('form');

        // 扫描所有可填写的字段
        this.formFields = this.scanFormFields();

        return {
            formCount: forms.length,
            fieldCount: this.formFields.length,
            url: window.location.href,
            title: document.title,
            method: 'dom'
        };
    }

    /**
     * 扫描表单字段
     */
    scanFormFields() {
        console.log('Starting comprehensive field scan...');
        const fields = [];

        // 第一阶段：标准选择器
        const standardSelectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="tel"]',
            'input[type="number"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'input[type="time"]',
            'input[type="url"]',
            'input[type="search"]',
            'input:not([type])',
            'textarea',
            'select'
        ];

        // 第二阶段：框架特定选择器
        const frameworkSelectors = [
            '[contenteditable="true"]',
            '.ant-input',
            '.ant-input-affix-wrapper input',
            '.el-input__inner',
            '.form-control',
            '.layui-input',
            '.ivu-input',
            '.van-field__control',
            '.weui-input',
            '.mui-input'
        ];

        // 第三阶段：通用模式选择器
        const patternSelectors = [
            '[class*="input"]:not(div):not(span)',
            '[class*="field"]:not(div):not(span)',
            '[class*="text"]:not(div):not(span)',
            '[class*="edit"]:not(div):not(span)',
            '[role="textbox"]',
            '[role="combobox"]',
            '[role="spinbutton"]',
            '[data-field]',
            '[data-name]',
            '[name]:not([type="hidden"]):not([type="button"]):not([type="submit"])'
        ];

        // 第四阶段：深度扫描
        const deepSelectors = [
            'input',
            'textarea',
            'select',
            '[contenteditable]'
        ];

        const foundElements = new Set();
        let scanStats = {
            standard: 0,
            framework: 0,
            pattern: 0,
            deep: 0,
            total: 0
        };

        // 执行分阶段扫描
        this.executeScanPhase('Standard', standardSelectors, fields, foundElements, scanStats, 'standard');
        this.executeScanPhase('Framework', frameworkSelectors, fields, foundElements, scanStats, 'framework');
        this.executeScanPhase('Pattern', patternSelectors, fields, foundElements, scanStats, 'pattern');
        this.executeScanPhase('Deep', deepSelectors, fields, foundElements, scanStats, 'deep');

        scanStats.total = fields.length;
        console.log('Scan statistics:', scanStats);
        console.log(`Total found ${fields.length} form fields`);

        // 如果还是没找到足够的字段，尝试更激进的扫描
        if (fields.length < 5) {
            console.log('Low field count, trying aggressive scan...');
            this.aggressiveScan(fields, foundElements);
        }

        return fields;
    }

    /**
     * 执行扫描阶段
     */
    executeScanPhase(phaseName, selectors, fields, foundElements, stats, statKey) {
        console.log(`Executing ${phaseName} scan phase...`);
        let phaseCount = 0;

        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`Selector "${selector}" found ${elements.length} elements`);

                elements.forEach(element => {
                    if (!foundElements.has(element)) {
                        const isValid = this.isValidFormField(element);
                        console.log(`Element ${element.tagName}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ')[0] : ''} - Valid: ${isValid}`);

                        if (isValid) {
                            const fieldInfo = this.extractFieldInfo(element);
                            if (fieldInfo) {
                                fields.push(fieldInfo);
                                foundElements.add(element);
                                phaseCount++;
                            }
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with selector "${selector}":`, error);
            }
        });

        stats[statKey] = phaseCount;
        console.log(`${phaseName} phase found ${phaseCount} fields`);
    }

    /**
     * 激进扫描模式
     */
    aggressiveScan(fields, foundElements) {
        console.log('Starting aggressive scan...');

        // 扫描所有可能的输入元素，降低验证标准
        const allElements = document.querySelectorAll('*');
        let aggressiveCount = 0;

        allElements.forEach(element => {
            if (foundElements.has(element)) return;

            // 更宽松的检查条件
            if (this.couldBeInputField(element)) {
                const fieldInfo = this.extractFieldInfo(element);
                if (fieldInfo) {
                    fieldInfo.scanMethod = 'aggressive';
                    fields.push(fieldInfo);
                    foundElements.add(element);
                    aggressiveCount++;
                }
            }
        });

        console.log(`Aggressive scan found ${aggressiveCount} additional fields`);
    }

    /**
     * 宽松的输入字段检查
     */
    couldBeInputField(element) {
        const tagName = element.tagName.toLowerCase();

        // 基本标签检查
        if (['input', 'textarea', 'select'].includes(tagName)) {
            return element.type !== 'hidden' && element.type !== 'button' && element.type !== 'submit';
        }

        // 可编辑元素
        if (element.contentEditable === 'true') {
            return true;
        }

        // 通过属性判断
        if (element.hasAttribute('name') || element.hasAttribute('data-field')) {
            return true;
        }

        // 通过class名称判断
        const className = element.className.toLowerCase();
        const inputKeywords = ['input', 'field', 'text', 'edit', 'form'];
        if (inputKeywords.some(keyword => className.includes(keyword))) {
            // 排除明显的非输入元素
            if (!['div', 'span', 'p', 'label'].includes(tagName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为有效的表单字段
     */
    isValidFormField(element) {
        console.log(`Checking element: ${element.tagName}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ')[0] : ''}`);

        // 基本检查
        if (!element || !element.isConnected) {
            console.log('  ❌ Element not connected to DOM');
            return false;
        }

        // 排除明显的隐藏字段
        if (element.type === 'hidden') {
            console.log('  ❌ Hidden input type');
            return false;
        }

        // 排除按钮类型
        const excludeTypes = ['button', 'submit', 'reset', 'image', 'file'];
        if (excludeTypes.includes(element.type)) {
            console.log(`  ❌ Excluded type: ${element.type}`);
            return false;
        }

        // 获取计算样式
        let style;
        try {
            style = window.getComputedStyle(element);
        } catch (error) {
            console.log('  ⚠️ Cannot get computed style');
            style = {};
        }

        // 检查可见性（更宽松的检查）
        if (style.display === 'none') {
            console.log('  ❌ Display none');
            return false;
        }

        // 检查元素尺寸（更宽松的检查）
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 && rect.height === 0) {
            console.log('  ❌ Zero dimensions');
            return false;
        }

        // 检查是否为可交互元素
        const tagName = element.tagName.toLowerCase();
        const isStandardInput = ['input', 'textarea', 'select'].includes(tagName);
        const isContentEditable = element.contentEditable === 'true';
        const hasRole = element.hasAttribute('role');

        if (isStandardInput || isContentEditable || hasRole) {
            console.log(`  ✅ Valid interactive element (${tagName})`);
            return true;
        }

        // 对于非标准控件，检查属性和class
        const hasName = element.hasAttribute('name');
        const hasDataField = element.hasAttribute('data-field');
        const className = element.className.toLowerCase();
        const hasInputClass = [
            'input', 'field', 'form', 'text', 'edit', 'control'
        ].some(keyword => className.includes(keyword));

        if (hasName || hasDataField || hasInputClass) {
            console.log(`  ✅ Valid by attributes/class (name:${hasName}, data-field:${hasDataField}, class:${hasInputClass})`);
            return true;
        }

        console.log('  ❌ Not a valid form field');
        return false;
    }

    /**
     * 提取字段信息
     */
    extractFieldInfo(element) {
        const info = {
            element: element,
            id: element.id || '',
            name: element.name || '',
            type: element.type || element.tagName.toLowerCase(),
            tagName: element.tagName.toLowerCase(),
            placeholder: element.placeholder || '',
            value: this.getElementValue(element),
            className: element.className || '',
            label: this.findFieldLabel(element),
            xpath: this.getElementXPath(element),
            // 添加更多属性
            dataField: element.getAttribute('data-field') || '',
            dataName: element.getAttribute('data-name') || '',
            title: element.title || '',
            ariaLabel: element.getAttribute('aria-label') || '',
            role: element.getAttribute('role') || ''
        };

        // 生成字段的文本描述（用于匹配）
        info.textContent = [
            info.id,
            info.name,
            info.label,
            info.placeholder,
            info.className,
            info.dataField,
            info.dataName,
            info.title,
            info.ariaLabel
        ].filter(Boolean).join(' ').toLowerCase();

        // 添加调试信息
        console.log('Extracted field info:', {
            tag: info.tagName,
            id: info.id,
            name: info.name,
            label: info.label,
            className: info.className
        });

        return info;
    }

    /**
     * 获取元素的值
     */
    getElementValue(element) {
        if (element.tagName.toLowerCase() === 'select') {
            return element.selectedOptions.length > 0 ? element.selectedOptions[0].text : '';
        } else if (element.contentEditable === 'true') {
            return element.textContent || element.innerText || '';
        } else {
            return element.value || '';
        }
    }

    /**
     * 查找字段标签
     */
    findFieldLabel(element) {
        let label = '';

        // 1. 通过for属性关联的label
        if (element.id) {
            const labelElement = document.querySelector(`label[for="${element.id}"]`);
            if (labelElement) {
                label = labelElement.textContent.trim();
                if (label) return this.cleanLabelText(label);
            }
        }

        // 2. 父级label
        const parentLabel = element.closest('label');
        if (parentLabel) {
            label = parentLabel.textContent.replace(element.value || '', '').trim();
            if (label) return this.cleanLabelText(label);
        }

        // 3. aria-label属性
        if (element.getAttribute('aria-label')) {
            return this.cleanLabelText(element.getAttribute('aria-label'));
        }

        // 4. title属性
        if (element.title) {
            return this.cleanLabelText(element.title);
        }

        // 5. placeholder作为标签
        if (element.placeholder && element.placeholder.length < 30) {
            return this.cleanLabelText(element.placeholder);
        }

        // 6. 查找同一行或附近的文本标签
        label = this.findNearbyLabel(element);
        if (label) return this.cleanLabelText(label);

        // 7. 查找表格中的表头
        label = this.findTableHeader(element);
        if (label) return this.cleanLabelText(label);

        // 8. 查找父级容器中的标签文本
        label = this.findContainerLabel(element);
        if (label) return this.cleanLabelText(label);

        return '';
    }

    /**
     * 清理标签文本
     */
    cleanLabelText(text) {
        return text
            .replace(/[：:*]/g, '') // 移除冒号和星号
            .replace(/\s+/g, ' ') // 合并空白字符
            .trim();
    }

    /**
     * 查找附近的标签
     */
    findNearbyLabel(element) {
        // 查找前面的兄弟元素
        let prev = element.previousElementSibling;
        let attempts = 0;
        while (prev && attempts < 3) {
            const text = prev.textContent?.trim();
            if (text && text.length < 50 && text.length > 1) {
                // 排除纯数字或特殊字符
                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                    return text;
                }
            }
            prev = prev.previousElementSibling;
            attempts++;
        }

        // 查找后面的兄弟元素（某些布局中标签在后面）
        let next = element.nextElementSibling;
        attempts = 0;
        while (next && attempts < 2) {
            const text = next.textContent?.trim();
            if (text && text.length < 30 && text.length > 1) {
                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                    return text;
                }
            }
            next = next.nextElementSibling;
            attempts++;
        }

        return '';
    }

    /**
     * 查找表格表头
     */
    findTableHeader(element) {
        const cell = element.closest('td, th');
        if (!cell) return '';

        const table = cell.closest('table');
        if (!table) return '';

        const cellIndex = Array.from(cell.parentNode.children).indexOf(cell);
        const headerRow = table.querySelector('thead tr, tr:first-child');

        if (headerRow) {
            const headerCell = headerRow.children[cellIndex];
            if (headerCell) {
                const text = headerCell.textContent?.trim();
                if (text && text.length < 50) {
                    return text;
                }
            }
        }

        return '';
    }

    /**
     * 查找容器标签
     */
    findContainerLabel(element) {
        // 查找父级容器中的标签文本
        let parent = element.parentElement;
        let level = 0;

        while (parent && level < 3) {
            // 查找容器中的直接文本节点
            const textNodes = this.getDirectTextNodes(parent);
            for (const node of textNodes) {
                const text = node.textContent?.trim();
                if (text && text.length < 50 && text.length > 1) {
                    if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                        return text;
                    }
                }
            }

            parent = parent.parentElement;
            level++;
        }

        return '';
    }

    /**
     * 获取直接文本节点（不包括子元素的文本）
     */
    getDirectTextNodes(element) {
        const textNodes = [];
        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        return textNodes;
    }

    /**
     * 获取元素的XPath
     */
    getElementXPath(element) {
        if (element.id) {
            return `//*[@id="${element.id}"]`;
        }
        
        const parts = [];
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let index = 0;
            let sibling = element.previousSibling;
            
            while (sibling) {
                if (sibling.nodeType === Node.ELEMENT_NODE && sibling.tagName === element.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            const tagName = element.tagName.toLowerCase();
            const pathIndex = index > 0 ? `[${index + 1}]` : '';
            parts.unshift(`${tagName}${pathIndex}`);
            
            element = element.parentNode;
        }
        
        return parts.length ? '/' + parts.join('/') : '';
    }

    /**
     * 获取元素中的文本节点
     */
    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        
        return textNodes;
    }

    /**
     * 智能回填表单
     */
    async fillForm(parsedData) {
        console.log('Starting intelligent form fill with data:', parsedData);
        
        if (!parsedData || Object.keys(parsedData).length === 0) {
            throw new Error('没有可用的解析数据');
        }

        // 重新扫描页面（可能有动态字段）
        this.formFields = this.scanFormFields();
        
        const filledFields = [];
        const fieldMapping = this.getFieldMapping();

        // 遍历解析出的数据
        for (const [dataKey, dataValue] of Object.entries(parsedData)) {
            if (!dataValue || dataValue.toString().trim() === '') {
                continue;
            }

            // 查找匹配的字段
            const matchedField = this.findMatchingField(dataKey, dataValue, fieldMapping);
            
            if (matchedField) {
                try {
                    await this.fillField(matchedField, dataValue);
                    filledFields.push({
                        dataKey,
                        dataValue,
                        fieldInfo: {
                            id: matchedField.id,
                            name: matchedField.name,
                            label: matchedField.label
                        }
                    });
                } catch (error) {
                    console.error(`Failed to fill field ${dataKey}:`, error);
                }
            } else {
                console.warn(`No matching field found for ${dataKey}: ${dataValue}`);
            }
        }

        console.log(`Form fill completed. Filled ${filledFields.length} fields:`, filledFields);

        return {
            success: true,
            filledCount: filledFields.length,
            filledFields: filledFields,
            totalDataFields: Object.keys(parsedData).length
        };
    }

    /**
     * 获取字段映射配置
     */
    getFieldMapping() {
        return {
            // 提单相关
            'BL_NO': ['提单号', 'bill', 'bl', 'blno', 'bill_number', 'bl_number'],
            'SHIPPER': ['发货人', 'shipper', 'consignor', 'sender', 'from'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver', 'to'],
            'NOTIFY_PARTY': ['通知方', 'notify', 'notify_party'],
            'PORT_OF_LOADING': ['起运港', 'pol', 'loading', 'origin', 'from_port'],
            'PORT_OF_DISCHARGE': ['目的港', 'pod', 'discharge', 'destination', 'to_port'],
            'VESSEL_VOYAGE': ['船名', 'vessel', 'voyage', 'ship'],
            'CONTAINER_NO': ['集装箱', 'container', 'cntr', 'box'],
            'SEAL_NO': ['封条', 'seal'],
            'GOODS_DESCRIPTION': ['货物', 'goods', 'cargo', 'commodity', 'description'],
            'PACKAGES': ['件数', 'packages', 'pkgs', 'pieces'],
            'GROSS_WEIGHT': ['毛重', 'weight', 'gross'],
            'MEASUREMENT': ['体积', 'volume', 'cbm', 'measurement'],
            'ETD': ['开船', 'etd', 'departure'],
            'ETA': ['到港', 'eta', 'arrival'],
            'FREIGHT_TERMS': ['运费', 'freight', 'payment'],

            // 发票相关
            'INVOICE_NO': ['发票号', 'invoice', 'inv'],
            'INVOICE_DATE': ['发票日期', 'invoice_date', 'date'],
            'TOTAL_AMOUNT': ['金额', 'amount', 'total', 'value'],
            'CURRENCY': ['币种', 'currency'],
            'SELLER': ['卖方', 'seller', 'vendor'],
            'BUYER': ['买方', 'buyer', 'customer'],
            'TRADE_TERMS': ['贸易条款', 'terms', 'incoterms'],

            // 装箱单相关
            'PACKING_LIST_NO': ['装箱单', 'packing', 'pl'],
            'TOTAL_PACKAGES': ['总件数', 'total_packages'],
            'TOTAL_GROSS_WEIGHT': ['总毛重', 'total_weight'],
            'TOTAL_NET_WEIGHT': ['总净重', 'net_weight'],
            'TOTAL_VOLUME': ['总体积', 'total_volume']
        };
    }

    /**
     * 查找匹配的字段
     */
    findMatchingField(dataKey, dataValue, fieldMapping) {
        const aliases = fieldMapping[dataKey] || [dataKey.toLowerCase()];
        let bestMatch = null;
        let bestScore = 0;

        for (const field of this.formFields) {
            let score = 0;

            // 精确匹配字段名或ID
            if (field.name === dataKey || field.id === dataKey) {
                score += 100;
            }

            // 别名匹配
            for (const alias of aliases) {
                if (field.textContent.includes(alias)) {
                    score += 50;
                }
            }

            // 字段类型匹配
            if (this.isFieldTypeMatch(dataKey, dataValue, field)) {
                score += 20;
            }

            // 位置权重（表单中靠前的字段优先级更高）
            const formIndex = this.formFields.indexOf(field);
            score += Math.max(0, 10 - formIndex * 0.1);

            if (score > bestScore) {
                bestScore = score;
                bestMatch = field;
            }
        }

        // 只返回置信度足够高的匹配
        return bestScore > 30 ? bestMatch : null;
    }

    /**
     * 检查字段类型是否匹配
     */
    isFieldTypeMatch(dataKey, dataValue, field) {
        // 数字字段
        if (['PACKAGES', 'GROSS_WEIGHT', 'MEASUREMENT', 'TOTAL_AMOUNT'].includes(dataKey)) {
            return field.type === 'number' || /number|amount|weight|volume|quantity/i.test(field.textContent);
        }

        // 日期字段
        if (['ETD', 'ETA', 'INVOICE_DATE'].includes(dataKey)) {
            return field.type === 'date' || /date|time/i.test(field.textContent);
        }

        // 邮箱字段
        if (/email/i.test(dataKey)) {
            return field.type === 'email' || /email|mail/i.test(field.textContent);
        }

        // 长文本字段
        if (['SHIPPER', 'CONSIGNEE', 'GOODS_DESCRIPTION'].includes(dataKey)) {
            return field.tagName === 'textarea' || /address|description|detail/i.test(field.textContent);
        }

        return true;
    }

    /**
     * 填充字段
     */
    async fillField(field, value) {
        const element = field.element;
        
        // 聚焦字段
        element.focus();
        
        // 清空现有值
        element.value = '';
        
        // 设置新值
        if (field.tagName === 'select') {
            // 下拉框处理
            this.selectOption(element, value);
        } else {
            // 输入框处理
            element.value = value.toString();
        }
        
        // 触发事件
        this.triggerEvents(element);
        
        // 添加视觉效果
        this.addFillEffect(element);
        
        // 短暂延迟，模拟人工输入
        await this.sleep(100);
    }

    /**
     * 选择下拉框选项
     */
    selectOption(selectElement, value) {
        const options = selectElement.querySelectorAll('option');
        const valueStr = value.toString().toLowerCase();
        
        for (const option of options) {
            const optionText = option.textContent.toLowerCase();
            const optionValue = option.value.toLowerCase();
            
            if (optionValue === valueStr || 
                optionText === valueStr || 
                optionText.includes(valueStr) || 
                valueStr.includes(optionText)) {
                option.selected = true;
                selectElement.value = option.value;
                break;
            }
        }
    }

    /**
     * 触发相关事件
     */
    triggerEvents(element) {
        const events = ['input', 'change', 'blur', 'keyup'];
        
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            element.dispatchEvent(event);
        });
    }

    /**
     * 添加填充效果
     */
    addFillEffect(element) {
        const originalStyle = {
            backgroundColor: element.style.backgroundColor,
            borderColor: element.style.borderColor,
            transition: element.style.transition
        };
        
        // 添加高亮效果
        element.style.transition = 'all 0.3s ease';
        element.style.backgroundColor = '#e8f5e8';
        element.style.borderColor = '#28a745';
        
        // 3秒后恢复原样
        setTimeout(() => {
            element.style.backgroundColor = originalStyle.backgroundColor;
            element.style.borderColor = originalStyle.borderColor;
            element.style.transition = originalStyle.transition;
        }, 3000);
    }

    /**
     * 高亮显示所有可填写字段
     */
    highlightFields() {
        // 先清除之前的高亮
        this.clearHighlights();

        if (!this.formFields || this.formFields.length === 0) {
            console.log('❌ No fields to highlight');
            return;
        }

        console.log(`🎯 Highlighting ${this.formFields.length} fields using CSS stylesheet method...`);

        // 初始化追踪数组
        this.highlightedElements = [];

        // 创建CSS样式表
        this.createHighlightStyleSheet();

        this.formFields.forEach((field, index) => {
            try {
                const element = field.element;

                // 保存原始样式
                element.dataset.originalOutline = element.style.outline || '';
                element.dataset.originalOutlineOffset = element.style.outlineOffset || '';
                element.dataset.originalBoxShadow = element.style.boxShadow || '';

                // 添加高亮类和属性
                element.classList.add('freight-parser-highlighted');
                element.setAttribute('data-freight-field-index', index);

                // 记录元素以便后续清理
                this.highlightedElements.push(element);

                // 获取准确的标签文本
                const labelText = this.findAssociatedLabel(element) ||
                                field.label ||
                                field.fieldType ||
                                element.placeholder ||
                                element.name ||
                                element.id ||
                                '未知字段';

                // 创建标签
                this.createFieldLabel(element, labelText, index);

                console.log(`✅ Highlighted field ${index + 1}: ${labelText}`);
            } catch (error) {
                console.error(`❌ Failed to highlight field ${index}:`, error);
            }
        });

        // 8秒后自动清除高亮
        this.autoCleanupTimer = setTimeout(() => {
            this.clearHighlights();
            console.log('🧹 Auto-cleared highlights after 8 seconds');
        }, 8000);

        console.log('🎯 Field highlighting completed (will auto-clear in 8 seconds)');
    }

    /**
     * 创建高亮样式表
     */
    createHighlightStyleSheet() {
        const styleId = 'freight-parser-highlight-styles';

        // 移除旧的样式表
        const oldStyle = document.getElementById(styleId);
        if (oldStyle) {
            oldStyle.remove();
        }

        // 创建新的样式表
        const style = document.createElement('style');
        style.id = styleId;
        style.setAttribute('data-freight-parser-style', 'true');
        style.textContent = `
            .freight-parser-highlighted {
                outline: 2px solid #007bff !important;
                outline-offset: 1px !important;
                box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.3) !important;
            }

            .freight-parser-label {
                position: absolute !important;
                background: #007bff !important;
                color: white !important;
                padding: 2px 6px !important;
                border-radius: 3px !important;
                font-size: 11px !important;
                font-weight: bold !important;
                z-index: 10000 !important;
                pointer-events: none !important;
                white-space: nowrap !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
                font-family: Arial, sans-serif !important;
            }
        `;

        document.head.appendChild(style);
        console.log('✅ Created highlight stylesheet');
    }

    /**
     * 创建字段标签
     */
    createFieldLabel(element, labelText, index) {
        const label = document.createElement('div');
        label.className = 'freight-parser-label';
        label.textContent = labelText;
        label.setAttribute('data-field-index', index);

        // 计算标签位置
        const rect = element.getBoundingClientRect();
        label.style.left = (rect.left + window.scrollX - 2) + 'px';
        label.style.top = (rect.top + window.scrollY - 20) + 'px';

        document.body.appendChild(label);

        return label;
    }

    /**
     * 调试字段识别
     */
    debugFields() {
        console.log('Starting field debug...');

        // 重新扫描字段
        this.formFields = this.scanFormFields();

        // 返回详细的字段信息
        const debugInfo = {
            success: true,
            fields: this.formFields.map(field => ({
                tagName: field.tagName,
                type: field.type,
                id: field.id,
                name: field.name,
                className: field.className,
                label: field.label,
                placeholder: field.placeholder,
                value: field.value,
                textContent: field.textContent,
                xpath: field.xpath
            }))
        };

        console.log('Debug info:', debugInfo);
        return debugInfo;
    }

    /**
     * 高亮单个字段
     */
    highlightSingleField(fieldIndex) {
        if (fieldIndex >= 0 && fieldIndex < this.formFields.length) {
            const field = this.formFields[fieldIndex];
            const element = field.element;

            // 清除之前的高亮
            document.querySelectorAll('.freight-parser-debug-highlight').forEach(el => {
                el.classList.remove('freight-parser-debug-highlight');
            });

            // 高亮选中的字段
            element.classList.add('freight-parser-debug-highlight');
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 5秒后移除高亮
            setTimeout(() => {
                element.classList.remove('freight-parser-debug-highlight');
            }, 5000);
        }
    }

    /**
     * 分析页面元素
     */
    analyzePageElements() {
        console.log('Analyzing page elements...');

        const analysis = {
            totalElements: document.querySelectorAll('*').length,
            inputElements: {},
            frameworks: [],
            pageInfo: {
                url: window.location.href,
                title: document.title,
                domain: window.location.hostname
            }
        };

        // 统计各种输入元素
        const inputSelectors = {
            'input[type="text"]': 'Text inputs',
            'input[type="email"]': 'Email inputs',
            'input[type="number"]': 'Number inputs',
            'input[type="date"]': 'Date inputs',
            'input:not([type])': 'Default inputs',
            'textarea': 'Textareas',
            'select': 'Select boxes',
            '[contenteditable="true"]': 'Contenteditable',
            'input': 'All inputs',
            '[name]': 'Elements with name',
            '[data-field]': 'Elements with data-field',
            '[class*="input"]': 'Elements with input class',
            '[class*="field"]': 'Elements with field class'
        };

        for (const [selector, description] of Object.entries(inputSelectors)) {
            try {
                const elements = document.querySelectorAll(selector);
                analysis.inputElements[description] = {
                    count: elements.length,
                    selector: selector,
                    elements: Array.from(elements).slice(0, 5).map(el => ({
                        tag: el.tagName,
                        id: el.id,
                        name: el.name,
                        className: el.className,
                        type: el.type,
                        visible: this.isElementVisible(el)
                    }))
                };
            } catch (error) {
                analysis.inputElements[description] = { error: error.message };
            }
        }

        // 检测UI框架
        const frameworkChecks = {
            'Ant Design': () => document.querySelector('.ant-input, .antd') !== null,
            'Element UI': () => document.querySelector('.el-input, .el-form') !== null,
            'Bootstrap': () => document.querySelector('.form-control, .bootstrap') !== null,
            'Layui': () => document.querySelector('.layui-input, .layui-form') !== null,
            'iView': () => document.querySelector('.ivu-input, .iview') !== null,
            'Vue': () => window.Vue !== undefined || document.querySelector('[v-model]') !== null,
            'React': () => window.React !== undefined || document.querySelector('[data-reactroot]') !== null,
            'Angular': () => window.angular !== undefined || document.querySelector('[ng-app]') !== null,
            'jQuery': () => window.jQuery !== undefined || window.$ !== undefined
        };

        for (const [framework, check] of Object.entries(frameworkChecks)) {
            try {
                if (check()) {
                    analysis.frameworks.push(framework);
                }
            } catch (error) {
                // 忽略检测错误
            }
        }

        console.log('Page analysis result:', analysis);
        return {
            success: true,
            analysis: analysis
        };
    }

    /**
     * 检查元素是否可见
     */
    isElementVisible(element) {
        try {
            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);

            return rect.width > 0 &&
                   rect.height > 0 &&
                   style.display !== 'none' &&
                   style.visibility !== 'hidden';
        } catch (error) {
            return false;
        }
    }

    /**
     * 强制使用截图检测
     */
    async forceScreenshotDetection() {
        try {
            console.log('🔥 Force screenshot detection requested');

            // 使用内嵌的检测器
            const detector = new EmbeddedScreenshotDetector();
            const result = await detector.detectFields();

            if (result.success) {
                this.formFields = result.fields;
                return {
                    success: true,
                    formCount: 1,
                    fieldCount: result.fields.length,
                    method: 'embedded_screenshot',
                    confidence: result.confidence
                };
            } else {
                return {
                    success: false,
                    error: result.error,
                    fieldCount: 0
                };
            }

        } catch (error) {
            console.error('Force screenshot detection failed:', error);
            return {
                success: false,
                error: error.message,
                fieldCount: 0
            };
        }
    }

    /**
     * 执行深度调试
     */
    async performDeepDebug() {
        try {
            console.log('🔬 Starting deep debug analysis...');

            const debug = {
                totalElements: 0,
                validElements: 0,
                totalLabels: 0,
                finalFields: 0,
                selectorResults: [],
                labelAnalysis: [],
                matchingDetails: []
            };

            // 创建调试版检测器
            const detector = new EmbeddedScreenshotDetector();

            // 重写检测器方法以收集调试信息
            const originalFindInputRegions = detector.findInputRegions;
            detector.findInputRegions = async function() {
                const regions = [];
                const inputSelectors = [
                    'input[type="text"]', 'input[type="email"]', 'input[type="tel"]', 'input[type="number"]',
                    'input[type="date"]', 'input[type="url"]', 'input:not([type])', 'textarea', 'select',
                    '[contenteditable="true"]', '.ant-input', '.el-input__inner', '.form-control',
                    '.layui-input', '.ivu-input', '[class*="input"]:not(div):not(span)',
                    '[role="textbox"]', '[role="combobox"]', '[data-field]', '[name]:not([type="hidden"])'
                ];

                const foundElements = new Set();

                for (const selector of inputSelectors) {
                    const selectorResult = {
                        selector: selector,
                        found: 0,
                        valid: 0,
                        elements: []
                    };

                    try {
                        const elements = document.querySelectorAll(selector);
                        selectorResult.found = elements.length;
                        debug.totalElements += elements.length;

                        elements.forEach(element => {
                            const isValid = this.isValidElement(element);
                            const elementInfo = {
                                tag: element.tagName.toLowerCase(),
                                id: element.id || '',
                                name: element.name || '',
                                class: element.className ? element.className.split(' ')[0] : '',
                                valid: isValid,
                                reason: this.getValidationReason(element)
                            };

                            selectorResult.elements.push(elementInfo);

                            if (isValid) {
                                selectorResult.valid++;
                                debug.validElements++;

                                if (!foundElements.has(element)) {
                                    const rect = element.getBoundingClientRect();
                                    if (rect.width > 10 && rect.height > 10) {
                                        regions.push({
                                            x: rect.left + window.scrollX,
                                            y: rect.top + window.scrollY,
                                            width: rect.width,
                                            height: rect.height,
                                            element: element,
                                            score: 0.8,
                                            selector: selector
                                        });
                                        foundElements.add(element);
                                    }
                                }
                            }
                        });
                    } catch (error) {
                        selectorResult.error = error.message;
                    }

                    debug.selectorResults.push(selectorResult);
                }

                console.log(`📦 Debug: Found ${regions.length} input regions from ${debug.totalElements} total elements`);
                return regions;
            };

            // 重写标签查找方法
            const originalFindTextLabels = detector.findTextLabels;
            detector.findTextLabels = async function() {
                const labels = [];
                const textSelectors = [
                    'label', 'span', 'div', 'td', 'th', 'p', 'strong', 'b',
                    '.form-label', '.field-label', '.label', '.ant-form-item-label'
                ];

                for (const selector of textSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            const text = element.textContent.trim();
                            if (text && text.length > 1 && text.length < 100) {
                                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                                    const rect = element.getBoundingClientRect();
                                    if (rect.width > 0 && rect.height > 0) {
                                        const labelInfo = {
                                            text: this.cleanText(text),
                                            x: rect.left + window.scrollX,
                                            y: rect.top + window.scrollY,
                                            width: rect.width,
                                            height: rect.height,
                                            element: element
                                        };
                                        labels.push(labelInfo);
                                        debug.labelAnalysis.push(labelInfo);
                                    }
                                }
                            }
                        });
                    } catch (error) {
                        console.warn(`Error with text selector ${selector}:`, error);
                    }
                }

                debug.totalLabels = labels.length;
                console.log(`📝 Debug: Found ${labels.length} text labels`);
                return labels;
            };

            // 重写匹配方法
            const originalMatchFieldsAndLabels = detector.matchFieldsAndLabels;
            detector.matchFieldsAndLabels = async function(inputRegions, textLabels) {
                const matchedFields = [];

                for (const region of inputRegions) {
                    const nearbyLabels = this.findNearbyLabels(region, textLabels);

                    let fieldType = 'UNKNOWN';
                    let confidence = 0.3;
                    let labelText = 'Unknown';
                    let matched = false;

                    if (nearbyLabels.length > 0) {
                        const bestLabel = nearbyLabels[0];
                        const typeGuess = this.guessFieldType(bestLabel.text);
                        fieldType = typeGuess.type;
                        confidence = typeGuess.confidence * region.score;
                        labelText = bestLabel.text;
                        matched = confidence > 0.3;
                    }

                    const attrGuess = this.guessFieldTypeFromElement(region.element);
                    if (attrGuess.confidence > confidence) {
                        fieldType = attrGuess.type;
                        confidence = attrGuess.confidence;
                        matched = confidence > 0.3;
                    }

                    const matchDetail = {
                        element: {
                            tag: region.element.tagName.toLowerCase(),
                            id: region.element.id || '',
                            name: region.element.name || '',
                            x: region.x,
                            y: region.y
                        },
                        nearbyLabels: nearbyLabels.slice(0, 5),
                        fieldType: fieldType,
                        confidence: confidence,
                        matched: matched
                    };

                    debug.matchingDetails.push(matchDetail);

                    if (matched) {
                        matchedFields.push({
                            element: region.element,
                            fieldType: fieldType,
                            confidence: confidence,
                            coordinates: {
                                x: region.x,
                                y: region.y,
                                width: region.width,
                                height: region.height
                            },
                            label: labelText,
                            method: 'embedded_screenshot',
                            selector: region.selector
                        });
                    }
                }

                debug.finalFields = matchedFields.length;
                console.log(`🔗 Debug: Matched ${matchedFields.length} fields from ${inputRegions.length} regions`);
                return matchedFields;
            };

            // 添加验证原因方法
            detector.getValidationReason = function(element) {
                if (!element || !element.isConnected) return '元素未连接到DOM';

                const excludeTypes = ['hidden', 'button', 'submit', 'reset', 'image', 'file'];
                if (excludeTypes.includes(element.type)) return `排除的类型: ${element.type}`;

                const style = window.getComputedStyle(element);
                if (style.display === 'none') return 'display: none';
                if (style.visibility === 'hidden') return 'visibility: hidden';

                const rect = element.getBoundingClientRect();
                if (rect.width <= 10 || rect.height <= 10) return `尺寸太小: ${rect.width}x${rect.height}`;

                return '通过验证';
            };

            // 执行检测
            const result = await detector.detectFields();

            return {
                success: true,
                debug: debug,
                result: result
            };

        } catch (error) {
            console.error('Deep debug failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 执行激进扫描
     */
    async performAggressiveScan() {
        try {
            console.log('🚀 Starting aggressive scan...');

            // 清除之前的高亮
            this.clearHighlights();

            // 使用增强版检测器
            const detector = new EmbeddedScreenshotDetector();
            const result = await detector.detectFields();

            if (result.success) {
                this.formFields = result.fields;

                console.log(`🚀 Aggressive scan completed: ${result.fields.length} fields found`);

                // 输出详细信息
                result.fields.forEach((field, index) => {
                    console.log(`Field ${index + 1}:`, {
                        tag: field.element.tagName,
                        id: field.element.id,
                        name: field.element.name,
                        type: field.fieldType,
                        label: field.label,
                        selector: field.selector,
                        document: field.document || 'main'
                    });
                });

                return {
                    success: true,
                    formCount: 1,
                    fieldCount: result.fields.length,
                    method: 'aggressive_scan',
                    confidence: result.confidence,
                    details: result.fields.map(field => ({
                        tag: field.element.tagName,
                        id: field.element.id,
                        name: field.element.name,
                        type: field.fieldType,
                        label: field.label
                    }))
                };
            } else {
                return {
                    success: false,
                    error: result.error,
                    fieldCount: 0
                };
            }

        } catch (error) {
            console.error('Aggressive scan failed:', error);
            return {
                success: false,
                error: error.message,
                fieldCount: 0
            };
        }
    }

    /**
     * 高亮所有识别到的字段
     */
    async highlightAllFields() {
        try {
            console.log('🎯 Starting field highlighting...');

            // 先执行激进扫描获取最新的字段列表
            const scanResult = await this.performAggressiveScan();

            if (!scanResult.success || scanResult.fieldCount === 0) {
                return {
                    success: false,
                    error: '没有找到可高亮的字段',
                    fieldCount: 0
                };
            }

            // 清除之前的高亮
            this.clearHighlights();

            // 添加高亮样式
            this.addHighlightStyles();

            let highlightedCount = 0;

            // 高亮每个字段
            this.formFields.forEach((field, index) => {
                if (field.element && field.element.isConnected) {
                    this.highlightSingleField(field, index);
                    highlightedCount++;
                }
            });

            console.log(`🎯 Highlighted ${highlightedCount} fields`);

            // 10秒后自动清除高亮
            setTimeout(() => {
                this.clearHighlights();
                console.log('🎯 Auto-cleared highlights');
            }, 10000);

            return {
                success: true,
                fieldCount: highlightedCount
            };

        } catch (error) {
            console.error('Field highlighting failed:', error);
            return {
                success: false,
                error: error.message,
                fieldCount: 0
            };
        }
    }

    /**
     * 高亮单个字段 - 简洁版本
     */
    highlightSingleField(field, index) {
        const element = field.element;

        // 严格过滤：只处理真正的表单输入元素
        if (!this.isValidFormField(element)) {
            console.log(`🚫 Skipping non-form element: ${element.tagName}#${element.id}`);
            return;
        }

        // 获取元素位置
        const rect = element.getBoundingClientRect();
        if (rect.width < 10 || rect.height < 10) {
            console.log(`🚫 Skipping too small element: ${rect.width}x${rect.height}`);
            return;
        }

        // 直接在元素上添加边框样式
        element.style.cssText += `
            outline: 2px solid #ff4757 !important;
            outline-offset: 1px !important;
            box-shadow: 0 0 8px rgba(255, 71, 87, 0.4) !important;
        `;

        // 添加数据属性
        element.setAttribute('data-freight-field-index', index);
        element.classList.add('freight-parser-highlighted');

        // 创建简洁的标签
        const label = document.createElement('div');
        label.className = 'freight-parser-simple-label';
        label.setAttribute('data-freight-label-index', index);

        // 生成简洁的标签文本
        const labelText = this.generateSimpleLabel(element, index);
        label.textContent = labelText;

        // 标签样式 - 固定定位避免滚动问题
        label.style.cssText = `
            position: fixed !important;
            left: ${rect.left - 5}px !important;
            top: ${rect.top - 25}px !important;
            background: #007bff !important;
            color: white !important;
            padding: 2px 6px !important;
            border-radius: 3px !important;
            font-size: 11px !important;
            font-weight: bold !important;
            font-family: Arial, sans-serif !important;
            white-space: nowrap !important;
            z-index: 999999 !important;
            pointer-events: none !important;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3) !important;
        `;

        // 边界检查
        if (rect.left < 50) {
            label.style.left = '5px';
        }
        if (rect.top < 30) {
            label.style.top = `${rect.bottom + 5}px`;
        }

        document.body.appendChild(label);

        console.log(`✅ Highlighted field ${index + 1}: ${labelText}`);
    }

    /**
     * 严格验证是否为表单字段
     */
    isValidFormField(element) {
        const tagName = element.tagName.toLowerCase();

        // 只接受真正的表单元素
        const validTags = ['input', 'select', 'textarea'];
        if (!validTags.includes(tagName)) {
            return false;
        }

        // 对于input，检查类型
        if (tagName === 'input') {
            const type = element.type.toLowerCase();
            const validTypes = ['text', 'email', 'tel', 'number', 'date', 'datetime-local', 'search'];
            if (!validTypes.includes(type)) {
                return false;
            }
        }

        // 检查是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
            return false;
        }

        // 检查是否被禁用
        if (element.disabled) {
            return false;
        }

        // 检查尺寸
        const rect = element.getBoundingClientRect();
        if (rect.width < 20 || rect.height < 20) {
            return false;
        }

        return true;
    }

    /**
     * 生成简洁的标签文本
     */
    generateSimpleLabel(element, index) {
        // 1. 查找关联的label
        let labelText = this.findAssociatedLabel(element);
        if (labelText && labelText.length < 15) {
            return `${index + 1}. ${labelText}`;
        }

        // 2. 使用placeholder
        const placeholder = element.placeholder;
        if (placeholder && placeholder.length < 15) {
            return `${index + 1}. ${placeholder}`;
        }

        // 3. 使用name属性
        const name = element.name;
        if (name && name.length < 15) {
            return `${index + 1}. ${name}`;
        }

        // 4. 使用id属性
        const id = element.id;
        if (id && id.length < 15) {
            return `${index + 1}. ${id}`;
        }

        // 5. 根据元素类型生成默认名称
        const tagName = element.tagName.toLowerCase();
        if (tagName === 'select') {
            return `${index + 1}. 下拉选择`;
        } else if (tagName === 'textarea') {
            return `${index + 1}. 文本区域`;
        } else {
            return `${index + 1}. 输入框`;
        }
    }

    /**
     * 查找关联的label文本 - 精确版
     */
    findAssociatedLabel(element) {
        console.log('🔍 Finding label for element:', element.tagName, element.id, element.name);

        // 方法1: 通过for属性关联
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) {
                const text = this.cleanLabelText(label.textContent);
                if (text) {
                    console.log('✅ Found label by for attribute:', text);
                    return text;
                }
            }
        }

        // 方法2: 父级label
        const parentLabel = element.closest('label');
        if (parentLabel) {
            const text = this.cleanLabelText(parentLabel.textContent);
            if (text) {
                console.log('✅ Found parent label:', text);
                return text;
            }
        }

        // 方法3: 基于元素属性推断
        const attrLabel = this.getLabelFromAttributes(element);
        if (attrLabel) {
            console.log('✅ Found label from attributes:', attrLabel);
            return attrLabel;
        }

        // 方法4: 精确的位置查找
        const positionLabel = this.findPrecisePositionLabel(element);
        if (positionLabel) {
            console.log('✅ Found label by position:', positionLabel);
            return positionLabel;
        }

        console.log('❌ No label found for element');
        return '';
    }

    /**
     * 从元素属性获取标签
     */
    getLabelFromAttributes(element) {
        // 检查placeholder
        if (element.placeholder) {
            const text = this.cleanLabelText(element.placeholder);
            if (text && !text.includes('请') && !text.includes('Please')) {
                return text;
            }
        }

        // 检查name属性
        if (element.name) {
            const nameText = this.translateFieldName(element.name);
            if (nameText) return nameText;
        }

        // 检查id属性
        if (element.id) {
            const idText = this.translateFieldName(element.id);
            if (idText) return idText;
        }

        return '';
    }

    /**
     * 翻译字段名称
     */
    translateFieldName(name) {
        const nameMap = {
            // 货代常用字段
            'shipper': '发货人',
            'consignee': '收货人',
            'notify': '通知人',
            'vessel': '船名',
            'voyage': '航次',
            'pol': '起运港',
            'pod': '目的港',
            'etd': '开船日期',
            'eta': '到港日期',
            'container': '集装箱号',
            'seal': '封号',
            'weight': '重量',
            'volume': '体积',
            'package': '件数',
            'goods': '货物描述',
            'marks': '唛头',
            'freight': '运费',
            'bl': '提单号',
            'booking': '订舱号',

            // 通用字段
            'name': '名称',
            'company': '公司',
            'address': '地址',
            'tel': '电话',
            'phone': '电话',
            'email': '邮箱',
            'date': '日期',
            'time': '时间',
            'amount': '金额',
            'quantity': '数量',
            'price': '价格',
            'total': '总计'
        };

        const lowerName = name.toLowerCase();

        // 直接匹配
        if (nameMap[lowerName]) {
            return nameMap[lowerName];
        }

        // 部分匹配
        for (const [key, value] of Object.entries(nameMap)) {
            if (lowerName.includes(key) || key.includes(lowerName)) {
                return value;
            }
        }

        return '';
    }

    /**
     * 精确的位置标签查找
     */
    findPrecisePositionLabel(element) {
        const rect = element.getBoundingClientRect();

        // 查找策略：优先左侧，然后上方
        const strategies = [
            {
                name: 'left',
                area: {
                    left: Math.max(0, rect.left - 150),
                    right: rect.left - 5,
                    top: rect.top - 5,
                    bottom: rect.bottom + 5
                }
            },
            {
                name: 'top',
                area: {
                    left: rect.left - 20,
                    right: rect.right + 20,
                    top: Math.max(0, rect.top - 40),
                    bottom: rect.top - 2
                }
            }
        ];

        for (const strategy of strategies) {
            const label = this.findBestTextInArea(strategy.area, element);
            if (label) {
                console.log(`Found label using ${strategy.name} strategy:`, label);
                return label;
            }
        }

        return '';
    }

    /**
     * 在区域内查找最佳文本
     */
    findBestTextInArea(area, excludeElement) {
        const candidates = [];

        // 查找所有可能的文本元素
        const textElements = document.querySelectorAll('span, div, td, th, label, p, strong, b, em, i');

        for (const textEl of textElements) {
            // 跳过包含输入元素的容器
            if (textEl.contains(excludeElement) || excludeElement.contains(textEl)) {
                continue;
            }

            // 跳过包含其他输入元素的容器
            if (textEl.querySelector('input, select, textarea')) {
                continue;
            }

            const textRect = textEl.getBoundingClientRect();

            // 检查是否在搜索区域内
            if (this.isRectInArea(textRect, area)) {
                const text = this.cleanLabelText(textEl.textContent);
                if (text) {
                    // 计算与目标元素的距离
                    const distance = this.calculateDistance(textRect, {
                        left: area.right,
                        top: area.top,
                        right: area.right,
                        bottom: area.bottom
                    });

                    candidates.push({
                        text: text,
                        distance: distance,
                        element: textEl
                    });
                }
            }
        }

        // 按距离排序，返回最近的
        candidates.sort((a, b) => a.distance - b.distance);

        return candidates.length > 0 ? candidates[0].text : '';
    }

    /**
     * 检查矩形是否在区域内
     */
    isRectInArea(rect, area) {
        return rect.right >= area.left &&
               rect.left <= area.right &&
               rect.bottom >= area.top &&
               rect.top <= area.bottom;
    }

    /**
     * 计算两个矩形的距离
     */
    calculateDistance(rect1, rect2) {
        const centerX1 = (rect1.left + rect1.right) / 2;
        const centerY1 = (rect1.top + rect1.bottom) / 2;
        const centerX2 = (rect2.left + rect2.right) / 2;
        const centerY2 = (rect2.top + rect2.bottom) / 2;

        return Math.sqrt(Math.pow(centerX2 - centerX1, 2) + Math.pow(centerY2 - centerY1, 2));
    }

    /**
     * 清理标签文本
     */
    cleanLabelText(text) {
        if (!text) return '';

        // 移除多余的空白字符
        text = text.trim().replace(/\s+/g, ' ');

        // 过滤掉无意义的文本
        const meaninglessTexts = [
            '请选择', '请输入', '请填写', '选择', '输入', '填写',
            'Please select', 'Please enter', 'Select', 'Enter',
            '---', '...', '　', ''
        ];

        if (meaninglessTexts.includes(text) || text.length < 1 || text.length > 20) {
            return '';
        }

        // 移除常见的后缀
        text = text.replace(/[：:*]+$/, '').trim();

        return text;
    }

    /**
     * 查找表格单元格中的标签
     */
    findTableCellLabel(element) {
        const cell = element.closest('td, th');
        if (!cell) return '';

        // 查找同一行的前一个单元格
        const row = cell.closest('tr');
        if (row) {
            const cells = Array.from(row.querySelectorAll('td, th'));
            const currentIndex = cells.indexOf(cell);

            // 检查前面的单元格
            for (let i = currentIndex - 1; i >= 0; i--) {
                const prevCell = cells[i];
                const text = this.cleanLabelText(prevCell.textContent);
                if (text && !prevCell.querySelector('input, select, textarea')) {
                    return text;
                }
            }
        }

        // 查找单元格内的其他文本
        const cellText = this.extractCellText(cell, element);
        if (cellText) return cellText;

        return '';
    }

    /**
     * 提取单元格内的文本（排除输入元素）
     */
    extractCellText(cell, excludeElement) {
        const walker = document.createTreeWalker(
            cell,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    // 排除输入元素内的文本
                    if (node.parentElement === excludeElement) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    // 排除输入元素
                    if (node.parentElement &&
                        ['INPUT', 'SELECT', 'TEXTAREA'].includes(node.parentElement.tagName)) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let texts = [];
        let node;
        while (node = walker.nextNode()) {
            const text = this.cleanLabelText(node.textContent);
            if (text) texts.push(text);
        }

        return texts.length > 0 ? texts[0] : '';
    }

    /**
     * 查找左侧相邻的文本
     */
    findLeftAdjacentText(element) {
        const rect = element.getBoundingClientRect();
        const searchArea = {
            left: rect.left - 200,
            right: rect.left,
            top: rect.top - 10,
            bottom: rect.bottom + 10
        };

        return this.findTextInArea(searchArea, element);
    }

    /**
     * 查找上方的文本
     */
    findTopAdjacentText(element) {
        const rect = element.getBoundingClientRect();
        const searchArea = {
            left: rect.left - 50,
            right: rect.right + 50,
            top: rect.top - 50,
            bottom: rect.top
        };

        return this.findTextInArea(searchArea, element);
    }

    /**
     * 在指定区域查找文本
     */
    findTextInArea(area, excludeElement) {
        const textElements = document.querySelectorAll('span, div, td, th, label, p, strong, b');
        let bestMatch = null;
        let bestDistance = Infinity;

        for (const textEl of textElements) {
            if (textEl.contains(excludeElement) || excludeElement.contains(textEl)) {
                continue;
            }

            // 检查是否包含输入元素
            if (textEl.querySelector('input, select, textarea')) {
                continue;
            }

            const textRect = textEl.getBoundingClientRect();

            // 检查是否在搜索区域内
            if (textRect.right >= area.left && textRect.left <= area.right &&
                textRect.bottom >= area.top && textRect.top <= area.bottom) {

                const text = this.cleanLabelText(textEl.textContent);
                if (text) {
                    // 计算距离
                    const distance = Math.sqrt(
                        Math.pow(textRect.right - area.right, 2) +
                        Math.pow(textRect.top - area.top, 2)
                    );

                    if (distance < bestDistance) {
                        bestDistance = distance;
                        bestMatch = text;
                    }
                }
            }
        }

        return bestMatch || '';
    }

    /**
     * 查找容器标签
     */
    findContainerLabel(element) {
        let parent = element.parentElement;
        let level = 0;

        while (parent && level < 3) {
            // 查找同级的文本元素
            const siblings = Array.from(parent.children);
            for (const sibling of siblings) {
                if (sibling !== element && !sibling.contains(element)) {
                    const text = this.cleanLabelText(sibling.textContent);
                    if (text && !sibling.querySelector('input, select, textarea')) {
                        return text;
                    }
                }
            }

            parent = parent.parentElement;
            level++;
        }

        return '';
    }

    /**
     * 生成精确的标签文本
     */
    generatePreciseLabel(field, element, index) {
        // 优先级：元素的实际文本内容 > name属性 > id属性 > placeholder > 字段类型

        // 1. 检查元素的值或文本内容
        let elementText = '';
        if (element.tagName === 'SELECT') {
            const selectedOption = element.options[element.selectedIndex];
            elementText = selectedOption ? selectedOption.textContent.trim() : '';
        } else if (element.value && element.value.trim()) {
            elementText = element.value.trim();
        } else if (element.textContent && element.textContent.trim()) {
            elementText = element.textContent.trim();
        }

        // 2. 检查placeholder
        const placeholder = element.placeholder || element.getAttribute('placeholder') || '';

        // 3. 检查相关的label
        let labelText = '';
        const labels = document.querySelectorAll('label');
        for (const label of labels) {
            if (label.getAttribute('for') === element.id ||
                label.contains(element) ||
                element.closest('label') === label) {
                labelText = label.textContent.trim();
                break;
            }
        }

        // 4. 检查附近的文本
        const nearbyText = this.findNearbyText(element);

        // 选择最合适的标签文本
        let displayText = '';

        if (labelText && labelText.length < 20) {
            displayText = labelText;
        } else if (placeholder && placeholder.length < 20) {
            displayText = placeholder;
        } else if (element.name && element.name.length < 20) {
            displayText = element.name;
        } else if (element.id && element.id.length < 20) {
            displayText = element.id;
        } else if (nearbyText && nearbyText.length < 20) {
            displayText = nearbyText;
        } else if (elementText && elementText.length < 20) {
            displayText = elementText;
        } else {
            // 使用字段类型
            const typeNames = {
                'BL_NO': '提单号',
                'SHIPPER': '发货人',
                'CONSIGNEE': '收货人',
                'PORT_OF_LOADING': '起运港',
                'PORT_OF_DISCHARGE': '目的港',
                'UNKNOWN': '未知字段'
            };
            displayText = typeNames[field.fieldType] || field.fieldType;
        }

        // 添加元素类型信息
        const elementInfo = `<${element.tagName.toLowerCase()}>`;

        return `${index + 1}. ${displayText} ${elementInfo}`;
    }

    /**
     * 查找元素附近的文本
     */
    findNearbyText(element) {
        const rect = element.getBoundingClientRect();
        const searchRadius = 100;

        // 查找附近的文本元素
        const textElements = document.querySelectorAll('span, div, td, th, label, p');

        for (const textEl of textElements) {
            if (textEl.contains(element) || element.contains(textEl)) continue;

            const textRect = textEl.getBoundingClientRect();
            const distance = Math.sqrt(
                Math.pow(textRect.left - rect.left, 2) +
                Math.pow(textRect.top - rect.top, 2)
            );

            if (distance < searchRadius) {
                const text = textEl.textContent.trim();
                if (text && text.length > 1 && text.length < 30 && !/^\d+$/.test(text)) {
                    return text;
                }
            }
        }

        return '';
    }

    /**
     * 显示字段详细信息
     */
    showFieldDetails(field, element, index) {
        const details = `
字段 ${index + 1} 详细信息:
━━━━━━━━━━━━━━━━━━━━
🏷️ 标签: ${field.label || '无'}
🔤 类型: ${field.fieldType}
📊 置信度: ${Math.round(field.confidence * 100)}%
🏗️ 元素: <${element.tagName.toLowerCase()}>
🆔 ID: ${element.id || '无'}
📝 Name: ${element.name || '无'}
💬 Placeholder: ${element.placeholder || '无'}
📍 位置: (${Math.round(field.coordinates.x)}, ${Math.round(field.coordinates.y)})
📏 尺寸: ${Math.round(field.coordinates.width)} × ${Math.round(field.coordinates.height)}
🎯 选择器: ${field.selector || '无'}
        `;

        alert(details);
    }

    /**
     * 检查是否为菜单或导航元素
     */
    isMenuOrNavigation(element) {
        // 检查元素及其父级是否为菜单
        let current = element;
        let level = 0;

        while (current && level < 5) {
            const className = current.className.toLowerCase();
            const id = current.id.toLowerCase();

            // 常见的菜单/导航关键词
            const menuKeywords = [
                'menu', 'nav', 'sidebar', 'aside', 'toolbar', 'header',
                'dropdown', 'collapse', 'accordion', 'tree', 'list'
            ];

            if (menuKeywords.some(keyword =>
                className.includes(keyword) || id.includes(keyword)
            )) {
                return true;
            }

            // 检查是否在左侧边栏区域（通常是菜单）
            const rect = current.getBoundingClientRect();
            if (rect.left < 200 && rect.width < 300) {
                return true;
            }

            current = current.parentElement;
            level++;
        }

        return false;
    }

    /**
     * 生成友好的字段标签
     */
    generateFieldLabel(field, index) {
        const fieldTypeNames = {
            'BL_NO': '提单号',
            'SHIPPER': '发货人',
            'CONSIGNEE': '收货人',
            'PORT_OF_LOADING': '起运港',
            'PORT_OF_DISCHARGE': '目的港',
            'VESSEL_VOYAGE': '船名航次',
            'CONTAINER_NO': '集装箱号',
            'INVOICE_NO': '发票号',
            'TOTAL_AMOUNT': '金额',
            'DATE': '日期',
            'QUANTITY': '数量',
            'WEIGHT': '重量',
            'VOLUME': '体积',
            'UNKNOWN': '未知字段'
        };

        const typeName = fieldTypeNames[field.fieldType] || field.fieldType;
        const confidence = Math.round(field.confidence * 100);

        // 如果有标签文本，优先显示
        if (field.label && field.label !== 'Unknown' && field.label.length < 20) {
            return `${index + 1}. ${field.label}`;
        }

        // 如果有元素名称，显示名称
        if (field.element.name && field.element.name.length < 20) {
            return `${index + 1}. ${field.element.name}`;
        }

        // 如果有ID，显示ID
        if (field.element.id && field.element.id.length < 20) {
            return `${index + 1}. ${field.element.id}`;
        }

        // 否则显示类型和置信度
        if (confidence > 50) {
            return `${index + 1}. ${typeName}`;
        } else {
            return `${index + 1}. ${typeName} (${confidence}%)`;
        }
    }

    /**
     * 根据字段类型获取颜色
     */
    getFieldTypeColor(fieldType) {
        const colors = {
            'BL_NO': 'linear-gradient(135deg, #e74c3c, #c0392b)',           // 红色 - 提单号
            'SHIPPER': 'linear-gradient(135deg, #3498db, #2980b9)',         // 蓝色 - 发货人
            'CONSIGNEE': 'linear-gradient(135deg, #2ecc71, #27ae60)',       // 绿色 - 收货人
            'PORT_OF_LOADING': 'linear-gradient(135deg, #f39c12, #e67e22)', // 橙色 - 起运港
            'PORT_OF_DISCHARGE': 'linear-gradient(135deg, #9b59b6, #8e44ad)', // 紫色 - 目的港
            'VESSEL_VOYAGE': 'linear-gradient(135deg, #1abc9c, #16a085)',   // 青色 - 船名航次
            'CONTAINER_NO': 'linear-gradient(135deg, #34495e, #2c3e50)',    // 深灰 - 集装箱号
            'INVOICE_NO': 'linear-gradient(135deg, #e67e22, #d35400)',      // 深橙 - 发票号
            'TOTAL_AMOUNT': 'linear-gradient(135deg, #27ae60, #229954)',    // 深绿 - 金额
            'UNKNOWN': 'linear-gradient(135deg, #95a5a6, #7f8c8d)'          // 灰色 - 未知
        };

        return colors[fieldType] || colors['UNKNOWN'];
    }

    /**
     * 添加高亮样式
     */
    addHighlightStyles() {
        // 检查是否已经添加了样式
        if (document.getElementById('freight-parser-highlight-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'freight-parser-highlight-styles';
        style.textContent = `
            .freight-parser-highlight-container {
                transition: all 0.2s ease !important;
            }

            .freight-parser-highlight-container:hover {
                border-color: #ff3742 !important;
                box-shadow: 0 0 15px rgba(255, 71, 87, 0.8) !important;
                cursor: pointer !important;
            }

            .freight-parser-precise-label {
                transition: all 0.2s ease !important;
            }

            .freight-parser-precise-label:hover {
                transform: scale(1.05) !important;
            }

            /* 确保高亮元素不被其他元素遮挡 */
            .freight-parser-highlight-container * {
                pointer-events: none !important;
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 清除所有高亮
     */
    clearHighlights() {
        console.log('🧹 Starting to clear all highlights...');

        // 清除自动清理定时器
        if (this.autoCleanupTimer) {
            clearTimeout(this.autoCleanupTimer);
            this.autoCleanupTimer = null;
        }

        // 方案1: 移除我们添加的CSS样式表
        this.removeHighlightStyleSheet();

        // 方案2: 清理我们记录的高亮元素
        this.clearTrackedHighlights();

        // 方案3: 强制刷新页面样式（最后手段）
        this.forceStyleRefresh();

        console.log('🧹 All highlights cleared successfully');
    }

    /**
     * 移除高亮样式表
     */
    removeHighlightStyleSheet() {
        // 移除我们添加的样式表
        const styleElements = document.querySelectorAll('#freight-parser-highlight-styles, [data-freight-parser-style]');
        styleElements.forEach(style => {
            style.remove();
            console.log('🧹 Removed style sheet');
        });
    }

    /**
     * 清理追踪的高亮元素
     */
    clearTrackedHighlights() {
        // 如果我们有记录高亮的元素，直接清理
        if (this.highlightedElements && this.highlightedElements.length > 0) {
            this.highlightedElements.forEach(element => {
                if (element && element.style) {
                    // 重置为原始样式
                    element.style.outline = element.dataset.originalOutline || '';
                    element.style.outlineOffset = element.dataset.originalOutlineOffset || '';
                    element.style.boxShadow = element.dataset.originalBoxShadow || '';

                    // 清理我们的标记
                    delete element.dataset.originalOutline;
                    delete element.dataset.originalOutlineOffset;
                    delete element.dataset.originalBoxShadow;
                    element.classList.remove('freight-parser-highlighted');
                    element.removeAttribute('data-freight-field-index');
                }
            });
            this.highlightedElements = [];
            console.log('🧹 Cleared tracked highlights');
        }

        // 移除所有标签
        document.querySelectorAll('.freight-parser-label').forEach(label => {
            label.remove();
        });
    }

    /**
     * 强制样式刷新
     */
    forceStyleRefresh() {
        // 创建一个临时样式来强制重绘
        const tempStyle = document.createElement('style');
        tempStyle.textContent = `
            input, select, textarea {
                outline: none !important;
            }
        `;
        document.head.appendChild(tempStyle);

        // 立即移除，触发重绘
        setTimeout(() => {
            tempStyle.remove();
        }, 100);

        console.log('🧹 Forced style refresh');
    }

    /**
     * 生成字段报告
     */
    async generateFieldReport() {
        try {
            console.log('📋 Generating field report...');

            // 先执行激进扫描获取最新的字段列表
            const scanResult = await this.performAggressiveScan();

            if (!scanResult.success || scanResult.fieldCount === 0) {
                return {
                    success: false,
                    error: '没有找到可报告的字段',
                    fieldCount: 0
                };
            }

            // 统计信息
            const totalFields = this.formFields.length;
            const identifiedFields = this.formFields.filter(f => f.fieldType !== 'UNKNOWN').length;
            const highConfidenceFields = this.formFields.filter(f => f.confidence >= 0.7).length;
            const averageConfidence = this.formFields.reduce((sum, f) => sum + f.confidence, 0) / totalFields * 100;

            // 生成字段详细信息
            const fields = this.formFields.map(field => ({
                fieldType: field.fieldType,
                displayName: this.getFieldDisplayName(field),
                confidence: field.confidence,
                coordinates: field.coordinates,
                label: field.label || 'Unknown',
                element: {
                    tag: field.element.tagName.toLowerCase(),
                    id: field.element.id || '',
                    name: field.element.name || '',
                    className: field.element.className || ''
                },
                selector: field.selector || '',
                method: field.method || 'unknown'
            }));

            console.log(`📋 Report generated: ${totalFields} total, ${identifiedFields} identified`);

            return {
                success: true,
                pageTitle: document.title,
                pageUrl: window.location.href,
                totalFields: totalFields,
                identifiedFields: identifiedFields,
                highConfidenceFields: highConfidenceFields,
                averageConfidence: averageConfidence,
                fields: fields
            };

        } catch (error) {
            console.error('Field report generation failed:', error);
            return {
                success: false,
                error: error.message,
                fieldCount: 0
            };
        }
    }

    /**
     * 获取字段显示名称
     */
    getFieldDisplayName(field) {
        const fieldTypeNames = {
            'BL_NO': '提单号',
            'SHIPPER': '发货人',
            'CONSIGNEE': '收货人',
            'PORT_OF_LOADING': '起运港',
            'PORT_OF_DISCHARGE': '目的港',
            'VESSEL_VOYAGE': '船名航次',
            'CONTAINER_NO': '集装箱号',
            'INVOICE_NO': '发票号',
            'TOTAL_AMOUNT': '金额',
            'DATE': '日期',
            'QUANTITY': '数量',
            'WEIGHT': '重量',
            'VOLUME': '体积',
            'COMPANY': '公司',
            'CONTACT': '联系方式',
            'ADDRESS': '地址',
            'EMAIL': '邮箱',
            'REMARK': '备注',
            'GOODS': '货物',
            'PACKAGE': '包装',
            'TERMS': '条款',
            'NUMBER_FIELD': '数字字段',
            'SELECT_FIELD': '选择字段',
            'UNKNOWN': '未知字段'
        };

        // 优先使用标签文本
        if (field.label && field.label !== 'Unknown' && field.label.length < 20) {
            return field.label;
        }

        // 使用元素名称
        if (field.element.name && field.element.name.length < 20) {
            return field.element.name;
        }

        // 使用元素ID
        if (field.element.id && field.element.id.length < 20) {
            return field.element.id;
        }

        // 使用字段类型名称
        return fieldTypeNames[field.fieldType] || field.fieldType;
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化Content Script
if (typeof window !== 'undefined') {
    // 避免重复初始化
    if (!window.freightParserContent) {
        window.freightParserContent = new FreightParserContent();
        console.log('FreightParser Content Script initialized');
    } else {
        console.log('FreightParser Content Script already exists');
    }
}
