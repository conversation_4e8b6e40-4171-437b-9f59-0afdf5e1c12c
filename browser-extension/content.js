/**
 * 货代单证智能解析浏览器扩展 - Content Script
 * 负责页面交互、表单扫描和智能回填
 */

// 内嵌截图检测器类
class EmbeddedScreenshotDetector {
    constructor() {
        console.log('EmbeddedScreenshotDetector initialized');
    }

    async detectFields() {
        try {
            console.log('🔍 Starting embedded screenshot detection...');

            // 直接从DOM中查找输入元素，但使用更智能的方法
            const inputRegions = await this.findInputRegions();
            const textLabels = await this.findTextLabels();
            const matchedFields = await this.matchFieldsAndLabels(inputRegions, textLabels);

            console.log(`✅ Embedded detection found ${matchedFields.length} fields`);

            return {
                success: true,
                fields: matchedFields,
                method: 'embedded_screenshot',
                confidence: this.calculateConfidence(matchedFields)
            };

        } catch (error) {
            console.error('❌ Embedded detection failed:', error);
            return {
                success: false,
                error: error.message,
                fields: []
            };
        }
    }

    async findInputRegions() {
        const regions = [];

        // 更全面的选择器
        const inputSelectors = [
            'input[type="text"]', 'input[type="email"]', 'input[type="tel"]', 'input[type="number"]',
            'input[type="date"]', 'input[type="url"]', 'input:not([type])', 'textarea', 'select',
            '[contenteditable="true"]', '.ant-input', '.el-input__inner', '.form-control',
            '.layui-input', '.ivu-input', '[class*="input"]:not(div):not(span)',
            '[role="textbox"]', '[role="combobox"]', '[data-field]', '[name]:not([type="hidden"])'
        ];

        const foundElements = new Set();

        for (const selector of inputSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`Selector "${selector}" found ${elements.length} elements`);

                elements.forEach(element => {
                    if (!foundElements.has(element) && this.isValidElement(element)) {
                        const rect = element.getBoundingClientRect();
                        if (rect.width > 10 && rect.height > 10) { // 更宽松的尺寸要求
                            regions.push({
                                x: rect.left + window.scrollX,
                                y: rect.top + window.scrollY,
                                width: rect.width,
                                height: rect.height,
                                element: element,
                                score: 0.8,
                                selector: selector
                            });
                            foundElements.add(element);
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with selector ${selector}:`, error);
            }
        }

        console.log(`📦 Found ${regions.length} input regions`);
        return regions;
    }

    isValidElement(element) {
        if (!element || !element.isConnected) return false;

        // 排除明显的非输入元素
        const excludeTypes = ['hidden', 'button', 'submit', 'reset', 'image', 'file'];
        if (excludeTypes.includes(element.type)) return false;

        // 更宽松的可见性检查
        const style = window.getComputedStyle(element);
        if (style.display === 'none') return false;

        return true;
    }

    async findTextLabels() {
        const labels = [];

        const textSelectors = [
            'label', 'span', 'div', 'td', 'th', 'p', 'strong', 'b',
            '.form-label', '.field-label', '.label', '.ant-form-item-label'
        ];

        for (const selector of textSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const text = element.textContent.trim();
                    if (text && text.length > 1 && text.length < 100) {
                        // 过滤掉纯数字和特殊字符
                        if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                            const rect = element.getBoundingClientRect();
                            if (rect.width > 0 && rect.height > 0) {
                                labels.push({
                                    text: this.cleanText(text),
                                    x: rect.left + window.scrollX,
                                    y: rect.top + window.scrollY,
                                    width: rect.width,
                                    height: rect.height,
                                    element: element
                                });
                            }
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with text selector ${selector}:`, error);
            }
        }

        console.log(`📝 Found ${labels.length} text labels`);
        return labels;
    }

    cleanText(text) {
        return text.replace(/[：:*]/g, '').replace(/\s+/g, ' ').trim();
    }

    async matchFieldsAndLabels(inputRegions, textLabels) {
        const matchedFields = [];

        for (const region of inputRegions) {
            const nearbyLabels = this.findNearbyLabels(region, textLabels);

            let fieldType = 'UNKNOWN';
            let confidence = 0.3;
            let labelText = 'Unknown';

            if (nearbyLabels.length > 0) {
                const bestLabel = nearbyLabels[0];
                const typeGuess = this.guessFieldType(bestLabel.text);
                fieldType = typeGuess.type;
                confidence = typeGuess.confidence * region.score;
                labelText = bestLabel.text;
            }

            // 也尝试从元素属性中猜测类型
            const attrGuess = this.guessFieldTypeFromElement(region.element);
            if (attrGuess.confidence > confidence) {
                fieldType = attrGuess.type;
                confidence = attrGuess.confidence;
            }

            matchedFields.push({
                element: region.element,
                fieldType: fieldType,
                confidence: confidence,
                coordinates: {
                    x: region.x,
                    y: region.y,
                    width: region.width,
                    height: region.height
                },
                label: labelText,
                method: 'embedded_screenshot',
                selector: region.selector
            });
        }

        console.log(`🔗 Matched ${matchedFields.length} fields`);
        return matchedFields;
    }

    findNearbyLabels(region, textLabels) {
        const maxDistance = 300; // 增加搜索范围
        const nearbyLabels = [];

        for (const label of textLabels) {
            const distance = this.calculateDistance(region, label);
            if (distance < maxDistance) {
                nearbyLabels.push({
                    ...label,
                    distance: distance
                });
            }
        }

        return nearbyLabels.sort((a, b) => a.distance - b.distance);
    }

    calculateDistance(region, label) {
        const regionCenterX = region.x + region.width / 2;
        const regionCenterY = region.y + region.height / 2;
        const labelCenterX = label.x + label.width / 2;
        const labelCenterY = label.y + label.height / 2;

        return Math.sqrt(
            Math.pow(regionCenterX - labelCenterX, 2) +
            Math.pow(regionCenterY - labelCenterY, 2)
        );
    }

    guessFieldType(labelText) {
        const fieldPatterns = {
            'BL_NO': ['提单号', '提单', 'bl', 'bill', 'lading'],
            'SHIPPER': ['发货人', '托运人', 'shipper', 'consignor', '发货'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver', '收货'],
            'PORT_OF_LOADING': ['起运港', '装货港', 'pol', 'loading', '起运'],
            'PORT_OF_DISCHARGE': ['目的港', '卸货港', 'pod', 'discharge', '目的'],
            'VESSEL_VOYAGE': ['船名', '航次', 'vessel', 'voyage', '船'],
            'CONTAINER_NO': ['集装箱', '箱号', 'container', 'cntr', '集装'],
            'INVOICE_NO': ['发票号', '发票', 'invoice', 'inv'],
            'TOTAL_AMOUNT': ['金额', '总额', 'amount', 'total', 'value', '价格'],
            'DATE': ['日期', 'date', '时间'],
            'QUANTITY': ['数量', '件数', 'quantity', 'pieces', 'pkgs'],
            'WEIGHT': ['重量', '毛重', 'weight', 'gross'],
            'VOLUME': ['体积', '立方', 'volume', 'cbm']
        };

        const text = labelText.toLowerCase();

        for (const [fieldType, keywords] of Object.entries(fieldPatterns)) {
            for (const keyword of keywords) {
                if (text.includes(keyword.toLowerCase())) {
                    return { type: fieldType, confidence: 0.8 };
                }
            }
        }

        return { type: 'UNKNOWN', confidence: 0.3 };
    }

    guessFieldTypeFromElement(element) {
        const elementText = [
            element.id || '',
            element.name || '',
            element.className || '',
            element.placeholder || '',
            element.title || ''
        ].join(' ').toLowerCase();

        const fieldPatterns = {
            'BL_NO': ['bl', 'bill', 'lading'],
            'SHIPPER': ['shipper', 'consignor'],
            'CONSIGNEE': ['consignee', 'receiver'],
            'PORT_OF_LOADING': ['pol', 'loading'],
            'PORT_OF_DISCHARGE': ['pod', 'discharge'],
            'VESSEL_VOYAGE': ['vessel', 'voyage'],
            'CONTAINER_NO': ['container', 'cntr'],
            'INVOICE_NO': ['invoice', 'inv'],
            'TOTAL_AMOUNT': ['amount', 'total', 'value']
        };

        for (const [fieldType, keywords] of Object.entries(fieldPatterns)) {
            for (const keyword of keywords) {
                if (elementText.includes(keyword)) {
                    return { type: fieldType, confidence: 0.6 };
                }
            }
        }

        return { type: 'UNKNOWN', confidence: 0.2 };
    }

    calculateConfidence(fields) {
        if (fields.length === 0) return 0;

        const totalConfidence = fields.reduce((sum, field) => sum + field.confidence, 0);
        return totalConfidence / fields.length;
    }
}

class FreightParserContent {
    constructor() {
        this.formFields = [];
        this.isInitialized = false;
        this.detectionMethod = 'screenshot'; // 'dom', 'vision', 'ml', 'screenshot'

        // 初始化检测器
        this.visionDetector = null;
        this.mlDetector = null;
        this.screenshotDetector = null;

        this.init();
    }

    async init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 初始化检测器
        await this.initDetectors();

        // 页面加载完成后扫描表单
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.scanPage());
        } else {
            this.scanPage();
        }

        this.isInitialized = true;
        console.log('FreightParser Content Script initialized with', this.detectionMethod, 'detection');
    }

    /**
     * 初始化检测器
     */
    async initDetectors() {
        try {
            console.log('Initializing detectors with method:', this.detectionMethod);

            if (this.detectionMethod === 'vision') {
                // 检查是否已经加载了VisionFieldDetector
                if (typeof VisionFieldDetector === 'undefined') {
                    console.log('Loading vision detector...');
                    // 优先尝试简化版检测器
                    await this.loadDetectorScript('vision-field-detector-simple.js');
                }

                if (typeof VisionFieldDetector !== 'undefined') {
                    this.visionDetector = new VisionFieldDetector();
                    console.log('Vision detector initialized');
                } else {
                    throw new Error('VisionFieldDetector not available');
                }

            } else if (this.detectionMethod === 'ml') {
                // 检查是否已经加载了MLFieldDetector
                if (typeof MLFieldDetector === 'undefined') {
                    console.log('Loading ML detector...');
                    await this.loadDetectorScript('ml-field-detector.js');
                }

                if (typeof MLFieldDetector !== 'undefined') {
                    this.mlDetector = new MLFieldDetector();
                    console.log('ML detector initialized');
                } else {
                    throw new Error('MLFieldDetector not available');
                }
            } else if (this.detectionMethod === 'screenshot') {
                // 检查是否已经加载了ScreenshotFieldDetector
                if (typeof ScreenshotFieldDetector === 'undefined') {
                    console.log('Loading screenshot detector...');
                    await this.loadDetectorScript('screenshot-detector.js');
                }

                if (typeof ScreenshotFieldDetector !== 'undefined') {
                    this.screenshotDetector = new ScreenshotFieldDetector();
                    console.log('Screenshot detector initialized');
                } else {
                    throw new Error('ScreenshotFieldDetector not available');
                }
            }

            console.log('Detectors initialization completed');

        } catch (error) {
            console.error('Failed to initialize detectors:', error);
            console.log('Falling back to DOM detection method');
            // 降级到DOM检测
            this.detectionMethod = 'dom';
        }
    }

    /**
     * 加载检测器脚本
     */
    async loadDetectorScript(scriptName) {
        return new Promise((resolve, reject) => {
            // 检查脚本是否已经存在
            const existingScript = document.querySelector(`script[src*="${scriptName}"]`);
            if (existingScript) {
                resolve();
                return;
            }

            const script = document.createElement('script');

            // 尝试不同的路径
            const possiblePaths = [
                chrome.runtime.getURL(scriptName),
                chrome.runtime.getURL(`js/${scriptName}`),
                chrome.runtime.getURL(`scripts/${scriptName}`)
            ];

            let pathIndex = 0;

            const tryLoadScript = () => {
                if (pathIndex >= possiblePaths.length) {
                    reject(new Error(`Failed to load ${scriptName} from all possible paths`));
                    return;
                }

                script.src = possiblePaths[pathIndex];
                console.log(`Trying to load script from: ${script.src}`);

                script.onload = () => {
                    console.log(`Successfully loaded: ${script.src}`);
                    resolve();
                };

                script.onerror = (error) => {
                    console.warn(`Failed to load from ${script.src}, trying next path...`);
                    pathIndex++;
                    tryLoadScript();
                };

                document.head.appendChild(script);
            };

            tryLoadScript();
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'ping':
                    sendResponse({ success: true, ready: true });
                    break;

                case 'scanPage':
                    const scanResult = this.scanPage();
                    sendResponse(scanResult);
                    break;

                case 'fillForm':
                    const fillResult = await this.fillForm(request.data);
                    sendResponse(fillResult);
                    break;

                case 'highlightFields':
                    this.highlightFields();
                    sendResponse({ success: true });
                    break;

                case 'debugFields':
                    const debugResult = this.debugFields();
                    sendResponse(debugResult);
                    break;

                case 'analyzePageElements':
                    const analysisResult = this.analyzePageElements();
                    sendResponse(analysisResult);
                    break;

                case 'forceScreenshotDetection':
                    const screenshotResult = await this.forceScreenshotDetection();
                    sendResponse(screenshotResult);
                    break;

                case 'highlightField':
                    this.highlightSingleField(request.fieldIndex);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action: ' + request.action });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ error: error.message });
        }
    }

    /**
     * 扫描页面表单和字段
     */
    async scanPage() {
        console.log('Scanning page for forms and fields using', this.detectionMethod, 'method...');

        let result;

        try {
            if (this.detectionMethod === 'vision' && this.visionDetector) {
                result = await this.scanWithVision();
            } else if (this.detectionMethod === 'ml' && this.mlDetector) {
                result = await this.scanWithML();
            } else if (this.detectionMethod === 'screenshot' && this.screenshotDetector) {
                result = await this.scanWithScreenshot();
            } else {
                result = await this.scanWithDOM();
            }
        } catch (error) {
            console.error('Advanced scanning failed, falling back to DOM:', error);
            result = await this.scanWithDOM();
        }

        console.log('Page scan result:', result);
        return result;
    }

    /**
     * 使用视觉检测扫描
     */
    async scanWithVision() {
        const visionResult = await this.visionDetector.detectFields();

        if (visionResult.success) {
            this.formFields = visionResult.fields;
            return {
                formCount: 1, // 视觉检测不区分表单
                fieldCount: visionResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'vision',
                confidence: visionResult.confidence
            };
        } else {
            throw new Error('Vision detection failed: ' + visionResult.error);
        }
    }

    /**
     * 使用机器学习扫描
     */
    async scanWithML() {
        const mlResult = await this.mlDetector.detectFields();

        if (mlResult.success) {
            this.formFields = mlResult.fields;
            return {
                formCount: 1,
                fieldCount: mlResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'ml',
                confidence: mlResult.confidence
            };
        } else {
            throw new Error('ML detection failed: ' + mlResult.error);
        }
    }

    /**
     * 使用截图扫描
     */
    async scanWithScreenshot() {
        // 使用内嵌的检测器
        const detector = new EmbeddedScreenshotDetector();
        const screenshotResult = await detector.detectFields();

        if (screenshotResult.success) {
            this.formFields = screenshotResult.fields;
            return {
                formCount: 1,
                fieldCount: screenshotResult.fields.length,
                url: window.location.href,
                title: document.title,
                method: 'embedded_screenshot',
                confidence: screenshotResult.confidence
            };
        } else {
            throw new Error('Screenshot detection failed: ' + screenshotResult.error);
        }
    }

    /**
     * 使用DOM扫描（原有方法）
     */
    async scanWithDOM() {
        // 扫描表单
        const forms = document.querySelectorAll('form');

        // 扫描所有可填写的字段
        this.formFields = this.scanFormFields();

        return {
            formCount: forms.length,
            fieldCount: this.formFields.length,
            url: window.location.href,
            title: document.title,
            method: 'dom'
        };
    }

    /**
     * 扫描表单字段
     */
    scanFormFields() {
        console.log('Starting comprehensive field scan...');
        const fields = [];

        // 第一阶段：标准选择器
        const standardSelectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="tel"]',
            'input[type="number"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'input[type="time"]',
            'input[type="url"]',
            'input[type="search"]',
            'input:not([type])',
            'textarea',
            'select'
        ];

        // 第二阶段：框架特定选择器
        const frameworkSelectors = [
            '[contenteditable="true"]',
            '.ant-input',
            '.ant-input-affix-wrapper input',
            '.el-input__inner',
            '.form-control',
            '.layui-input',
            '.ivu-input',
            '.van-field__control',
            '.weui-input',
            '.mui-input'
        ];

        // 第三阶段：通用模式选择器
        const patternSelectors = [
            '[class*="input"]:not(div):not(span)',
            '[class*="field"]:not(div):not(span)',
            '[class*="text"]:not(div):not(span)',
            '[class*="edit"]:not(div):not(span)',
            '[role="textbox"]',
            '[role="combobox"]',
            '[role="spinbutton"]',
            '[data-field]',
            '[data-name]',
            '[name]:not([type="hidden"]):not([type="button"]):not([type="submit"])'
        ];

        // 第四阶段：深度扫描
        const deepSelectors = [
            'input',
            'textarea',
            'select',
            '[contenteditable]'
        ];

        const foundElements = new Set();
        let scanStats = {
            standard: 0,
            framework: 0,
            pattern: 0,
            deep: 0,
            total: 0
        };

        // 执行分阶段扫描
        this.executeScanPhase('Standard', standardSelectors, fields, foundElements, scanStats, 'standard');
        this.executeScanPhase('Framework', frameworkSelectors, fields, foundElements, scanStats, 'framework');
        this.executeScanPhase('Pattern', patternSelectors, fields, foundElements, scanStats, 'pattern');
        this.executeScanPhase('Deep', deepSelectors, fields, foundElements, scanStats, 'deep');

        scanStats.total = fields.length;
        console.log('Scan statistics:', scanStats);
        console.log(`Total found ${fields.length} form fields`);

        // 如果还是没找到足够的字段，尝试更激进的扫描
        if (fields.length < 5) {
            console.log('Low field count, trying aggressive scan...');
            this.aggressiveScan(fields, foundElements);
        }

        return fields;
    }

    /**
     * 执行扫描阶段
     */
    executeScanPhase(phaseName, selectors, fields, foundElements, stats, statKey) {
        console.log(`Executing ${phaseName} scan phase...`);
        let phaseCount = 0;

        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`Selector "${selector}" found ${elements.length} elements`);

                elements.forEach(element => {
                    if (!foundElements.has(element)) {
                        const isValid = this.isValidFormField(element);
                        console.log(`Element ${element.tagName}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ')[0] : ''} - Valid: ${isValid}`);

                        if (isValid) {
                            const fieldInfo = this.extractFieldInfo(element);
                            if (fieldInfo) {
                                fields.push(fieldInfo);
                                foundElements.add(element);
                                phaseCount++;
                            }
                        }
                    }
                });
            } catch (error) {
                console.warn(`Error with selector "${selector}":`, error);
            }
        });

        stats[statKey] = phaseCount;
        console.log(`${phaseName} phase found ${phaseCount} fields`);
    }

    /**
     * 激进扫描模式
     */
    aggressiveScan(fields, foundElements) {
        console.log('Starting aggressive scan...');

        // 扫描所有可能的输入元素，降低验证标准
        const allElements = document.querySelectorAll('*');
        let aggressiveCount = 0;

        allElements.forEach(element => {
            if (foundElements.has(element)) return;

            // 更宽松的检查条件
            if (this.couldBeInputField(element)) {
                const fieldInfo = this.extractFieldInfo(element);
                if (fieldInfo) {
                    fieldInfo.scanMethod = 'aggressive';
                    fields.push(fieldInfo);
                    foundElements.add(element);
                    aggressiveCount++;
                }
            }
        });

        console.log(`Aggressive scan found ${aggressiveCount} additional fields`);
    }

    /**
     * 宽松的输入字段检查
     */
    couldBeInputField(element) {
        const tagName = element.tagName.toLowerCase();

        // 基本标签检查
        if (['input', 'textarea', 'select'].includes(tagName)) {
            return element.type !== 'hidden' && element.type !== 'button' && element.type !== 'submit';
        }

        // 可编辑元素
        if (element.contentEditable === 'true') {
            return true;
        }

        // 通过属性判断
        if (element.hasAttribute('name') || element.hasAttribute('data-field')) {
            return true;
        }

        // 通过class名称判断
        const className = element.className.toLowerCase();
        const inputKeywords = ['input', 'field', 'text', 'edit', 'form'];
        if (inputKeywords.some(keyword => className.includes(keyword))) {
            // 排除明显的非输入元素
            if (!['div', 'span', 'p', 'label'].includes(tagName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为有效的表单字段
     */
    isValidFormField(element) {
        console.log(`Checking element: ${element.tagName}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ')[0] : ''}`);

        // 基本检查
        if (!element || !element.isConnected) {
            console.log('  ❌ Element not connected to DOM');
            return false;
        }

        // 排除明显的隐藏字段
        if (element.type === 'hidden') {
            console.log('  ❌ Hidden input type');
            return false;
        }

        // 排除按钮类型
        const excludeTypes = ['button', 'submit', 'reset', 'image', 'file'];
        if (excludeTypes.includes(element.type)) {
            console.log(`  ❌ Excluded type: ${element.type}`);
            return false;
        }

        // 获取计算样式
        let style;
        try {
            style = window.getComputedStyle(element);
        } catch (error) {
            console.log('  ⚠️ Cannot get computed style');
            style = {};
        }

        // 检查可见性（更宽松的检查）
        if (style.display === 'none') {
            console.log('  ❌ Display none');
            return false;
        }

        // 检查元素尺寸（更宽松的检查）
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 && rect.height === 0) {
            console.log('  ❌ Zero dimensions');
            return false;
        }

        // 检查是否为可交互元素
        const tagName = element.tagName.toLowerCase();
        const isStandardInput = ['input', 'textarea', 'select'].includes(tagName);
        const isContentEditable = element.contentEditable === 'true';
        const hasRole = element.hasAttribute('role');

        if (isStandardInput || isContentEditable || hasRole) {
            console.log(`  ✅ Valid interactive element (${tagName})`);
            return true;
        }

        // 对于非标准控件，检查属性和class
        const hasName = element.hasAttribute('name');
        const hasDataField = element.hasAttribute('data-field');
        const className = element.className.toLowerCase();
        const hasInputClass = [
            'input', 'field', 'form', 'text', 'edit', 'control'
        ].some(keyword => className.includes(keyword));

        if (hasName || hasDataField || hasInputClass) {
            console.log(`  ✅ Valid by attributes/class (name:${hasName}, data-field:${hasDataField}, class:${hasInputClass})`);
            return true;
        }

        console.log('  ❌ Not a valid form field');
        return false;
    }

    /**
     * 提取字段信息
     */
    extractFieldInfo(element) {
        const info = {
            element: element,
            id: element.id || '',
            name: element.name || '',
            type: element.type || element.tagName.toLowerCase(),
            tagName: element.tagName.toLowerCase(),
            placeholder: element.placeholder || '',
            value: this.getElementValue(element),
            className: element.className || '',
            label: this.findFieldLabel(element),
            xpath: this.getElementXPath(element),
            // 添加更多属性
            dataField: element.getAttribute('data-field') || '',
            dataName: element.getAttribute('data-name') || '',
            title: element.title || '',
            ariaLabel: element.getAttribute('aria-label') || '',
            role: element.getAttribute('role') || ''
        };

        // 生成字段的文本描述（用于匹配）
        info.textContent = [
            info.id,
            info.name,
            info.label,
            info.placeholder,
            info.className,
            info.dataField,
            info.dataName,
            info.title,
            info.ariaLabel
        ].filter(Boolean).join(' ').toLowerCase();

        // 添加调试信息
        console.log('Extracted field info:', {
            tag: info.tagName,
            id: info.id,
            name: info.name,
            label: info.label,
            className: info.className
        });

        return info;
    }

    /**
     * 获取元素的值
     */
    getElementValue(element) {
        if (element.tagName.toLowerCase() === 'select') {
            return element.selectedOptions.length > 0 ? element.selectedOptions[0].text : '';
        } else if (element.contentEditable === 'true') {
            return element.textContent || element.innerText || '';
        } else {
            return element.value || '';
        }
    }

    /**
     * 查找字段标签
     */
    findFieldLabel(element) {
        let label = '';

        // 1. 通过for属性关联的label
        if (element.id) {
            const labelElement = document.querySelector(`label[for="${element.id}"]`);
            if (labelElement) {
                label = labelElement.textContent.trim();
                if (label) return this.cleanLabelText(label);
            }
        }

        // 2. 父级label
        const parentLabel = element.closest('label');
        if (parentLabel) {
            label = parentLabel.textContent.replace(element.value || '', '').trim();
            if (label) return this.cleanLabelText(label);
        }

        // 3. aria-label属性
        if (element.getAttribute('aria-label')) {
            return this.cleanLabelText(element.getAttribute('aria-label'));
        }

        // 4. title属性
        if (element.title) {
            return this.cleanLabelText(element.title);
        }

        // 5. placeholder作为标签
        if (element.placeholder && element.placeholder.length < 30) {
            return this.cleanLabelText(element.placeholder);
        }

        // 6. 查找同一行或附近的文本标签
        label = this.findNearbyLabel(element);
        if (label) return this.cleanLabelText(label);

        // 7. 查找表格中的表头
        label = this.findTableHeader(element);
        if (label) return this.cleanLabelText(label);

        // 8. 查找父级容器中的标签文本
        label = this.findContainerLabel(element);
        if (label) return this.cleanLabelText(label);

        return '';
    }

    /**
     * 清理标签文本
     */
    cleanLabelText(text) {
        return text
            .replace(/[：:*]/g, '') // 移除冒号和星号
            .replace(/\s+/g, ' ') // 合并空白字符
            .trim();
    }

    /**
     * 查找附近的标签
     */
    findNearbyLabel(element) {
        // 查找前面的兄弟元素
        let prev = element.previousElementSibling;
        let attempts = 0;
        while (prev && attempts < 3) {
            const text = prev.textContent?.trim();
            if (text && text.length < 50 && text.length > 1) {
                // 排除纯数字或特殊字符
                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                    return text;
                }
            }
            prev = prev.previousElementSibling;
            attempts++;
        }

        // 查找后面的兄弟元素（某些布局中标签在后面）
        let next = element.nextElementSibling;
        attempts = 0;
        while (next && attempts < 2) {
            const text = next.textContent?.trim();
            if (text && text.length < 30 && text.length > 1) {
                if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                    return text;
                }
            }
            next = next.nextElementSibling;
            attempts++;
        }

        return '';
    }

    /**
     * 查找表格表头
     */
    findTableHeader(element) {
        const cell = element.closest('td, th');
        if (!cell) return '';

        const table = cell.closest('table');
        if (!table) return '';

        const cellIndex = Array.from(cell.parentNode.children).indexOf(cell);
        const headerRow = table.querySelector('thead tr, tr:first-child');

        if (headerRow) {
            const headerCell = headerRow.children[cellIndex];
            if (headerCell) {
                const text = headerCell.textContent?.trim();
                if (text && text.length < 50) {
                    return text;
                }
            }
        }

        return '';
    }

    /**
     * 查找容器标签
     */
    findContainerLabel(element) {
        // 查找父级容器中的标签文本
        let parent = element.parentElement;
        let level = 0;

        while (parent && level < 3) {
            // 查找容器中的直接文本节点
            const textNodes = this.getDirectTextNodes(parent);
            for (const node of textNodes) {
                const text = node.textContent?.trim();
                if (text && text.length < 50 && text.length > 1) {
                    if (!/^\d+$/.test(text) && !/^[^\w\u4e00-\u9fa5]+$/.test(text)) {
                        return text;
                    }
                }
            }

            parent = parent.parentElement;
            level++;
        }

        return '';
    }

    /**
     * 获取直接文本节点（不包括子元素的文本）
     */
    getDirectTextNodes(element) {
        const textNodes = [];
        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        return textNodes;
    }

    /**
     * 获取元素的XPath
     */
    getElementXPath(element) {
        if (element.id) {
            return `//*[@id="${element.id}"]`;
        }
        
        const parts = [];
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let index = 0;
            let sibling = element.previousSibling;
            
            while (sibling) {
                if (sibling.nodeType === Node.ELEMENT_NODE && sibling.tagName === element.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            const tagName = element.tagName.toLowerCase();
            const pathIndex = index > 0 ? `[${index + 1}]` : '';
            parts.unshift(`${tagName}${pathIndex}`);
            
            element = element.parentNode;
        }
        
        return parts.length ? '/' + parts.join('/') : '';
    }

    /**
     * 获取元素中的文本节点
     */
    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        
        return textNodes;
    }

    /**
     * 智能回填表单
     */
    async fillForm(parsedData) {
        console.log('Starting intelligent form fill with data:', parsedData);
        
        if (!parsedData || Object.keys(parsedData).length === 0) {
            throw new Error('没有可用的解析数据');
        }

        // 重新扫描页面（可能有动态字段）
        this.formFields = this.scanFormFields();
        
        const filledFields = [];
        const fieldMapping = this.getFieldMapping();

        // 遍历解析出的数据
        for (const [dataKey, dataValue] of Object.entries(parsedData)) {
            if (!dataValue || dataValue.toString().trim() === '') {
                continue;
            }

            // 查找匹配的字段
            const matchedField = this.findMatchingField(dataKey, dataValue, fieldMapping);
            
            if (matchedField) {
                try {
                    await this.fillField(matchedField, dataValue);
                    filledFields.push({
                        dataKey,
                        dataValue,
                        fieldInfo: {
                            id: matchedField.id,
                            name: matchedField.name,
                            label: matchedField.label
                        }
                    });
                } catch (error) {
                    console.error(`Failed to fill field ${dataKey}:`, error);
                }
            } else {
                console.warn(`No matching field found for ${dataKey}: ${dataValue}`);
            }
        }

        console.log(`Form fill completed. Filled ${filledFields.length} fields:`, filledFields);

        return {
            success: true,
            filledCount: filledFields.length,
            filledFields: filledFields,
            totalDataFields: Object.keys(parsedData).length
        };
    }

    /**
     * 获取字段映射配置
     */
    getFieldMapping() {
        return {
            // 提单相关
            'BL_NO': ['提单号', 'bill', 'bl', 'blno', 'bill_number', 'bl_number'],
            'SHIPPER': ['发货人', 'shipper', 'consignor', 'sender', 'from'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver', 'to'],
            'NOTIFY_PARTY': ['通知方', 'notify', 'notify_party'],
            'PORT_OF_LOADING': ['起运港', 'pol', 'loading', 'origin', 'from_port'],
            'PORT_OF_DISCHARGE': ['目的港', 'pod', 'discharge', 'destination', 'to_port'],
            'VESSEL_VOYAGE': ['船名', 'vessel', 'voyage', 'ship'],
            'CONTAINER_NO': ['集装箱', 'container', 'cntr', 'box'],
            'SEAL_NO': ['封条', 'seal'],
            'GOODS_DESCRIPTION': ['货物', 'goods', 'cargo', 'commodity', 'description'],
            'PACKAGES': ['件数', 'packages', 'pkgs', 'pieces'],
            'GROSS_WEIGHT': ['毛重', 'weight', 'gross'],
            'MEASUREMENT': ['体积', 'volume', 'cbm', 'measurement'],
            'ETD': ['开船', 'etd', 'departure'],
            'ETA': ['到港', 'eta', 'arrival'],
            'FREIGHT_TERMS': ['运费', 'freight', 'payment'],

            // 发票相关
            'INVOICE_NO': ['发票号', 'invoice', 'inv'],
            'INVOICE_DATE': ['发票日期', 'invoice_date', 'date'],
            'TOTAL_AMOUNT': ['金额', 'amount', 'total', 'value'],
            'CURRENCY': ['币种', 'currency'],
            'SELLER': ['卖方', 'seller', 'vendor'],
            'BUYER': ['买方', 'buyer', 'customer'],
            'TRADE_TERMS': ['贸易条款', 'terms', 'incoterms'],

            // 装箱单相关
            'PACKING_LIST_NO': ['装箱单', 'packing', 'pl'],
            'TOTAL_PACKAGES': ['总件数', 'total_packages'],
            'TOTAL_GROSS_WEIGHT': ['总毛重', 'total_weight'],
            'TOTAL_NET_WEIGHT': ['总净重', 'net_weight'],
            'TOTAL_VOLUME': ['总体积', 'total_volume']
        };
    }

    /**
     * 查找匹配的字段
     */
    findMatchingField(dataKey, dataValue, fieldMapping) {
        const aliases = fieldMapping[dataKey] || [dataKey.toLowerCase()];
        let bestMatch = null;
        let bestScore = 0;

        for (const field of this.formFields) {
            let score = 0;

            // 精确匹配字段名或ID
            if (field.name === dataKey || field.id === dataKey) {
                score += 100;
            }

            // 别名匹配
            for (const alias of aliases) {
                if (field.textContent.includes(alias)) {
                    score += 50;
                }
            }

            // 字段类型匹配
            if (this.isFieldTypeMatch(dataKey, dataValue, field)) {
                score += 20;
            }

            // 位置权重（表单中靠前的字段优先级更高）
            const formIndex = this.formFields.indexOf(field);
            score += Math.max(0, 10 - formIndex * 0.1);

            if (score > bestScore) {
                bestScore = score;
                bestMatch = field;
            }
        }

        // 只返回置信度足够高的匹配
        return bestScore > 30 ? bestMatch : null;
    }

    /**
     * 检查字段类型是否匹配
     */
    isFieldTypeMatch(dataKey, dataValue, field) {
        // 数字字段
        if (['PACKAGES', 'GROSS_WEIGHT', 'MEASUREMENT', 'TOTAL_AMOUNT'].includes(dataKey)) {
            return field.type === 'number' || /number|amount|weight|volume|quantity/i.test(field.textContent);
        }

        // 日期字段
        if (['ETD', 'ETA', 'INVOICE_DATE'].includes(dataKey)) {
            return field.type === 'date' || /date|time/i.test(field.textContent);
        }

        // 邮箱字段
        if (/email/i.test(dataKey)) {
            return field.type === 'email' || /email|mail/i.test(field.textContent);
        }

        // 长文本字段
        if (['SHIPPER', 'CONSIGNEE', 'GOODS_DESCRIPTION'].includes(dataKey)) {
            return field.tagName === 'textarea' || /address|description|detail/i.test(field.textContent);
        }

        return true;
    }

    /**
     * 填充字段
     */
    async fillField(field, value) {
        const element = field.element;
        
        // 聚焦字段
        element.focus();
        
        // 清空现有值
        element.value = '';
        
        // 设置新值
        if (field.tagName === 'select') {
            // 下拉框处理
            this.selectOption(element, value);
        } else {
            // 输入框处理
            element.value = value.toString();
        }
        
        // 触发事件
        this.triggerEvents(element);
        
        // 添加视觉效果
        this.addFillEffect(element);
        
        // 短暂延迟，模拟人工输入
        await this.sleep(100);
    }

    /**
     * 选择下拉框选项
     */
    selectOption(selectElement, value) {
        const options = selectElement.querySelectorAll('option');
        const valueStr = value.toString().toLowerCase();
        
        for (const option of options) {
            const optionText = option.textContent.toLowerCase();
            const optionValue = option.value.toLowerCase();
            
            if (optionValue === valueStr || 
                optionText === valueStr || 
                optionText.includes(valueStr) || 
                valueStr.includes(optionText)) {
                option.selected = true;
                selectElement.value = option.value;
                break;
            }
        }
    }

    /**
     * 触发相关事件
     */
    triggerEvents(element) {
        const events = ['input', 'change', 'blur', 'keyup'];
        
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            element.dispatchEvent(event);
        });
    }

    /**
     * 添加填充效果
     */
    addFillEffect(element) {
        const originalStyle = {
            backgroundColor: element.style.backgroundColor,
            borderColor: element.style.borderColor,
            transition: element.style.transition
        };
        
        // 添加高亮效果
        element.style.transition = 'all 0.3s ease';
        element.style.backgroundColor = '#e8f5e8';
        element.style.borderColor = '#28a745';
        
        // 3秒后恢复原样
        setTimeout(() => {
            element.style.backgroundColor = originalStyle.backgroundColor;
            element.style.borderColor = originalStyle.borderColor;
            element.style.transition = originalStyle.transition;
        }, 3000);
    }

    /**
     * 高亮显示所有可填写字段
     */
    highlightFields() {
        this.formFields.forEach(field => {
            const element = field.element;
            element.style.outline = '2px solid #007bff';
            element.style.outlineOffset = '2px';
            
            // 添加提示
            const tooltip = document.createElement('div');
            tooltip.textContent = field.label || field.name || field.id || '未知字段';
            tooltip.style.cssText = `
                position: absolute;
                background: #333;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 10000;
                pointer-events: none;
            `;
            
            const rect = element.getBoundingClientRect();
            tooltip.style.left = (rect.left + window.scrollX) + 'px';
            tooltip.style.top = (rect.top + window.scrollY - 30) + 'px';
            
            document.body.appendChild(tooltip);
            
            // 5秒后移除高亮
            setTimeout(() => {
                element.style.outline = '';
                element.style.outlineOffset = '';
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 5000);
        });
    }

    /**
     * 调试字段识别
     */
    debugFields() {
        console.log('Starting field debug...');

        // 重新扫描字段
        this.formFields = this.scanFormFields();

        // 返回详细的字段信息
        const debugInfo = {
            success: true,
            fields: this.formFields.map(field => ({
                tagName: field.tagName,
                type: field.type,
                id: field.id,
                name: field.name,
                className: field.className,
                label: field.label,
                placeholder: field.placeholder,
                value: field.value,
                textContent: field.textContent,
                xpath: field.xpath
            }))
        };

        console.log('Debug info:', debugInfo);
        return debugInfo;
    }

    /**
     * 高亮单个字段
     */
    highlightSingleField(fieldIndex) {
        if (fieldIndex >= 0 && fieldIndex < this.formFields.length) {
            const field = this.formFields[fieldIndex];
            const element = field.element;

            // 清除之前的高亮
            document.querySelectorAll('.freight-parser-debug-highlight').forEach(el => {
                el.classList.remove('freight-parser-debug-highlight');
            });

            // 高亮选中的字段
            element.classList.add('freight-parser-debug-highlight');
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 5秒后移除高亮
            setTimeout(() => {
                element.classList.remove('freight-parser-debug-highlight');
            }, 5000);
        }
    }

    /**
     * 分析页面元素
     */
    analyzePageElements() {
        console.log('Analyzing page elements...');

        const analysis = {
            totalElements: document.querySelectorAll('*').length,
            inputElements: {},
            frameworks: [],
            pageInfo: {
                url: window.location.href,
                title: document.title,
                domain: window.location.hostname
            }
        };

        // 统计各种输入元素
        const inputSelectors = {
            'input[type="text"]': 'Text inputs',
            'input[type="email"]': 'Email inputs',
            'input[type="number"]': 'Number inputs',
            'input[type="date"]': 'Date inputs',
            'input:not([type])': 'Default inputs',
            'textarea': 'Textareas',
            'select': 'Select boxes',
            '[contenteditable="true"]': 'Contenteditable',
            'input': 'All inputs',
            '[name]': 'Elements with name',
            '[data-field]': 'Elements with data-field',
            '[class*="input"]': 'Elements with input class',
            '[class*="field"]': 'Elements with field class'
        };

        for (const [selector, description] of Object.entries(inputSelectors)) {
            try {
                const elements = document.querySelectorAll(selector);
                analysis.inputElements[description] = {
                    count: elements.length,
                    selector: selector,
                    elements: Array.from(elements).slice(0, 5).map(el => ({
                        tag: el.tagName,
                        id: el.id,
                        name: el.name,
                        className: el.className,
                        type: el.type,
                        visible: this.isElementVisible(el)
                    }))
                };
            } catch (error) {
                analysis.inputElements[description] = { error: error.message };
            }
        }

        // 检测UI框架
        const frameworkChecks = {
            'Ant Design': () => document.querySelector('.ant-input, .antd') !== null,
            'Element UI': () => document.querySelector('.el-input, .el-form') !== null,
            'Bootstrap': () => document.querySelector('.form-control, .bootstrap') !== null,
            'Layui': () => document.querySelector('.layui-input, .layui-form') !== null,
            'iView': () => document.querySelector('.ivu-input, .iview') !== null,
            'Vue': () => window.Vue !== undefined || document.querySelector('[v-model]') !== null,
            'React': () => window.React !== undefined || document.querySelector('[data-reactroot]') !== null,
            'Angular': () => window.angular !== undefined || document.querySelector('[ng-app]') !== null,
            'jQuery': () => window.jQuery !== undefined || window.$ !== undefined
        };

        for (const [framework, check] of Object.entries(frameworkChecks)) {
            try {
                if (check()) {
                    analysis.frameworks.push(framework);
                }
            } catch (error) {
                // 忽略检测错误
            }
        }

        console.log('Page analysis result:', analysis);
        return {
            success: true,
            analysis: analysis
        };
    }

    /**
     * 检查元素是否可见
     */
    isElementVisible(element) {
        try {
            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);

            return rect.width > 0 &&
                   rect.height > 0 &&
                   style.display !== 'none' &&
                   style.visibility !== 'hidden';
        } catch (error) {
            return false;
        }
    }

    /**
     * 强制使用截图检测
     */
    async forceScreenshotDetection() {
        try {
            console.log('🔥 Force screenshot detection requested');

            // 使用内嵌的检测器
            const detector = new EmbeddedScreenshotDetector();
            const result = await detector.detectFields();

            if (result.success) {
                this.formFields = result.fields;
                return {
                    success: true,
                    formCount: 1,
                    fieldCount: result.fields.length,
                    method: 'embedded_screenshot',
                    confidence: result.confidence
                };
            } else {
                return {
                    success: false,
                    error: result.error,
                    fieldCount: 0
                };
            }

        } catch (error) {
            console.error('Force screenshot detection failed:', error);
            return {
                success: false,
                error: error.message,
                fieldCount: 0
            };
        }
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化Content Script
if (typeof window !== 'undefined') {
    // 避免重复初始化
    if (!window.freightParserContent) {
        window.freightParserContent = new FreightParserContent();
        console.log('FreightParser Content Script initialized');
    } else {
        console.log('FreightParser Content Script already exists');
    }
}
