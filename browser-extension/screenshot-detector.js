/**
 * 基于截图的字段检测器
 * 使用浏览器原生API + 简单图像处理
 * 不依赖外部库，直接分析页面视觉结构
 */

class ScreenshotFieldDetector {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        console.log('ScreenshotFieldDetector initialized');
    }

    /**
     * 主检测流程
     */
    async detectFields() {
        try {
            console.log('🔍 Starting screenshot-based detection...');
            
            // 1. 截取页面
            const screenshot = await this.capturePageScreenshot();
            
            // 2. 分析图像找到输入框区域
            const inputRegions = await this.findInputRegions(screenshot);
            
            // 3. 分析文字找到标签
            const textLabels = await this.findTextLabels(screenshot);
            
            // 4. 匹配输入框和标签
            const matchedFields = await this.matchFieldsAndLabels(inputRegions, textLabels);
            
            // 5. 映射回DOM元素
            const domMappedFields = await this.mapToDOMElements(matchedFields);
            
            console.log(`✅ Screenshot detection found ${domMappedFields.length} fields`);
            
            return {
                success: true,
                fields: domMappedFields,
                method: 'screenshot',
                confidence: this.calculateConfidence(domMappedFields)
            };
            
        } catch (error) {
            console.error('❌ Screenshot detection failed:', error);
            return {
                success: false,
                error: error.message,
                fields: []
            };
        }
    }

    /**
     * 截取页面截图
     */
    async capturePageScreenshot() {
        return new Promise((resolve, reject) => {
            // 使用Chrome扩展API截图
            chrome.tabs.captureVisibleTab(null, {
                format: 'png',
                quality: 100
            }, (dataUrl) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    console.log('📸 Screenshot captured');
                    resolve(dataUrl);
                }
            });
        });
    }

    /**
     * 查找输入框区域
     */
    async findInputRegions(screenshotDataUrl) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const regions = this.detectRectangularRegions(canvas);
                console.log(`📦 Found ${regions.length} potential input regions`);
                resolve(regions);
            };
            img.src = screenshotDataUrl;
        });
    }

    /**
     * 检测矩形区域（简化的边缘检测）
     */
    detectRectangularRegions(canvas) {
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        const width = canvas.width;
        const height = canvas.height;
        
        const regions = [];
        const minWidth = 80;  // 最小宽度
        const minHeight = 20; // 最小高度
        const stepSize = 10;  // 扫描步长
        
        // 扫描图像寻找矩形边框
        for (let y = 0; y < height - minHeight; y += stepSize) {
            for (let x = 0; x < width - minWidth; x += stepSize) {
                const region = this.analyzeRegion(data, width, x, y, minWidth, minHeight);
                if (region && region.score > 0.3) {
                    // 检查是否与已有区域重叠
                    if (!this.isOverlapping(region, regions)) {
                        regions.push(region);
                    }
                }
            }
        }
        
        return regions.sort((a, b) => b.score - a.score).slice(0, 50); // 返回前50个最可能的区域
    }

    /**
     * 分析区域是否像输入框
     */
    analyzeRegion(data, width, x, y, w, h) {
        // 检查边框特征
        const borderScore = this.checkBorderPattern(data, width, x, y, w, h);
        
        // 检查内部是否为浅色（输入框通常是白色或浅色背景）
        const backgroundScore = this.checkBackgroundColor(data, width, x, y, w, h);
        
        // 检查宽高比（输入框通常比较宽）
        const aspectRatio = w / h;
        const ratioScore = aspectRatio > 2 && aspectRatio < 20 ? 0.8 : 0.2;
        
        const totalScore = (borderScore * 0.4 + backgroundScore * 0.4 + ratioScore * 0.2);
        
        if (totalScore > 0.3) {
            return {
                x: x,
                y: y,
                width: w,
                height: h,
                score: totalScore,
                type: 'input_region'
            };
        }
        
        return null;
    }

    /**
     * 检查边框模式
     */
    checkBorderPattern(data, width, x, y, w, h) {
        let borderPixels = 0;
        let totalChecked = 0;
        
        // 检查上边框
        for (let i = 0; i < w; i += 2) {
            const pixelIndex = ((y * width) + (x + i)) * 4;
            if (this.isDarkPixel(data, pixelIndex)) borderPixels++;
            totalChecked++;
        }
        
        // 检查下边框
        for (let i = 0; i < w; i += 2) {
            const pixelIndex = (((y + h - 1) * width) + (x + i)) * 4;
            if (this.isDarkPixel(data, pixelIndex)) borderPixels++;
            totalChecked++;
        }
        
        // 检查左边框
        for (let i = 0; i < h; i += 2) {
            const pixelIndex = (((y + i) * width) + x) * 4;
            if (this.isDarkPixel(data, pixelIndex)) borderPixels++;
            totalChecked++;
        }
        
        // 检查右边框
        for (let i = 0; i < h; i += 2) {
            const pixelIndex = (((y + i) * width) + (x + w - 1)) * 4;
            if (this.isDarkPixel(data, pixelIndex)) borderPixels++;
            totalChecked++;
        }
        
        return borderPixels / totalChecked;
    }

    /**
     * 检查背景颜色
     */
    checkBackgroundColor(data, width, x, y, w, h) {
        let lightPixels = 0;
        let totalChecked = 0;
        
        // 检查内部区域
        for (let dy = 2; dy < h - 2; dy += 3) {
            for (let dx = 2; dx < w - 2; dx += 3) {
                const pixelIndex = (((y + dy) * width) + (x + dx)) * 4;
                if (this.isLightPixel(data, pixelIndex)) lightPixels++;
                totalChecked++;
            }
        }
        
        return totalChecked > 0 ? lightPixels / totalChecked : 0;
    }

    /**
     * 判断是否为深色像素（边框）
     */
    isDarkPixel(data, index) {
        const r = data[index];
        const g = data[index + 1];
        const b = data[index + 2];
        const brightness = (r + g + b) / 3;
        return brightness < 128;
    }

    /**
     * 判断是否为浅色像素（背景）
     */
    isLightPixel(data, index) {
        const r = data[index];
        const g = data[index + 1];
        const b = data[index + 2];
        const brightness = (r + g + b) / 3;
        return brightness > 200;
    }

    /**
     * 检查区域是否重叠
     */
    isOverlapping(newRegion, existingRegions) {
        return existingRegions.some(region => {
            const overlapX = Math.max(0, Math.min(newRegion.x + newRegion.width, region.x + region.width) - Math.max(newRegion.x, region.x));
            const overlapY = Math.max(0, Math.min(newRegion.y + newRegion.height, region.y + region.height) - Math.max(newRegion.y, region.y));
            const overlapArea = overlapX * overlapY;
            const newRegionArea = newRegion.width * newRegion.height;
            return overlapArea / newRegionArea > 0.5; // 50%重叠阈值
        });
    }

    /**
     * 查找文字标签
     */
    async findTextLabels(screenshotDataUrl) {
        // 简化版：直接从DOM中提取文字信息和位置
        const labels = [];
        
        // 查找所有可能的标签文字
        const textElements = document.querySelectorAll('label, span, div, td, th, p');
        
        textElements.forEach(element => {
            const text = element.textContent.trim();
            if (text && text.length > 1 && text.length < 50) {
                const rect = element.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {
                    labels.push({
                        text: this.cleanText(text),
                        x: rect.left + window.scrollX,
                        y: rect.top + window.scrollY,
                        width: rect.width,
                        height: rect.height,
                        element: element
                    });
                }
            }
        });
        
        console.log(`📝 Found ${labels.length} text labels`);
        return labels;
    }

    /**
     * 清理文字
     */
    cleanText(text) {
        return text.replace(/[：:*]/g, '').trim();
    }

    /**
     * 匹配输入框和标签
     */
    async matchFieldsAndLabels(inputRegions, textLabels) {
        const matchedFields = [];
        
        for (const region of inputRegions) {
            // 查找最近的标签
            const nearbyLabels = this.findNearbyLabels(region, textLabels);
            
            if (nearbyLabels.length > 0) {
                const bestLabel = nearbyLabels[0];
                const fieldType = this.guessFieldType(bestLabel.text);
                
                matchedFields.push({
                    region: region,
                    label: bestLabel,
                    fieldType: fieldType.type,
                    confidence: fieldType.confidence * region.score,
                    x: region.x,
                    y: region.y,
                    width: region.width,
                    height: region.height
                });
            }
        }
        
        console.log(`🔗 Matched ${matchedFields.length} fields with labels`);
        return matchedFields;
    }

    /**
     * 查找附近的标签
     */
    findNearbyLabels(region, textLabels) {
        const maxDistance = 200;
        const nearbyLabels = [];
        
        for (const label of textLabels) {
            const distance = this.calculateDistance(region, label);
            if (distance < maxDistance) {
                nearbyLabels.push({
                    ...label,
                    distance: distance
                });
            }
        }
        
        return nearbyLabels.sort((a, b) => a.distance - b.distance);
    }

    /**
     * 计算距离
     */
    calculateDistance(region, label) {
        const regionCenterX = region.x + region.width / 2;
        const regionCenterY = region.y + region.height / 2;
        const labelCenterX = label.x + label.width / 2;
        const labelCenterY = label.y + label.height / 2;
        
        return Math.sqrt(
            Math.pow(regionCenterX - labelCenterX, 2) + 
            Math.pow(regionCenterY - labelCenterY, 2)
        );
    }

    /**
     * 猜测字段类型
     */
    guessFieldType(labelText) {
        const fieldPatterns = {
            'BL_NO': ['提单号', '提单', 'bl', 'bill'],
            'SHIPPER': ['发货人', '托运人', 'shipper', '发货'],
            'CONSIGNEE': ['收货人', 'consignee', '收货'],
            'PORT_OF_LOADING': ['起运港', '装货港', 'pol', '起运'],
            'PORT_OF_DISCHARGE': ['目的港', '卸货港', 'pod', '目的'],
            'VESSEL_VOYAGE': ['船名', '航次', 'vessel', '船'],
            'CONTAINER_NO': ['集装箱', '箱号', 'container', '集装'],
            'INVOICE_NO': ['发票号', '发票', 'invoice'],
            'TOTAL_AMOUNT': ['金额', '总额', 'amount', '价格']
        };
        
        const text = labelText.toLowerCase();
        
        for (const [fieldType, keywords] of Object.entries(fieldPatterns)) {
            for (const keyword of keywords) {
                if (text.includes(keyword.toLowerCase())) {
                    return { type: fieldType, confidence: 0.8 };
                }
            }
        }
        
        return { type: 'UNKNOWN', confidence: 0.3 };
    }

    /**
     * 映射到DOM元素
     */
    async mapToDOMElements(matchedFields) {
        const domFields = [];
        
        for (const field of matchedFields) {
            // 使用坐标查找DOM元素
            const centerX = field.x + field.width / 2;
            const centerY = field.y + field.height / 2;
            
            const element = document.elementFromPoint(centerX, centerY);
            
            if (element && this.isInputElement(element)) {
                domFields.push({
                    element: element,
                    fieldType: field.fieldType,
                    confidence: field.confidence,
                    coordinates: {
                        x: field.x,
                        y: field.y,
                        width: field.width,
                        height: field.height
                    },
                    label: field.label.text,
                    method: 'screenshot'
                });
            }
        }
        
        return domFields;
    }

    /**
     * 检查是否为输入元素
     */
    isInputElement(element) {
        const inputTags = ['input', 'textarea', 'select'];
        return inputTags.includes(element.tagName.toLowerCase()) ||
               element.contentEditable === 'true';
    }

    /**
     * 计算置信度
     */
    calculateConfidence(fields) {
        if (fields.length === 0) return 0;
        
        const totalConfidence = fields.reduce((sum, field) => sum + field.confidence, 0);
        return totalConfidence / fields.length;
    }
}

// 全局导出
if (typeof window !== 'undefined') {
    window.ScreenshotFieldDetector = ScreenshotFieldDetector;
}
