// 货代单证智能解析扩展 - 简化版Background Script

console.log('Background script starting...');

// 扩展安装时的处理
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed:', details.reason);
    
    // 设置默认配置
    const defaultSettings = {
        apiBaseUrl: 'http://localhost:8080/api',
        autoDetectDocumentType: true,
        enableOcr: true,
        ocrLanguage: 'chi_sim+eng',
        enableNotifications: true,
        enableAutoFill: true,
        theme: 'light'
    };
    
    chrome.storage.sync.set(defaultSettings).then(() => {
        console.log('Default settings saved');
    }).catch((error) => {
        console.error('Failed to save settings:', error);
    });
});

// 消息处理
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Received message:', request.action);
    
    switch (request.action) {
        case 'getSettings':
            chrome.storage.sync.get().then((settings) => {
                sendResponse({ success: true, data: settings });
            }).catch((error) => {
                sendResponse({ success: false, error: error.message });
            });
            break;
            
        case 'saveSettings':
            chrome.storage.sync.set(request.settings).then(() => {
                sendResponse({ success: true });
            }).catch((error) => {
                sendResponse({ success: false, error: error.message });
            });
            break;
            
        case 'checkApiConnection':
            fetch(`${request.apiUrl}/document/health`)
                .then(response => response.json())
                .then(data => {
                    sendResponse({ success: true, data: data });
                })
                .catch(error => {
                    sendResponse({ success: false, error: error.message });
                });
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
    
    return true; // 保持消息通道开放
});

console.log('Background script loaded successfully');
