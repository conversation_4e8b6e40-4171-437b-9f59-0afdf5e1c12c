# 货代单证智能解析浏览器扩展

## 📋 概述

货代单证智能解析浏览器扩展是一个专为货代行业设计的Chrome扩展，通过AI技术实现单证的智能解析和表单自动回填，大幅提升工作效率。

## ✨ 主要功能

- 🚢 **多种单证支持**：海运提单、商业发票、装箱单、报关单等
- 🤖 **AI智能解析**：准确率高达90%以上
- 📝 **智能表单回填**：自动匹配并填写表单字段
- 🔍 **OCR文字识别**：支持PDF和图片格式文档
- ⚡ **零配置使用**：安装即可使用，无需复杂设置
- 🔒 **数据安全**：本地处理，保护隐私

## 🚀 安装方法

### 方法一：开发者模式安装（推荐）

1. **下载扩展文件**
   ```bash
   git clone <repository-url>
   cd browser-extension
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `browser-extension` 文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 扩展列表中出现"货代单证智能解析助手"
   - 浏览器工具栏出现扩展图标

### 方法二：打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择 `browser-extension` 文件夹
   - 生成 `.crx` 文件

2. **安装打包文件**
   - 将 `.crx` 文件拖拽到扩展管理页面
   - 点击"添加扩展程序"

## 🔧 配置说明

### 首次使用配置

1. **点击扩展图标**，打开设置页面
2. **配置API服务地址**：
   - 默认：`http://localhost:8080/api`
   - 如果后端服务部署在其他地址，请修改此配置
3. **测试连接**：点击"测试连接"按钮验证服务可用性
4. **保存设置**

### 高级配置选项

- **自动检测文档类型**：启用后无需手动选择单证类型
- **OCR语言设置**：根据文档语言选择合适的识别语言
- **智能回填**：启用后解析完成自动回填表单
- **通知提醒**：启用桌面通知功能

## 📖 使用指南

### 基本使用流程

1. **打开货代相关网页**
   - 任何包含表单的货代业务网页
   - 扩展会自动检测表单字段

2. **点击扩展图标**
   - 浏览器工具栏中的扩展图标
   - 打开解析面板

3. **上传单证文件**
   - 点击上传区域或拖拽文件
   - 支持PDF、JPG、PNG格式
   - 文件大小限制：20MB

4. **选择文档类型**（可选）
   - 如果启用自动检测，可跳过此步
   - 手动选择可提高解析准确率

5. **开始解析**
   - 点击"开始解析"按钮
   - 等待AI解析完成

6. **智能回填**
   - 解析完成后点击"智能回填"
   - 扩展自动匹配并填写表单字段

### 高级功能

#### 字段匹配优化
- 扩展会智能匹配字段名称、标签、占位符
- 支持中英文字段识别
- 可在设置中自定义字段映射关系

#### 批量处理
- 支持连续解析多个文档
- 历史记录保存解析结果
- 可重复使用之前的解析数据

#### 调试模式
- 启用调试模式查看详细日志
- 帮助排查解析和回填问题
- 在开发者工具控制台查看信息

## 🛠️ 故障排除

### 常见问题

#### 1. 扩展无法加载
**症状**：扩展安装失败或无法启用
**解决方案**：
- 确保Chrome版本 ≥ 88
- 检查manifest.json语法是否正确
- 查看扩展管理页面的错误信息

#### 2. 无法连接API服务
**症状**：显示"连接失败"或"服务不可用"
**解决方案**：
- 确保后端服务正在运行
- 检查API地址配置是否正确
- 验证网络连接和防火墙设置
- 查看浏览器控制台的网络错误

#### 3. 文档解析失败
**症状**：上传文件后解析失败
**解决方案**：
- 检查文件格式是否支持
- 确认文件大小不超过20MB
- 尝试不同的文档类型设置
- 查看错误信息详情

#### 4. 表单回填不准确
**症状**：字段匹配错误或回填失败
**解决方案**：
- 检查页面表单结构
- 启用调试模式查看匹配过程
- 自定义字段映射关系
- 手动调整解析结果

#### 5. 权限问题
**症状**：扩展无法访问页面或API
**解决方案**：
- 检查扩展权限设置
- 确认host_permissions配置
- 重新安装扩展
- 检查浏览器安全设置

### 调试方法

#### 启用调试模式
1. 打开扩展设置页面
2. 切换到"高级选项"标签
3. 启用"调试模式"
4. 保存设置

#### 查看调试信息
1. 打开浏览器开发者工具（F12）
2. 切换到"控制台"标签
3. 查看以"FreightParser"开头的日志信息
4. 分析错误信息和执行流程

#### 检查网络请求
1. 在开发者工具中切换到"网络"标签
2. 执行解析操作
3. 查看API请求和响应
4. 检查请求状态和错误信息

## 🔒 隐私和安全

### 数据处理
- **本地处理**：文件在本地浏览器中处理
- **API传输**：仅向配置的API服务发送数据
- **不存储敏感信息**：不在扩展中永久存储文档内容
- **用户控制**：用户完全控制数据的上传和处理

### 权限说明
- **activeTab**：访问当前活动标签页
- **storage**：保存扩展设置
- **scripting**：注入内容脚本
- **tabs**：获取标签页信息
- **host_permissions**：访问指定域名的API

### 安全建议
- 仅在可信的网站上使用扩展
- 定期更新扩展到最新版本
- 不要在公共网络上处理敏感文档
- 及时清理浏览器缓存和历史记录

## 🤝 贡献指南

### 开发环境搭建
1. 克隆项目代码
2. 安装开发依赖
3. 启动后端服务
4. 加载扩展进行测试

### 代码结构
```
browser-extension/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗页面
├── popup.js               # 弹窗脚本
├── popup.css              # 弹窗样式
├── content.js             # 内容脚本
├── content.css            # 内容样式
├── background.js          # 后台脚本
├── options.html           # 设置页面
├── options.js             # 设置脚本
├── options.css            # 设置样式
└── icons/                 # 图标文件
```

### 提交规范
- 使用清晰的提交信息
- 遵循代码风格规范
- 添加必要的测试用例
- 更新相关文档

## 📞 技术支持

### 联系方式
- **邮箱**：<EMAIL>
- **GitHub**：https://github.com/example/freight-parser-extension
- **文档**：https://docs.example.com

### 反馈渠道
- GitHub Issues：报告Bug和功能请求
- 邮件支持：技术问题和使用咨询
- 用户社区：经验分享和讨论

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！
