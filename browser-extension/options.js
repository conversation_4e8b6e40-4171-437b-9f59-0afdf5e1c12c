/**
 * 货代单证智能解析浏览器扩展 - 设置页面脚本
 */

class FreightParserOptions {
    constructor() {
        this.settings = {};
        this.originalSettings = {};
        this.hasUnsavedChanges = false;
        
        this.init();
    }

    async init() {
        // 绑定事件
        this.bindEvents();
        
        // 加载设置
        await this.loadSettings();
        
        // 加载使用统计
        await this.loadUsageStats();
        
        // 初始化标签页
        this.initTabs();
        
        console.log('Options page initialized');
    }

    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 设置变更监听
        this.bindSettingEvents();

        // 按钮事件
        document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
        document.getElementById('cancelSettings').addEventListener('click', () => this.cancelSettings());
        document.getElementById('testConnection').addEventListener('click', () => this.testConnection());
        document.getElementById('resetSettings').addEventListener('click', () => this.resetSettings());
        document.getElementById('exportSettings').addEventListener('click', () => this.exportSettings());
        document.getElementById('importSettings').addEventListener('click', () => this.importSettings());
        document.getElementById('editMappings').addEventListener('click', () => this.editMappings());

        // 链接事件
        document.getElementById('helpLink').addEventListener('click', () => this.openHelp());
        document.getElementById('feedbackLink').addEventListener('click', () => this.openFeedback());
        document.getElementById('githubLink').addEventListener('click', () => this.openGitHub());

        // 文件导入
        document.getElementById('importFile').addEventListener('change', (e) => this.handleImportFile(e));

        // 页面关闭前检查
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
            }
        });
    }

    bindSettingEvents() {
        // 复选框
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => this.markAsChanged());
        });

        // 输入框
        document.querySelectorAll('.setting-input').forEach(input => {
            input.addEventListener('input', () => this.markAsChanged());
        });

        // 下拉框
        document.querySelectorAll('.setting-select').forEach(select => {
            select.addEventListener('change', () => this.markAsChanged());
        });
    }

    initTabs() {
        // 默认显示第一个标签页
        this.switchTab('general');
    }

    switchTab(tabName) {
        // 隐藏所有标签内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 移除所有标签按钮的激活状态
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // 显示选中的标签内容
        const targetContent = document.getElementById(tabName);
        const targetButton = document.querySelector(`[data-tab="${tabName}"]`);

        if (targetContent && targetButton) {
            targetContent.classList.add('active');
            targetButton.classList.add('active');
        }
    }

    async loadSettings() {
        try {
            this.settings = await chrome.storage.sync.get();
            this.originalSettings = { ...this.settings };
            
            // 填充表单
            this.populateForm();
            
            console.log('Settings loaded:', this.settings);
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.showStatus('加载设置失败', 'error');
        }
    }

    populateForm() {
        // 常规设置
        this.setCheckbox('autoDetectDocumentType', this.settings.autoDetectDocumentType);
        this.setCheckbox('enableOcr', this.settings.enableOcr);
        this.setCheckbox('enableAutoFill', this.settings.enableAutoFill);
        this.setCheckbox('enableNotifications', this.settings.enableNotifications);
        this.setValue('ocrLanguage', this.settings.ocrLanguage);
        this.setValue('theme', this.settings.theme);

        // API配置
        this.setValue('apiBaseUrl', this.settings.apiBaseUrl);
        this.setValue('apiTimeout', this.settings.apiTimeout || 30);
        this.setValue('maxRetries', this.settings.maxRetries || 3);
        this.setValue('clientId', this.settings.clientId || 'browser_extension');
        this.setValue('apiKey', this.settings.apiKey || '');

        // 高级选项
        this.setCheckbox('enableCustomMapping', this.settings.enableCustomMapping);
        this.setCheckbox('enableDebugMode', this.settings.enableDebugMode);
        this.setCheckbox('saveDebugLogs', this.settings.saveDebugLogs);
    }

    setCheckbox(id, value) {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.checked = !!value;
        }
    }

    setValue(id, value) {
        const element = document.getElementById(id);
        if (element && value !== undefined) {
            element.value = value;
        }
    }

    collectFormData() {
        return {
            // 常规设置
            autoDetectDocumentType: document.getElementById('autoDetectDocumentType').checked,
            enableOcr: document.getElementById('enableOcr').checked,
            enableAutoFill: document.getElementById('enableAutoFill').checked,
            enableNotifications: document.getElementById('enableNotifications').checked,
            ocrLanguage: document.getElementById('ocrLanguage').value,
            theme: document.getElementById('theme').value,

            // API配置
            apiBaseUrl: document.getElementById('apiBaseUrl').value.trim(),
            apiTimeout: parseInt(document.getElementById('apiTimeout').value) || 30,
            maxRetries: parseInt(document.getElementById('maxRetries').value) || 3,
            clientId: document.getElementById('clientId').value.trim() || 'browser_extension',
            apiKey: document.getElementById('apiKey').value.trim(),

            // 高级选项
            enableCustomMapping: document.getElementById('enableCustomMapping').checked,
            enableDebugMode: document.getElementById('enableDebugMode').checked,
            saveDebugLogs: document.getElementById('saveDebugLogs').checked
        };
    }

    async saveSettings() {
        try {
            this.showStatus('正在保存...', 'saving');

            const newSettings = this.collectFormData();
            
            // 验证设置
            const validation = this.validateSettings(newSettings);
            if (!validation.valid) {
                this.showStatus(validation.message, 'error');
                return;
            }

            // 保存到存储
            await chrome.storage.sync.set(newSettings);
            
            this.settings = newSettings;
            this.originalSettings = { ...newSettings };
            this.hasUnsavedChanges = false;
            
            this.showStatus('设置已保存', 'success');
            
            // 通知background script设置已更新
            chrome.runtime.sendMessage({
                action: 'settingsUpdated',
                settings: newSettings
            });
            
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showStatus('保存失败: ' + error.message, 'error');
        }
    }

    validateSettings(settings) {
        // 验证API地址
        if (!settings.apiBaseUrl) {
            return { valid: false, message: 'API服务地址不能为空' };
        }

        try {
            new URL(settings.apiBaseUrl);
        } catch {
            return { valid: false, message: 'API服务地址格式不正确' };
        }

        // 验证超时时间
        if (settings.apiTimeout < 10 || settings.apiTimeout > 300) {
            return { valid: false, message: '超时时间必须在10-300秒之间' };
        }

        // 验证重试次数
        if (settings.maxRetries < 0 || settings.maxRetries > 5) {
            return { valid: false, message: '重试次数必须在0-5次之间' };
        }

        return { valid: true };
    }

    cancelSettings() {
        if (this.hasUnsavedChanges) {
            if (confirm('您有未保存的更改，确定要取消吗？')) {
                this.populateForm();
                this.hasUnsavedChanges = false;
                this.showStatus('已取消更改', 'success');
            }
        }
    }

    async testConnection() {
        try {
            const apiUrl = document.getElementById('apiBaseUrl').value.trim();
            if (!apiUrl) {
                this.showConnectionStatus('请先输入API地址', 'error');
                return;
            }

            this.showConnectionStatus('正在测试连接...', 'testing');

            const response = await fetch(`${apiUrl}/document/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.showConnectionStatus('连接成功 ✓', 'success');
                console.log('Connection test result:', data);
            } else {
                this.showConnectionStatus(`连接失败: HTTP ${response.status}`, 'error');
            }
        } catch (error) {
            this.showConnectionStatus(`连接失败: ${error.message}`, 'error');
        }
    }

    async resetSettings() {
        if (confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
            try {
                // 清除所有设置
                await chrome.storage.sync.clear();
                
                // 重新加载默认设置
                await this.loadSettings();
                
                this.hasUnsavedChanges = false;
                this.showStatus('设置已重置', 'success');
            } catch (error) {
                console.error('Failed to reset settings:', error);
                this.showStatus('重置失败: ' + error.message, 'error');
            }
        }
    }

    exportSettings() {
        try {
            const settingsJson = JSON.stringify(this.settings, null, 2);
            const blob = new Blob([settingsJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `freight-parser-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            URL.revokeObjectURL(url);
            this.showStatus('设置已导出', 'success');
        } catch (error) {
            console.error('Failed to export settings:', error);
            this.showStatus('导出失败: ' + error.message, 'error');
        }
    }

    importSettings() {
        document.getElementById('importFile').click();
    }

    async handleImportFile(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const importedSettings = JSON.parse(text);
            
            // 验证导入的设置
            const validation = this.validateSettings(importedSettings);
            if (!validation.valid) {
                this.showStatus('导入失败: ' + validation.message, 'error');
                return;
            }
            
            if (confirm('确定要导入这些设置吗？当前设置将被覆盖。')) {
                this.settings = importedSettings;
                this.populateForm();
                this.markAsChanged();
                this.showStatus('设置已导入，请保存生效', 'success');
            }
        } catch (error) {
            console.error('Failed to import settings:', error);
            this.showStatus('导入失败: 文件格式不正确', 'error');
        }
        
        // 清除文件选择
        event.target.value = '';
    }

    editMappings() {
        // 打开字段映射编辑器（简化版本）
        const mappingWindow = window.open('', '_blank', 'width=800,height=600');
        mappingWindow.document.write(`
            <html>
                <head>
                    <title>字段映射编辑器</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .mapping-item { margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                        .mapping-key { font-weight: bold; color: #007bff; }
                        .mapping-aliases { margin-top: 5px; }
                        .alias-tag { display: inline-block; background: #f0f0f0; padding: 2px 8px; margin: 2px; border-radius: 3px; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <h2>字段映射配置</h2>
                    <p>以下是当前的字段映射关系：</p>
                    <div id="mappings"></div>
                    <p><em>注意：字段映射编辑功能将在后续版本中提供完整的可视化编辑界面。</em></p>
                </body>
            </html>
        `);
        
        // 显示当前映射（简化显示）
        const mappings = this.getDefaultFieldMapping();
        const mappingsDiv = mappingWindow.document.getElementById('mappings');
        
        Object.entries(mappings).forEach(([key, aliases]) => {
            const item = mappingWindow.document.createElement('div');
            item.className = 'mapping-item';
            item.innerHTML = `
                <div class="mapping-key">${key}</div>
                <div class="mapping-aliases">
                    ${aliases.map(alias => `<span class="alias-tag">${alias}</span>`).join('')}
                </div>
            `;
            mappingsDiv.appendChild(item);
        });
    }

    getDefaultFieldMapping() {
        return {
            'BL_NO': ['提单号', 'bill', 'bl', 'blno'],
            'SHIPPER': ['发货人', 'shipper', 'consignor'],
            'CONSIGNEE': ['收货人', 'consignee', 'receiver'],
            'PORT_OF_LOADING': ['起运港', 'pol', 'loading'],
            'PORT_OF_DISCHARGE': ['目的港', 'pod', 'discharge'],
            'VESSEL_VOYAGE': ['船名', 'vessel', 'voyage'],
            'CONTAINER_NO': ['集装箱', 'container', 'cntr'],
            'INVOICE_NO': ['发票号', 'invoice'],
            'TOTAL_AMOUNT': ['金额', 'amount', 'total']
        };
    }

    async loadUsageStats() {
        try {
            const stats = await chrome.storage.local.get(['usageStats']);
            const usageStats = stats.usageStats || {};
            
            // 计算统计数据
            let totalParses = 0;
            let totalFills = 0;
            let monthlyUsage = 0;
            
            const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
            
            Object.entries(usageStats).forEach(([date, dayStats]) => {
                Object.entries(dayStats).forEach(([action, count]) => {
                    if (action === 'parseDocument') {
                        totalParses += count;
                        if (date.startsWith(currentMonth)) {
                            monthlyUsage += count;
                        }
                    } else if (action === 'fillForm') {
                        totalFills += count;
                    }
                });
            });
            
            // 更新显示
            document.getElementById('totalParses').textContent = totalParses;
            document.getElementById('totalFills').textContent = totalFills;
            document.getElementById('monthlyUsage').textContent = monthlyUsage;
            
        } catch (error) {
            console.error('Failed to load usage stats:', error);
        }
    }

    markAsChanged() {
        this.hasUnsavedChanges = true;
        
        // 更新保存按钮状态
        const saveButton = document.getElementById('saveSettings');
        saveButton.style.background = '#28a745';
        saveButton.textContent = '💾 保存更改';
    }

    showStatus(message, type) {
        const statusElement = document.getElementById('saveStatus');
        statusElement.textContent = message;
        statusElement.className = `save-status ${type}`;
        
        // 3秒后清除状态
        setTimeout(() => {
            statusElement.textContent = '';
            statusElement.className = 'save-status';
        }, 3000);
    }

    showConnectionStatus(message, type) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.textContent = message;
        statusElement.className = `connection-status ${type}`;
        
        if (type !== 'testing') {
            // 5秒后清除状态
            setTimeout(() => {
                statusElement.textContent = '';
                statusElement.className = 'connection-status';
            }, 5000);
        }
    }

    openHelp() {
        chrome.tabs.create({
            url: 'https://github.com/your-repo/freight-parser-extension/wiki'
        });
    }

    openFeedback() {
        chrome.tabs.create({
            url: 'https://github.com/your-repo/freight-parser-extension/issues'
        });
    }

    openGitHub() {
        chrome.tabs.create({
            url: 'https://github.com/your-repo/freight-parser-extension'
        });
    }
}

// 初始化设置页面
document.addEventListener('DOMContentLoaded', () => {
    new FreightParserOptions();
});
