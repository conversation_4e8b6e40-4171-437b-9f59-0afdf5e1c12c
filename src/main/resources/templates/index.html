<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货代单证智能解析插件 - 测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .result-card {
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
        .loading {
            display: none;
        }
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        .json-viewer {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-ship"></i>
                            货代单证智能解析插件
                        </h3>
                        <small>支持PDF、JPG、PNG格式文件，自动识别提单、发票、装箱单等单证</small>
                    </div>
                    <div class="card-body">
                        <!-- 文件上传区域 -->
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>拖拽文件到此处或点击上传</h5>
                            <p class="text-muted">支持 PDF、JPG、PNG 格式，最大 20MB</p>
                            <input type="file" id="fileInput" accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                        </div>

                        <!-- 配置选项 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <label for="documentType" class="form-label">单证类型</label>
                                <select class="form-select" id="documentType">
                                    <option value="AUTO_DETECT">自动识别</option>
                                    <option value="BILL_OF_LADING">海运提单</option>
                                    <option value="COMMERCIAL_INVOICE">商业发票</option>
                                    <option value="PACKING_LIST">装箱单</option>
                                    <option value="CUSTOMS_DECLARATION">报关单</option>
                                    <option value="FREIGHT_INVOICE">货代费用单</option>
                                    <option value="CERTIFICATE_OF_ORIGIN">原产地证</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="ocrLanguage" class="form-label">OCR语言</label>
                                <select class="form-select" id="ocrLanguage">
                                    <option value="chi_sim+eng">中英文混合</option>
                                    <option value="eng">英文</option>
                                    <option value="chi_sim">简体中文</option>
                                    <option value="chi_tra">繁体中文</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="customRequirement" class="form-label">自定义解析需求（可选）</label>
                            <textarea class="form-control" id="customRequirement" rows="2" 
                                placeholder="例如：重点提取货物信息和金额数据"></textarea>
                        </div>

                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="includeRawContent">
                            <label class="form-check-label" for="includeRawContent">
                                包含原始提取内容
                            </label>
                        </div>

                        <!-- 解析按钮 -->
                        <div class="d-grid gap-2 mt-4">
                            <button class="btn btn-primary btn-lg" id="parseBtn" disabled>
                                <i class="fas fa-magic"></i>
                                开始智能解析
                            </button>
                        </div>

                        <!-- 进度条 -->
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-muted" id="progressText">准备中...</small>
                            </div>
                        </div>

                        <!-- 结果显示区域 -->
                        <div id="resultArea"></div>
                    </div>
                </div>

                <!-- 示例表单（用于测试回填功能） -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i>
                            示例表单（测试回填功能）
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="blNo" class="form-label">提单号</label>
                                        <input type="text" class="form-control" id="blNo" name="BL_NO" placeholder="请输入提单号">
                                    </div>
                                    <div class="mb-3">
                                        <label for="shipper" class="form-label">发货人</label>
                                        <input type="text" class="form-control" id="shipper" name="SHIPPER" placeholder="请输入发货人">
                                    </div>
                                    <div class="mb-3">
                                        <label for="consignee" class="form-label">收货人</label>
                                        <input type="text" class="form-control" id="consignee" name="CONSIGNEE" placeholder="请输入收货人">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="pol" class="form-label">起运港</label>
                                        <input type="text" class="form-control" id="pol" name="PORT_OF_LOADING" placeholder="请输入起运港">
                                    </div>
                                    <div class="mb-3">
                                        <label for="pod" class="form-label">目的港</label>
                                        <input type="text" class="form-control" id="pod" name="PORT_OF_DISCHARGE" placeholder="请输入目的港">
                                    </div>
                                    <div class="mb-3">
                                        <label for="vessel" class="form-label">船名航次</label>
                                        <input type="text" class="form-control" id="vessel" name="VESSEL_VOYAGE" placeholder="请输入船名航次">
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" id="fillFormBtn" disabled>
                                    <i class="fas fa-fill-drip"></i>
                                    智能回填表单
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentParseResult = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeUpload();
            initializeFormFill();
        });

        // 初始化文件上传功能
        function initializeUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const parseBtn = document.getElementById('parseBtn');

            // 点击上传区域
            uploadArea.addEventListener('click', () => fileInput.click());

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽功能
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 解析按钮
            parseBtn.addEventListener('click', parseDocument);
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                updateUploadArea(file);
            }
        }

        // 处理拖拽
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                updateUploadArea(files[0]);
            }
        }

        // 更新上传区域显示
        function updateUploadArea(file) {
            const uploadArea = document.getElementById('uploadArea');
            const parseBtn = document.getElementById('parseBtn');
            
            uploadArea.innerHTML = `
                <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                <h6>${file.name}</h6>
                <small class="text-muted">${formatFileSize(file.size)} - ${file.type}</small>
            `;
            
            parseBtn.disabled = false;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        // 解析文档
        async function parseDocument() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请先选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('documentType', document.getElementById('documentType').value);
            formData.append('clientId', 'test_client_001');
            formData.append('userId', 'test_user');
            formData.append('ocrLanguage', document.getElementById('ocrLanguage').value);
            formData.append('includeRawContent', document.getElementById('includeRawContent').checked);
            
            const customRequirement = document.getElementById('customRequirement').value;
            if (customRequirement) {
                formData.append('customRequirement', customRequirement);
            }

            showProgress();

            try {
                const response = await fetch('/api/document/parse', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                hideProgress();

                if (result.success) {
                    currentParseResult = result.data;
                    showResult(result.data);
                    document.getElementById('fillFormBtn').disabled = false;
                } else {
                    showError(result.message);
                }
            } catch (error) {
                hideProgress();
                showError('解析失败: ' + error.message);
            }
        }

        // 显示进度
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('parseBtn').disabled = true;
            
            let progress = 0;
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                
                progressBar.style.width = progress + '%';
                
                if (progress < 30) {
                    progressText.textContent = '正在上传文件...';
                } else if (progress < 60) {
                    progressText.textContent = '正在提取文档内容...';
                } else {
                    progressText.textContent = '正在AI智能解析...';
                }
            }, 200);

            // 保存interval以便后续清除
            window.progressInterval = interval;
        }

        // 隐藏进度
        function hideProgress() {
            if (window.progressInterval) {
                clearInterval(window.progressInterval);
            }
            
            const progressBar = document.querySelector('.progress-bar');
            progressBar.style.width = '100%';
            
            setTimeout(() => {
                document.getElementById('progressContainer').style.display = 'none';
                document.getElementById('parseBtn').disabled = false;
                progressBar.style.width = '0%';
            }, 500);
        }

        // 显示解析结果
        function showResult(data) {
            const resultArea = document.getElementById('resultArea');
            
            resultArea.innerHTML = `
                <div class="result-card card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle"></i>
                            解析成功
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <strong>文件名:</strong><br>
                                <small class="text-muted">${data.fileName}</small>
                            </div>
                            <div class="col-md-3">
                                <strong>文档类型:</strong><br>
                                <small class="text-muted">${data.documentType}</small>
                            </div>
                            <div class="col-md-3">
                                <strong>置信度:</strong><br>
                                <small class="text-muted">${data.confidenceScore || 'N/A'}%</small>
                            </div>
                            <div class="col-md-3">
                                <strong>处理时间:</strong><br>
                                <small class="text-muted">${data.processingTime || 'N/A'}ms</small>
                            </div>
                        </div>
                        
                        <h6>解析结果:</h6>
                        <div class="json-viewer">${JSON.stringify(data.parsedData, null, 2)}</div>
                        
                        ${data.rawContent ? `
                            <h6 class="mt-3">原始内容:</h6>
                            <div class="json-viewer" style="max-height: 200px;">${data.rawContent}</div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 显示错误
        function showError(message) {
            const resultArea = document.getElementById('resultArea');
            
            resultArea.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>解析失败:</strong> ${message}
                </div>
            `;
        }

        // 初始化表单回填功能
        function initializeFormFill() {
            document.getElementById('fillFormBtn').addEventListener('click', fillForm);
        }

        // 智能回填表单
        function fillForm() {
            if (!currentParseResult || !currentParseResult.parsedData) {
                alert('没有可用的解析结果');
                return;
            }

            const parsedData = currentParseResult.parsedData;
            const form = document.getElementById('testForm');
            
            // 简单的字段映射和回填
            Object.keys(parsedData).forEach(key => {
                const value = parsedData[key];
                if (value) {
                    // 查找对应的表单字段
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        field.value = value;
                        // 添加回填动画效果
                        field.style.backgroundColor = '#d4edda';
                        field.style.borderColor = '#28a745';
                        
                        setTimeout(() => {
                            field.style.backgroundColor = '';
                            field.style.borderColor = '';
                        }, 2000);
                    }
                }
            });

            // 显示回填完成提示
            const fillBtn = document.getElementById('fillFormBtn');
            const originalText = fillBtn.innerHTML;
            fillBtn.innerHTML = '<i class="fas fa-check"></i> 回填完成';
            fillBtn.classList.remove('btn-success');
            fillBtn.classList.add('btn-info');
            
            setTimeout(() => {
                fillBtn.innerHTML = originalText;
                fillBtn.classList.remove('btn-info');
                fillBtn.classList.add('btn-success');
            }, 2000);
        }
    </script>
</body>
</html>
