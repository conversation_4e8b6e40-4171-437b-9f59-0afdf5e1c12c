<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货代单证智能解析插件 SDK 演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .demo-form {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .form-section {
            margin-bottom: 30px;
        }
        .form-section h5 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
            margin-bottom: 20px;
        }
        .filled-field {
            background-color: #e8f5e8 !important;
            border-color: #28a745 !important;
        }
        .sdk-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .event-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .event-item {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .event-success { background-color: #d4edda; }
        .event-info { background-color: #d1ecf1; }
        .event-warning { background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- SDK信息 -->
        <div class="sdk-info">
            <h2>🚢 货代单证智能解析插件 SDK 演示</h2>
            <p class="mb-0">这是一个完整的SDK集成演示页面，展示了如何在现有系统中集成货代单证智能解析功能。</p>
        </div>

        <!-- 使用说明 -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>📖 使用说明</h4>
            </div>
            <div class="card-body">
                <ol>
                    <li>点击右下角的 <strong>"智能解析"</strong> 浮动按钮打开插件面板</li>
                    <li>上传货代单证文件（PDF、JPG、PNG格式）</li>
                    <li>选择单证类型或使用自动识别</li>
                    <li>点击 <strong>"开始解析"</strong> 进行AI智能解析</li>
                    <li>解析完成后，点击 <strong>"智能回填"</strong> 自动填写下方表单</li>
                </ol>
                <div class="alert alert-info mt-3">
                    <strong>提示：</strong> 插件会自动识别页面中的表单字段，并智能匹配解析出的数据进行回填。
                </div>
            </div>
        </div>

        <!-- 演示表单 -->
        <div class="demo-form">
            <h3 class="text-center mb-4">📋 货代业务表单演示</h3>
            
            <form id="freightForm">
                <!-- 提单信息 -->
                <div class="form-section">
                    <h5>🚢 提单信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="blNo" class="form-label">提单号 *</label>
                                <input type="text" class="form-control" id="blNo" name="BL_NO" placeholder="请输入提单号">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vesselVoyage" class="form-label">船名航次</label>
                                <input type="text" class="form-control" id="vesselVoyage" name="VESSEL_VOYAGE" placeholder="请输入船名航次">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pol" class="form-label">起运港</label>
                                <input type="text" class="form-control" id="pol" name="PORT_OF_LOADING" placeholder="请输入起运港">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pod" class="form-label">目的港</label>
                                <input type="text" class="form-control" id="pod" name="PORT_OF_DISCHARGE" placeholder="请输入目的港">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 货主信息 -->
                <div class="form-section">
                    <h5>👥 货主信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shipper" class="form-label">发货人 *</label>
                                <textarea class="form-control" id="shipper" name="SHIPPER" rows="3" placeholder="请输入发货人信息"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="consignee" class="form-label">收货人 *</label>
                                <textarea class="form-control" id="consignee" name="CONSIGNEE" rows="3" placeholder="请输入收货人信息"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notifyParty" class="form-label">通知方</label>
                        <textarea class="form-control" id="notifyParty" name="NOTIFY_PARTY" rows="2" placeholder="请输入通知方信息"></textarea>
                    </div>
                </div>

                <!-- 货物信息 -->
                <div class="form-section">
                    <h5>📦 货物信息</h5>
                    <div class="mb-3">
                        <label for="goodsDescription" class="form-label">货物描述</label>
                        <textarea class="form-control" id="goodsDescription" name="GOODS_DESCRIPTION" rows="3" placeholder="请输入货物描述"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="packages" class="form-label">件数</label>
                                <input type="number" class="form-control" id="packages" name="PACKAGES" placeholder="件数">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="grossWeight" class="form-label">毛重 (KG)</label>
                                <input type="number" class="form-control" id="grossWeight" name="GROSS_WEIGHT" placeholder="毛重">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="measurement" class="form-label">体积 (CBM)</label>
                                <input type="number" step="0.01" class="form-control" id="measurement" name="MEASUREMENT" placeholder="体积">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 集装箱信息 -->
                <div class="form-section">
                    <h5>📦 集装箱信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="containerNo" class="form-label">集装箱号</label>
                                <input type="text" class="form-control" id="containerNo" name="CONTAINER_NO" placeholder="请输入集装箱号">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sealNo" class="form-label">封条号</label>
                                <input type="text" class="form-control" id="sealNo" name="SEAL_NO" placeholder="请输入封条号">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发票信息 -->
                <div class="form-section">
                    <h5>💰 发票信息</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="invoiceNo" class="form-label">发票号</label>
                                <input type="text" class="form-control" id="invoiceNo" name="INVOICE_NO" placeholder="请输入发票号">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="totalAmount" class="form-label">总金额</label>
                                <input type="number" step="0.01" class="form-control" id="totalAmount" name="TOTAL_AMOUNT" placeholder="总金额">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="currency" class="form-label">币种</label>
                                <select class="form-control" id="currency" name="CURRENCY">
                                    <option value="">请选择币种</option>
                                    <option value="USD">美元 (USD)</option>
                                    <option value="EUR">欧元 (EUR)</option>
                                    <option value="CNY">人民币 (CNY)</option>
                                    <option value="JPY">日元 (JPY)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 表单操作 -->
                <div class="text-center">
                    <button type="button" class="btn btn-secondary me-2" onclick="clearForm()">清空表单</button>
                    <button type="button" class="btn btn-primary me-2" onclick="validateForm()">验证表单</button>
                    <button type="submit" class="btn btn-success">提交表单</button>
                </div>
            </form>
        </div>

        <!-- 事件日志 -->
        <div class="card">
            <div class="card-header">
                <h5>📊 插件事件日志</h5>
                <small class="text-muted">实时显示插件的各种事件和操作</small>
            </div>
            <div class="card-body">
                <div id="eventLog" class="event-log">
                    <div class="event-item event-info">
                        <strong>[INFO]</strong> 页面加载完成，等待插件初始化...
                    </div>
                </div>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-secondary" onclick="clearEventLog()">清空日志</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入SDK -->
    <script src="/static/js/freight-parser-sdk.js"></script>
    
    <script>
        // SDK配置
        window.freightParserConfig = {
            apiBaseUrl: '/api',
            clientId: 'sdk_demo_client',
            userId: 'demo_user',
            theme: 'light',
            position: 'bottom-right'
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addEventLog('页面DOM加载完成', 'info');
            
            // 监听插件事件
            document.addEventListener('freightParser:initialized', function(e) {
                addEventLog('插件初始化完成，版本: ' + e.detail.version, 'success');
            });

            document.addEventListener('freightParser:pageScanComplete', function(e) {
                addEventLog('页面扫描完成，发现 ' + e.detail.formCount + ' 个表单', 'info');
            });

            document.addEventListener('freightParser:parseComplete', function(e) {
                addEventLog('文档解析完成，提取到 ' + Object.keys(e.detail.parsedData).length + ' 个字段', 'success');
                console.log('解析结果:', e.detail);
            });

            document.addEventListener('freightParser:fillComplete', function(e) {
                addEventLog('表单回填完成，成功填写 ' + e.detail.filledFields.length + ' 个字段', 'success');
                
                // 高亮填写的字段
                e.detail.filledFields.forEach(field => {
                    const element = document.querySelector(`[name="${field.fieldName}"]`);
                    if (element) {
                        element.classList.add('filled-field');
                        setTimeout(() => {
                            element.classList.remove('filled-field');
                        }, 3000);
                    }
                });
            });
        });

        // 添加事件日志
        function addEventLog(message, type = 'info') {
            const log = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const eventItem = document.createElement('div');
            eventItem.className = `event-item event-${type}`;
            eventItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            log.appendChild(eventItem);
            log.scrollTop = log.scrollHeight;
        }

        // 清空事件日志
        function clearEventLog() {
            document.getElementById('eventLog').innerHTML = '';
            addEventLog('事件日志已清空', 'info');
        }

        // 清空表单
        function clearForm() {
            document.getElementById('freightForm').reset();
            addEventLog('表单已清空', 'warning');
        }

        // 验证表单
        function validateForm() {
            const form = document.getElementById('freightForm');
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            let emptyFields = [];

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    emptyFields.push(field.previousElementSibling.textContent);
                }
            });

            if (isValid) {
                addEventLog('表单验证通过', 'success');
                alert('表单验证通过！');
            } else {
                addEventLog('表单验证失败，缺少必填字段: ' + emptyFields.join(', '), 'warning');
                alert('请填写必填字段: ' + emptyFields.join(', '));
            }
        }

        // 表单提交
        document.getElementById('freightForm').addEventListener('submit', function(e) {
            e.preventDefault();
            addEventLog('表单提交被拦截（演示模式）', 'info');
            alert('演示模式：表单提交已被拦截');
        });
    </script>
</body>
</html>
