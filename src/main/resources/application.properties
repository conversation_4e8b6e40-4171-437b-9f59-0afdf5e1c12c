# ??????
spring.application.name=freight-document-parser
server.port=8080
server.servlet.context-path=/api

# ?????
spring.datasource.url=***************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA??
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# ??????
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:db/schema.sql
spring.sql.init.data-locations=classpath:db/data.sql
spring.sql.init.continue-on-error=false

# ??????
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB

# ??????
app.file.upload-dir=./uploads
app.file.temp-dir=./temp

# ????
app.cors.allowed-origins=*
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*

# LLM????
app.llm.timeout=30000
app.llm.retry-count=3
app.llm.default-service=QWEN

# ????
app.plugin.enable-cors=true
app.plugin.max-requests-per-day=1000
app.plugin.cache-ttl=3600

# ????
logging.level.com.cvi.compute_view_input=DEBUG
logging.level.org.springframework.web=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
