/**
 * 货代单证智能解析插件 SDK
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */

(function(global) {
    'use strict';

    // SDK版本
    const SDK_VERSION = '1.0.0';
    
    // 默认配置
    const DEFAULT_CONFIG = {
        apiBaseUrl: '/api',
        theme: 'light',
        language: 'zh-CN',
        autoDetect: true,
        enableOcr: true,
        ocrLanguage: 'chi_sim+eng',
        maxFileSize: 20 * 1024 * 1024, // 20MB
        supportedTypes: ['pdf', 'jpg', 'jpeg', 'png'],
        position: 'bottom-right',
        zIndex: 10000
    };

    /**
     * 货代解析器主类
     */
    class FreightParser {
        constructor(config = {}) {
            this.config = { ...DEFAULT_CONFIG, ...config };
            this.isInitialized = false;
            this.currentParseResult = null;
            this.pluginContainer = null;
            
            // 绑定方法上下文
            this.init = this.init.bind(this);
            this.parseDocument = this.parseDocument.bind(this);
            this.fillForm = this.fillForm.bind(this);
        }

        /**
         * 初始化插件
         */
        async init() {
            if (this.isInitialized) {
                console.warn('FreightParser already initialized');
                return;
            }

            try {
                // 检查依赖
                this.checkDependencies();
                
                // 创建插件UI
                this.createPluginUI();
                
                // 绑定事件
                this.bindEvents();
                
                // 扫描页面表单
                this.scanPageForms();
                
                this.isInitialized = true;
                console.log('FreightParser SDK initialized successfully', {
                    version: SDK_VERSION,
                    config: this.config
                });
                
                // 触发初始化完成事件
                this.dispatchEvent('initialized', { version: SDK_VERSION });
                
            } catch (error) {
                console.error('FreightParser initialization failed:', error);
                throw error;
            }
        }

        /**
         * 检查依赖
         */
        checkDependencies() {
            // 检查浏览器支持
            if (!window.File || !window.FileReader || !window.FormData) {
                throw new Error('Browser does not support required APIs');
            }
            
            // 检查API可用性
            if (!this.config.apiBaseUrl) {
                throw new Error('API base URL is required');
            }
        }

        /**
         * 创建插件UI
         */
        createPluginUI() {
            // 创建主容器
            this.pluginContainer = document.createElement('div');
            this.pluginContainer.id = 'freight-parser-plugin';
            this.pluginContainer.className = `freight-parser-plugin ${this.config.theme}`;
            
            // 设置样式
            this.pluginContainer.innerHTML = `
                <div class="fp-trigger-btn" id="fp-trigger-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span>智能解析</span>
                </div>
                
                <div class="fp-panel" id="fp-panel" style="display: none;">
                    <div class="fp-header">
                        <h3>🚢 货代单证智能解析</h3>
                        <button class="fp-close-btn" id="fp-close-btn">×</button>
                    </div>
                    
                    <div class="fp-content">
                        <div class="fp-upload-area" id="fp-upload-area">
                            <div class="fp-upload-icon">📁</div>
                            <div class="fp-upload-text">
                                <div>拖拽文件到此处或点击上传</div>
                                <small>支持 PDF、JPG、PNG 格式，最大 20MB</small>
                            </div>
                            <input type="file" id="fp-file-input" accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                        </div>
                        
                        <div class="fp-options">
                            <div class="fp-option-group">
                                <label>单证类型：</label>
                                <select id="fp-document-type">
                                    <option value="AUTO_DETECT">自动识别</option>
                                    <option value="BILL_OF_LADING">海运提单</option>
                                    <option value="COMMERCIAL_INVOICE">商业发票</option>
                                    <option value="PACKING_LIST">装箱单</option>
                                    <option value="CUSTOMS_DECLARATION">报关单</option>
                                    <option value="FREIGHT_INVOICE">货代费用单</option>
                                    <option value="CERTIFICATE_OF_ORIGIN">原产地证</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="fp-actions">
                            <button id="fp-parse-btn" class="fp-btn fp-btn-primary" disabled>
                                🚀 开始解析
                            </button>
                            <button id="fp-fill-btn" class="fp-btn fp-btn-success" disabled>
                                📝 智能回填
                            </button>
                        </div>
                        
                        <div class="fp-progress" id="fp-progress" style="display: none;">
                            <div class="fp-progress-bar">
                                <div class="fp-progress-fill"></div>
                            </div>
                            <div class="fp-progress-text">正在处理...</div>
                        </div>
                        
                        <div class="fp-result" id="fp-result" style="display: none;">
                            <div class="fp-result-header">解析结果</div>
                            <div class="fp-result-content"></div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加样式
            this.injectStyles();
            
            // 添加到页面
            document.body.appendChild(this.pluginContainer);
            
            // 设置位置
            this.setPosition();
        }

        /**
         * 注入样式
         */
        injectStyles() {
            if (document.getElementById('freight-parser-styles')) {
                return;
            }
            
            const styles = document.createElement('style');
            styles.id = 'freight-parser-styles';
            styles.textContent = `
                .freight-parser-plugin {
                    position: fixed;
                    z-index: ${this.config.zIndex};
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                    line-height: 1.4;
                }
                
                .fp-trigger-btn {
                    background: #007bff;
                    color: white;
                    padding: 12px 16px;
                    border-radius: 8px;
                    cursor: pointer;
                    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: all 0.3s ease;
                    user-select: none;
                }
                
                .fp-trigger-btn:hover {
                    background: #0056b3;
                    transform: translateY(-2px);
                    box-shadow: 0 6px 16px rgba(0,123,255,0.4);
                }
                
                .fp-panel {
                    background: white;
                    border: 1px solid #e0e0e0;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                    width: 360px;
                    max-height: 600px;
                    overflow: hidden;
                    margin-top: 12px;
                }
                
                .fp-header {
                    background: #f8f9fa;
                    padding: 16px 20px;
                    border-bottom: 1px solid #e0e0e0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .fp-header h3 {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                }
                
                .fp-close-btn {
                    background: none;
                    border: none;
                    font-size: 20px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .fp-content {
                    padding: 20px;
                }
                
                .fp-upload-area {
                    border: 2px dashed #007bff;
                    border-radius: 8px;
                    padding: 24px;
                    text-align: center;
                    background: #f8f9ff;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    margin-bottom: 16px;
                }
                
                .fp-upload-area:hover {
                    border-color: #0056b3;
                    background: #e3f2fd;
                }
                
                .fp-upload-area.dragover {
                    border-color: #28a745;
                    background: #d4edda;
                }
                
                .fp-upload-icon {
                    font-size: 32px;
                    margin-bottom: 8px;
                }
                
                .fp-options {
                    margin-bottom: 16px;
                }
                
                .fp-option-group {
                    margin-bottom: 12px;
                }
                
                .fp-option-group label {
                    display: block;
                    margin-bottom: 4px;
                    font-weight: 500;
                }
                
                .fp-option-group select {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 14px;
                }
                
                .fp-actions {
                    display: flex;
                    gap: 8px;
                    margin-bottom: 16px;
                }
                
                .fp-btn {
                    flex: 1;
                    padding: 10px 16px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                
                .fp-btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .fp-btn-primary {
                    background: #007bff;
                    color: white;
                }
                
                .fp-btn-primary:hover:not(:disabled) {
                    background: #0056b3;
                }
                
                .fp-btn-success {
                    background: #28a745;
                    color: white;
                }
                
                .fp-btn-success:hover:not(:disabled) {
                    background: #1e7e34;
                }
                
                .fp-progress {
                    margin-bottom: 16px;
                }
                
                .fp-progress-bar {
                    background: #e9ecef;
                    border-radius: 4px;
                    height: 8px;
                    overflow: hidden;
                    margin-bottom: 8px;
                }
                
                .fp-progress-fill {
                    background: #007bff;
                    height: 100%;
                    width: 0%;
                    transition: width 0.3s ease;
                }
                
                .fp-progress-text {
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                }
                
                .fp-result {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    overflow: hidden;
                }
                
                .fp-result-header {
                    background: #f8f9fa;
                    padding: 8px 12px;
                    font-weight: 500;
                    border-bottom: 1px solid #e0e0e0;
                }
                
                .fp-result-content {
                    padding: 12px;
                    max-height: 200px;
                    overflow-y: auto;
                    font-family: monospace;
                    font-size: 12px;
                    background: #f8f9fa;
                }
                
                /* 位置样式 */
                .freight-parser-plugin.position-bottom-right {
                    bottom: 20px;
                    right: 20px;
                }
                
                .freight-parser-plugin.position-bottom-left {
                    bottom: 20px;
                    left: 20px;
                }
                
                .freight-parser-plugin.position-top-right {
                    top: 20px;
                    right: 20px;
                }
                
                .freight-parser-plugin.position-top-left {
                    top: 20px;
                    left: 20px;
                }
            `;
            
            document.head.appendChild(styles);
        }

        /**
         * 设置插件位置
         */
        setPosition() {
            this.pluginContainer.classList.add(`position-${this.config.position}`);
        }

        /**
         * 绑定事件
         */
        bindEvents() {
            const triggerBtn = document.getElementById('fp-trigger-btn');
            const panel = document.getElementById('fp-panel');
            const closeBtn = document.getElementById('fp-close-btn');
            const uploadArea = document.getElementById('fp-upload-area');
            const fileInput = document.getElementById('fp-file-input');
            const parseBtn = document.getElementById('fp-parse-btn');
            const fillBtn = document.getElementById('fp-fill-btn');

            // 触发按钮点击
            triggerBtn.addEventListener('click', () => {
                const isVisible = panel.style.display !== 'none';
                panel.style.display = isVisible ? 'none' : 'block';
            });

            // 关闭按钮
            closeBtn.addEventListener('click', () => {
                panel.style.display = 'none';
            });

            // 上传区域点击
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleFileSelect(file);
                }
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const file = e.dataTransfer.files[0];
                if (file) {
                    this.handleFileSelect(file);
                }
            });

            // 解析按钮
            parseBtn.addEventListener('click', () => {
                this.parseDocument();
            });

            // 回填按钮
            fillBtn.addEventListener('click', () => {
                this.fillForm();
            });
        }

        /**
         * 处理文件选择
         */
        handleFileSelect(file) {
            // 验证文件
            if (!this.validateFile(file)) {
                return;
            }

            // 更新UI
            const uploadArea = document.getElementById('fp-upload-area');
            uploadArea.innerHTML = `
                <div class="fp-upload-icon">📄</div>
                <div class="fp-upload-text">
                    <div>${file.name}</div>
                    <small>${this.formatFileSize(file.size)} - ${file.type}</small>
                </div>
            `;

            // 启用解析按钮
            document.getElementById('fp-parse-btn').disabled = false;

            // 保存文件
            this.selectedFile = file;
        }

        /**
         * 验证文件
         */
        validateFile(file) {
            // 检查文件大小
            if (file.size > this.config.maxFileSize) {
                this.showError(`文件大小不能超过 ${this.formatFileSize(this.config.maxFileSize)}`);
                return false;
            }

            // 检查文件类型
            const extension = file.name.split('.').pop().toLowerCase();
            if (!this.config.supportedTypes.includes(extension)) {
                this.showError(`不支持的文件类型: ${extension}`);
                return false;
            }

            return true;
        }

        /**
         * 格式化文件大小
         */
        formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        /**
         * 解析文档
         */
        async parseDocument() {
            if (!this.selectedFile) {
                this.showError('请先选择文件');
                return;
            }

            try {
                this.showProgress('正在上传文件...');

                const formData = new FormData();
                formData.append('file', this.selectedFile);
                formData.append('documentType', document.getElementById('fp-document-type').value);
                formData.append('clientId', this.config.clientId || 'sdk_client');
                formData.append('userId', this.config.userId || 'sdk_user');
                formData.append('enableOcr', this.config.enableOcr);
                formData.append('ocrLanguage', this.config.ocrLanguage);

                this.updateProgress(30, '正在解析文档...');

                const response = await fetch(`${this.config.apiBaseUrl}/document/parse`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                this.updateProgress(100, '解析完成');

                if (result.success) {
                    this.currentParseResult = result.data;
                    this.showResult(result.data);
                    document.getElementById('fp-fill-btn').disabled = false;
                    
                    // 触发解析完成事件
                    this.dispatchEvent('parseComplete', result.data);
                } else {
                    this.showError(result.message);
                }

            } catch (error) {
                console.error('Parse error:', error);
                this.showError('解析失败: ' + error.message);
            } finally {
                this.hideProgress();
            }
        }

        /**
         * 智能回填表单
         */
        fillForm() {
            if (!this.currentParseResult || !this.currentParseResult.parsedData) {
                this.showError('没有可用的解析结果');
                return;
            }

            try {
                const parsedData = this.currentParseResult.parsedData;
                const filledFields = [];

                // 扫描页面表单字段
                const formFields = this.scanFormFields();

                // 智能匹配和回填
                Object.keys(parsedData).forEach(dataKey => {
                    const dataValue = parsedData[dataKey];
                    if (!dataValue) return;

                    // 查找匹配的表单字段
                    const matchedField = this.findMatchingField(dataKey, formFields);
                    if (matchedField) {
                        this.fillField(matchedField, dataValue);
                        filledFields.push({
                            dataKey,
                            dataValue,
                            fieldName: matchedField.name || matchedField.id
                        });
                    }
                });

                // 显示回填结果
                this.showFillResult(filledFields);

                // 触发回填完成事件
                this.dispatchEvent('fillComplete', {
                    filledFields,
                    totalFields: Object.keys(parsedData).length
                });

            } catch (error) {
                console.error('Fill error:', error);
                this.showError('回填失败: ' + error.message);
            }
        }

        /**
         * 扫描表单字段
         */
        scanFormFields() {
            const fields = [];
            const selectors = [
                'input[type="text"]',
                'input[type="email"]',
                'input[type="tel"]',
                'input[type="number"]',
                'input[type="date"]',
                'textarea',
                'select'
            ];

            selectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(element => {
                    if (this.isValidFormField(element)) {
                        fields.push({
                            element,
                            id: element.id,
                            name: element.name,
                            type: element.type,
                            placeholder: element.placeholder,
                            label: this.findFieldLabel(element)
                        });
                    }
                });
            });

            return fields;
        }

        /**
         * 检查是否为有效的表单字段
         */
        isValidFormField(element) {
            // 排除隐藏字段和插件自身的字段
            return !element.hidden && 
                   !element.disabled && 
                   !element.readOnly &&
                   !element.closest('#freight-parser-plugin');
        }

        /**
         * 查找字段标签
         */
        findFieldLabel(element) {
            // 查找关联的label
            if (element.id) {
                const label = document.querySelector(`label[for="${element.id}"]`);
                if (label) return label.textContent.trim();
            }

            // 查找父级label
            const parentLabel = element.closest('label');
            if (parentLabel) return parentLabel.textContent.trim();

            // 查找前面的标签文本
            const prevElement = element.previousElementSibling;
            if (prevElement && prevElement.tagName === 'LABEL') {
                return prevElement.textContent.trim();
            }

            return '';
        }

        /**
         * 查找匹配的字段
         */
        findMatchingField(dataKey, formFields) {
            // 货代专用字段映射
            const fieldMapping = {
                'BL_NO': ['提单号', 'bill', 'bl', 'blno'],
                'SHIPPER': ['发货人', 'shipper', 'consignor', 'sender'],
                'CONSIGNEE': ['收货人', 'consignee', 'receiver'],
                'PORT_OF_LOADING': ['起运港', 'pol', 'loading', 'origin'],
                'PORT_OF_DISCHARGE': ['目的港', 'pod', 'discharge', 'destination'],
                'VESSEL_VOYAGE': ['船名', 'vessel', 'voyage'],
                'CONTAINER_NO': ['集装箱', 'container', 'cntr'],
                'INVOICE_NO': ['发票号', 'invoice'],
                'TOTAL_AMOUNT': ['金额', 'amount', 'total']
            };

            const aliases = fieldMapping[dataKey] || [dataKey.toLowerCase()];

            // 查找最佳匹配
            let bestMatch = null;
            let bestScore = 0;

            formFields.forEach(field => {
                let score = 0;

                // 精确匹配字段名
                if (field.name === dataKey || field.id === dataKey) {
                    score += 100;
                }

                // 别名匹配
                const fieldText = (field.name + ' ' + field.id + ' ' + field.label + ' ' + field.placeholder).toLowerCase();
                aliases.forEach(alias => {
                    if (fieldText.includes(alias)) {
                        score += 50;
                    }
                });

                if (score > bestScore) {
                    bestScore = score;
                    bestMatch = field;
                }
            });

            return bestScore > 30 ? bestMatch : null;
        }

        /**
         * 填充字段
         */
        fillField(field, value) {
            const element = field.element;
            
            // 设置值
            element.value = value;
            
            // 触发事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            
            // 添加视觉效果
            element.style.transition = 'all 0.3s ease';
            element.style.backgroundColor = '#e8f5e8';
            element.style.borderColor = '#28a745';
            
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.borderColor = '';
            }, 2000);
        }

        /**
         * 显示进度
         */
        showProgress(text) {
            const progress = document.getElementById('fp-progress');
            const progressText = progress.querySelector('.fp-progress-text');
            
            progress.style.display = 'block';
            progressText.textContent = text;
            
            this.updateProgress(0);
        }

        /**
         * 更新进度
         */
        updateProgress(percent, text) {
            const progressFill = document.querySelector('.fp-progress-fill');
            const progressText = document.querySelector('.fp-progress-text');
            
            progressFill.style.width = percent + '%';
            if (text) {
                progressText.textContent = text;
            }
        }

        /**
         * 隐藏进度
         */
        hideProgress() {
            setTimeout(() => {
                document.getElementById('fp-progress').style.display = 'none';
            }, 500);
        }

        /**
         * 显示结果
         */
        showResult(data) {
            const result = document.getElementById('fp-result');
            const content = result.querySelector('.fp-result-content');
            
            content.textContent = JSON.stringify(data.parsedData, null, 2);
            result.style.display = 'block';
        }

        /**
         * 显示回填结果
         */
        showFillResult(filledFields) {
            const message = `成功回填 ${filledFields.length} 个字段`;
            this.showSuccess(message);
        }

        /**
         * 显示错误
         */
        showError(message) {
            console.error('FreightParser Error:', message);
            alert('错误: ' + message);
        }

        /**
         * 显示成功
         */
        showSuccess(message) {
            console.log('FreightParser Success:', message);
            alert('成功: ' + message);
        }

        /**
         * 触发自定义事件
         */
        dispatchEvent(eventName, data) {
            const event = new CustomEvent(`freightParser:${eventName}`, {
                detail: data
            });
            document.dispatchEvent(event);
        }

        /**
         * 扫描页面表单（初始化时调用）
         */
        scanPageForms() {
            const forms = document.querySelectorAll('form');
            console.log(`FreightParser: Found ${forms.length} forms on page`);
            
            // 触发页面扫描完成事件
            this.dispatchEvent('pageScanComplete', {
                formCount: forms.length
            });
        }

        /**
         * 销毁插件
         */
        destroy() {
            if (this.pluginContainer) {
                this.pluginContainer.remove();
            }
            
            const styles = document.getElementById('freight-parser-styles');
            if (styles) {
                styles.remove();
            }
            
            this.isInitialized = false;
            console.log('FreightParser destroyed');
        }
    }

    // 全局暴露
    global.FreightParser = FreightParser;

    // 自动初始化（如果配置了自动初始化）
    if (global.freightParserAutoInit !== false) {
        document.addEventListener('DOMContentLoaded', () => {
            if (!global.freightParserInstance) {
                global.freightParserInstance = new FreightParser(global.freightParserConfig || {});
                global.freightParserInstance.init();
            }
        });
    }

})(window);
