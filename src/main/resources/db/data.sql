-- 货代单证智能解析插件初始数据
-- 创建时间: 2024-01-15

-- 插入字段映射配置 - 海运提单
INSERT INTO field_mapping_config (document_type, field_name, field_aliases, field_type, validation_rules, is_required, sort_order) VALUES
('BILL_OF_LADING', 'BL_NO', '["提单号", "bill_number", "bl_number", "blno", "B/L NO"]', 'TEXT', '{"pattern": "^[A-Z0-9]{8,20}$", "maxLength": 20}', TRUE, 1),
('BILL_OF_LADING', 'SHIPPER', '["发货人", "shipper", "consignor", "sender", "托运人"]', 'TEXT', '{"maxLength": 500}', TRUE, 2),
('BILL_OF_LADING', 'CONSIGNEE', '["收货人", "consignee", "receiver", "收货方"]', 'TEXT', '{"maxLength": 500}', TRUE, 3),
('BILL_OF_LADING', 'NOTIFY_PARTY', '["通知方", "notify_party", "notify", "通知人"]', 'TEXT', '{"maxLength": 500}', FALSE, 4),
('BILL_OF_LADING', 'PORT_OF_LOADING', '["起运港", "pol", "loading_port", "origin_port", "装货港"]', 'TEXT', '{"maxLength": 100}', TRUE, 5),
('BILL_OF_LADING', 'PORT_OF_DISCHARGE', '["目的港", "pod", "discharge_port", "dest_port", "卸货港"]', 'TEXT', '{"maxLength": 100}', TRUE, 6),
('BILL_OF_LADING', 'VESSEL_VOYAGE', '["船名航次", "vessel", "voyage", "船舶", "航次"]', 'TEXT', '{"maxLength": 200}', TRUE, 7),
('BILL_OF_LADING', 'ETD', '["开船日期", "etd", "departure_date", "sailing_date"]', 'DATE', '{"format": "YYYY-MM-DD"}', FALSE, 8),
('BILL_OF_LADING', 'ETA', '["到港日期", "eta", "arrival_date", "预计到港"]', 'DATE', '{"format": "YYYY-MM-DD"}', FALSE, 9),
('BILL_OF_LADING', 'CONTAINER_NO', '["集装箱号", "container", "cntr_no", "箱号"]', 'TEXT', '{"pattern": "^[A-Z]{4}[0-9]{7}$"}', FALSE, 10),
('BILL_OF_LADING', 'SEAL_NO', '["封条号", "seal", "seal_number", "铅封号"]', 'TEXT', '{"maxLength": 50}', FALSE, 11),
('BILL_OF_LADING', 'GOODS_DESCRIPTION', '["货物描述", "goods", "cargo", "commodity", "货名"]', 'TEXT', '{"maxLength": 1000}', TRUE, 12),
('BILL_OF_LADING', 'PACKAGES', '["件数", "packages", "pkgs", "package_count"]', 'NUMBER', '{"min": 1, "max": 999999}', FALSE, 13),
('BILL_OF_LADING', 'GROSS_WEIGHT', '["毛重", "gross_weight", "weight", "重量"]', 'NUMBER', '{"min": 0, "unit": "KG"}', FALSE, 14),
('BILL_OF_LADING', 'MEASUREMENT', '["体积", "measurement", "volume", "cbm", "立方"]', 'NUMBER', '{"min": 0, "unit": "CBM"}', FALSE, 15),
('BILL_OF_LADING', 'FREIGHT_TERMS', '["运费条款", "freight_terms", "payment_terms", "运费支付"]', 'TEXT', '{"enum": ["PREPAID", "COLLECT", "预付", "到付"]}', FALSE, 16);

-- 插入字段映射配置 - 商业发票
INSERT INTO field_mapping_config (document_type, field_name, field_aliases, field_type, validation_rules, is_required, sort_order) VALUES
('COMMERCIAL_INVOICE', 'INVOICE_NO', '["发票号", "invoice_number", "inv_no", "发票编号"]', 'TEXT', '{"maxLength": 50}', TRUE, 1),
('COMMERCIAL_INVOICE', 'INVOICE_DATE', '["发票日期", "invoice_date", "date", "开票日期"]', 'DATE', '{"format": "YYYY-MM-DD"}', TRUE, 2),
('COMMERCIAL_INVOICE', 'SELLER', '["卖方", "seller", "exporter", "供应商", "出口商"]', 'TEXT', '{"maxLength": 500}', TRUE, 3),
('COMMERCIAL_INVOICE', 'BUYER', '["买方", "buyer", "importer", "采购商", "进口商"]', 'TEXT', '{"maxLength": 500}', TRUE, 4),
('COMMERCIAL_INVOICE', 'TRADE_TERMS', '["贸易条款", "trade_terms", "incoterms", "价格条款"]', 'TEXT', '{"enum": ["FOB", "CIF", "CFR", "EXW", "DDP", "DDU"]}', TRUE, 5),
('COMMERCIAL_INVOICE', 'TOTAL_AMOUNT', '["发票总金额", "total_amount", "amount", "总价"]', 'NUMBER', '{"min": 0, "precision": 2}', TRUE, 6),
('COMMERCIAL_INVOICE', 'CURRENCY', '["币种", "currency", "货币"]', 'TEXT', '{"enum": ["USD", "EUR", "CNY", "JPY", "GBP"]}', TRUE, 7),
('COMMERCIAL_INVOICE', 'ORIGIN', '["原产地", "origin", "country_of_origin", "产地"]', 'TEXT', '{"maxLength": 100}', FALSE, 8),
('COMMERCIAL_INVOICE', 'DESTINATION', '["目的地", "destination", "目的国"]', 'TEXT', '{"maxLength": 100}', FALSE, 9);

-- 插入字段映射配置 - 装箱单
INSERT INTO field_mapping_config (document_type, field_name, field_aliases, field_type, validation_rules, is_required, sort_order) VALUES
('PACKING_LIST', 'PACKING_LIST_NO', '["装箱单号", "packing_list_number", "pl_no", "装箱单编号"]', 'TEXT', '{"maxLength": 50}', TRUE, 1),
('PACKING_LIST', 'DATE', '["日期", "date", "装箱日期"]', 'DATE', '{"format": "YYYY-MM-DD"}', TRUE, 2),
('PACKING_LIST', 'SHIPPER', '["发货人", "shipper", "consignor", "托运人"]', 'TEXT', '{"maxLength": 500}', TRUE, 3),
('PACKING_LIST', 'CONSIGNEE', '["收货人", "consignee", "receiver", "收货方"]', 'TEXT', '{"maxLength": 500}', TRUE, 4),
('PACKING_LIST', 'TOTAL_PACKAGES', '["总件数", "total_packages", "total_pkgs", "总包装数"]', 'NUMBER', '{"min": 1}', TRUE, 5),
('PACKING_LIST', 'TOTAL_GROSS_WEIGHT', '["总毛重", "total_gross_weight", "total_weight", "总重量"]', 'NUMBER', '{"min": 0, "unit": "KG"}', FALSE, 6),
('PACKING_LIST', 'TOTAL_NET_WEIGHT', '["总净重", "total_net_weight", "net_weight", "净重"]', 'NUMBER', '{"min": 0, "unit": "KG"}', FALSE, 7),
('PACKING_LIST', 'TOTAL_VOLUME', '["总体积", "total_volume", "total_cbm", "总立方"]', 'NUMBER', '{"min": 0, "unit": "CBM"}', FALSE, 8);

-- 插入LLM服务配置
INSERT INTO llm_service_config (service_name, service_type, api_endpoint, api_key, model_name, max_tokens, temperature, is_active, priority_order, rate_limit_per_minute) VALUES
('OpenAI GPT-4', 'OPENAI', 'https://api.openai.com/v1/chat/completions', 'your-openai-api-key', 'gpt-4', 4000, 0.1, FALSE, 1, 60),
('Claude 3', 'CLAUDE', 'https://api.anthropic.com/v1/messages', 'your-claude-api-key', 'claude-3-sonnet-20240229', 4000, 0.1, FALSE, 2, 60),
('通义千问', 'QWEN', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 'your-qwen-api-key', 'qwen-max', 4000, 0.1, TRUE, 3, 60);

-- 插入测试用的插件配置
INSERT INTO plugin_config (client_id, client_name, domain, api_key, config_data, max_daily_requests, is_active) VALUES
('test_client_001', '测试客户端', 'localhost', 'test_api_key_001', '{"theme": "light", "language": "zh-CN", "autoDetect": true}', 1000, TRUE),
('demo_client_001', '演示客户端', 'demo.example.com', 'demo_api_key_001', '{"theme": "dark", "language": "zh-CN", "autoDetect": true}', 500, TRUE);
