-- 货代单证智能解析插件数据库设计
-- 创建时间: 2024-01-15

-- 1. 文档解析记录表
CREATE TABLE document_parse_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型(PDF/JPG/PNG)',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_path VARCHAR(500) COMMENT '文件存储路径',
    document_type VARCHAR(50) NOT NULL COMMENT '单证类型',
    parse_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '解析状态(PENDING/PROCESSING/SUCCESS/FAILED)',
    raw_content TEXT COMMENT '原始提取内容',
    parsed_data JSON COMMENT '解析后的结构化数据',
    confidence_score DECIMAL(5,2) COMMENT '解析置信度分数',
    processing_time INT COMMENT '处理耗时(毫秒)',
    error_message TEXT COMMENT '错误信息',
    client_id VARCHAR(100) COMMENT '客户端ID',
    user_id VARCHAR(100) COMMENT '用户ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_client_user (client_id, user_id),
    INDEX idx_document_type (document_type),
    INDEX idx_parse_status (parse_status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档解析记录表';

-- 2. 字段映射配置表
CREATE TABLE field_mapping_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    document_type VARCHAR(50) NOT NULL COMMENT '单证类型',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    field_aliases JSON COMMENT '字段别名列表',
    field_type VARCHAR(50) COMMENT '字段类型(TEXT/NUMBER/DATE/EMAIL等)',
    validation_rules JSON COMMENT '验证规则',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    default_value VARCHAR(255) COMMENT '默认值',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_doc_field (document_type, field_name),
    INDEX idx_document_type (document_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段映射配置表';

-- 3. 用户操作日志表
CREATE TABLE user_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id VARCHAR(100) COMMENT '用户ID',
    client_id VARCHAR(100) COMMENT '客户端ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    document_id BIGINT COMMENT '关联文档ID',
    operation_data JSON COMMENT '操作数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_client_id (client_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_time (operation_time),
    FOREIGN KEY (document_id) REFERENCES document_parse_record(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户操作日志表';

-- 4. 插件配置表
CREATE TABLE plugin_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    client_id VARCHAR(100) NOT NULL COMMENT '客户端ID',
    client_name VARCHAR(255) COMMENT '客户端名称',
    domain VARCHAR(255) NOT NULL COMMENT '授权域名',
    api_key VARCHAR(255) NOT NULL COMMENT 'API密钥',
    config_data JSON NOT NULL COMMENT '配置数据',
    max_daily_requests INT DEFAULT 1000 COMMENT '每日最大请求数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    expires_at DATETIME COMMENT '过期时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_client_id (client_id),
    UNIQUE KEY uk_api_key (api_key),
    INDEX idx_domain (domain),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='插件配置表';

-- 5. 插件使用统计表
CREATE TABLE plugin_usage_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    client_id VARCHAR(100) NOT NULL COMMENT '客户端ID',
    domain VARCHAR(255) NOT NULL COMMENT '域名',
    usage_date DATE NOT NULL COMMENT '使用日期',
    parse_count INT DEFAULT 0 COMMENT '解析次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    avg_response_time DECIMAL(10,2) COMMENT '平均响应时间(毫秒)',
    total_file_size BIGINT DEFAULT 0 COMMENT '总文件大小(字节)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_client_date (client_id, usage_date),
    INDEX idx_usage_date (usage_date),
    INDEX idx_client_id (client_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='插件使用统计表';

-- 6. LLM服务配置表
CREATE TABLE llm_service_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    service_type VARCHAR(50) NOT NULL COMMENT '服务类型(OPENAI/CLAUDE/QWEN等)',
    api_endpoint VARCHAR(500) NOT NULL COMMENT 'API端点',
    api_key VARCHAR(500) NOT NULL COMMENT 'API密钥',
    model_name VARCHAR(100) COMMENT '模型名称',
    max_tokens INT DEFAULT 4000 COMMENT '最大token数',
    temperature DECIMAL(3,2) DEFAULT 0.1 COMMENT '温度参数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority_order INT DEFAULT 0 COMMENT '优先级顺序',
    rate_limit_per_minute INT DEFAULT 60 COMMENT '每分钟限制次数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_service_type (service_type),
    INDEX idx_is_active (is_active),
    INDEX idx_priority_order (priority_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LLM服务配置表';
