package com.cvi.compute_view_input.repository;

import com.cvi.compute_view_input.entity.DocumentParseRecord;
import com.cvi.compute_view_input.entity.FieldMappingConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 字段映射配置Repository
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Repository
public interface FieldMappingConfigRepository extends JpaRepository<FieldMappingConfig, Long> {

    /**
     * 根据单证类型查询字段映射配置
     */
    List<FieldMappingConfig> findByDocumentTypeAndIsActiveTrueOrderBySortOrder(
            DocumentParseRecord.DocumentType documentType);

    /**
     * 根据单证类型和字段名查询配置
     */
    Optional<FieldMappingConfig> findByDocumentTypeAndFieldNameAndIsActiveTrue(
            DocumentParseRecord.DocumentType documentType, String fieldName);

    /**
     * 查询所有启用的字段配置
     */
    List<FieldMappingConfig> findByIsActiveTrueOrderByDocumentTypeAscSortOrderAsc();

    /**
     * 根据字段别名模糊查询
     */
    @Query("SELECT f FROM FieldMappingConfig f WHERE f.documentType = :documentType AND f.isActive = true " +
           "AND (LOWER(f.fieldName) LIKE LOWER(CONCAT('%', :alias, '%')) " +
           "OR JSON_CONTAINS(LOWER(f.fieldAliases), LOWER(CONCAT('\"', :alias, '\"'))))")
    List<FieldMappingConfig> findByDocumentTypeAndFieldAlias(
            @Param("documentType") DocumentParseRecord.DocumentType documentType,
            @Param("alias") String alias);

    /**
     * 查询必填字段
     */
    List<FieldMappingConfig> findByDocumentTypeAndIsRequiredTrueAndIsActiveTrueOrderBySortOrder(
            DocumentParseRecord.DocumentType documentType);

    /**
     * 统计单证类型的字段数量
     */
    @Query("SELECT COUNT(f) FROM FieldMappingConfig f WHERE f.documentType = :documentType AND f.isActive = true")
    long countByDocumentTypeAndIsActiveTrue(@Param("documentType") DocumentParseRecord.DocumentType documentType);

    /**
     * 查询指定字段类型的配置
     */
    List<FieldMappingConfig> findByDocumentTypeAndFieldTypeAndIsActiveTrueOrderBySortOrder(
            DocumentParseRecord.DocumentType documentType, FieldMappingConfig.FieldType fieldType);

    /**
     * 批量更新字段状态
     */
    @Query("UPDATE FieldMappingConfig f SET f.isActive = :isActive WHERE f.documentType = :documentType")
    void updateActiveStatusByDocumentType(
            @Param("documentType") DocumentParseRecord.DocumentType documentType,
            @Param("isActive") Boolean isActive);
}
