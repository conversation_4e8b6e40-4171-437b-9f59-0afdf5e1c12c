package com.cvi.compute_view_input.repository;

import com.cvi.compute_view_input.entity.DocumentParseRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 文档解析记录Repository
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Repository
public interface DocumentParseRecordRepository extends JpaRepository<DocumentParseRecord, Long> {

    /**
     * 根据客户端ID和用户ID查询解析记录
     */
    Page<DocumentParseRecord> findByClientIdAndUserIdOrderByCreatedTimeDesc(
            String clientId, String userId, Pageable pageable);

    /**
     * 根据客户端ID查询解析记录
     */
    Page<DocumentParseRecord> findByClientIdOrderByCreatedTimeDesc(
            String clientId, Pageable pageable);

    /**
     * 根据解析状态查询记录
     */
    List<DocumentParseRecord> findByParseStatus(DocumentParseRecord.ParseStatus parseStatus);

    /**
     * 根据单证类型查询记录
     */
    Page<DocumentParseRecord> findByDocumentTypeOrderByCreatedTimeDesc(
            DocumentParseRecord.DocumentType documentType, Pageable pageable);

    /**
     * 查询指定时间范围内的记录
     */
    @Query("SELECT d FROM DocumentParseRecord d WHERE d.createdTime BETWEEN :startTime AND :endTime ORDER BY d.createdTime DESC")
    List<DocumentParseRecord> findByCreatedTimeBetween(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计客户端今日解析次数
     */
    @Query("SELECT COUNT(d) FROM DocumentParseRecord d WHERE d.clientId = :clientId AND DATE(d.createdTime) = CURRENT_DATE")
    long countTodayParsesByClientId(@Param("clientId") String clientId);

    /**
     * 统计成功解析的记录数
     */
    @Query("SELECT COUNT(d) FROM DocumentParseRecord d WHERE d.parseStatus = 'SUCCESS' AND d.clientId = :clientId")
    long countSuccessParsesByClientId(@Param("clientId") String clientId);

    /**
     * 查询平均处理时间
     */
    @Query("SELECT AVG(d.processingTime) FROM DocumentParseRecord d WHERE d.parseStatus = 'SUCCESS' AND d.clientId = :clientId")
    Double getAverageProcessingTimeByClientId(@Param("clientId") String clientId);

    /**
     * 查询最近的成功解析记录
     */
    Optional<DocumentParseRecord> findFirstByClientIdAndParseStatusOrderByCreatedTimeDesc(
            String clientId, DocumentParseRecord.ParseStatus parseStatus);

    /**
     * 删除指定时间之前的记录
     */
    void deleteByCreatedTimeBefore(LocalDateTime cutoffTime);

    /**
     * 根据文件路径查询记录
     */
    Optional<DocumentParseRecord> findByFilePath(String filePath);

    /**
     * 查询处理中的记录（用于监控）
     */
    @Query("SELECT d FROM DocumentParseRecord d WHERE d.parseStatus = 'PROCESSING' AND d.createdTime < :timeoutTime")
    List<DocumentParseRecord> findTimeoutProcessingRecords(@Param("timeoutTime") LocalDateTime timeoutTime);
}
