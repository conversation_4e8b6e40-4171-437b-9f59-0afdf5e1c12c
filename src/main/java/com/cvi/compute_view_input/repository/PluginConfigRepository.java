package com.cvi.compute_view_input.repository;

import com.cvi.compute_view_input.entity.PluginConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 插件配置Repository
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Repository
public interface PluginConfigRepository extends JpaRepository<PluginConfig, Long> {

    /**
     * 根据客户端ID查询配置
     */
    Optional<PluginConfig> findByClientId(String clientId);

    /**
     * 根据API密钥查询配置
     */
    Optional<PluginConfig> findByApiKey(String apiKey);

    /**
     * 根据域名查询配置
     */
    List<PluginConfig> findByDomainAndIsActiveTrue(String domain);

    /**
     * 查询所有启用的配置
     */
    List<PluginConfig> findByIsActiveTrueOrderByCreatedTimeDesc();

    /**
     * 查询即将过期的配置
     */
    @Query("SELECT p FROM PluginConfig p WHERE p.isActive = true AND p.expiresAt IS NOT NULL " +
           "AND p.expiresAt BETWEEN :now AND :warningTime")
    List<PluginConfig> findExpiringConfigs(
            @Param("now") LocalDateTime now,
            @Param("warningTime") LocalDateTime warningTime);

    /**
     * 查询已过期的配置
     */
    @Query("SELECT p FROM PluginConfig p WHERE p.isActive = true AND p.expiresAt IS NOT NULL " +
           "AND p.expiresAt < :now")
    List<PluginConfig> findExpiredConfigs(@Param("now") LocalDateTime now);

    /**
     * 根据域名模糊匹配查询配置
     */
    @Query("SELECT p FROM PluginConfig p WHERE p.isActive = true " +
           "AND (p.domain = '*' OR p.domain = :domain " +
           "OR (p.domain LIKE '*.%' AND :domain LIKE CONCAT('%', SUBSTRING(p.domain, 3))))")
    List<PluginConfig> findByDomainPattern(@Param("domain") String domain);

    /**
     * 统计启用的配置数量
     */
    long countByIsActiveTrue();

    /**
     * 查询指定客户端名称的配置
     */
    List<PluginConfig> findByClientNameContainingIgnoreCaseAndIsActiveTrue(String clientName);

    /**
     * 批量更新过期配置状态
     */
    @Query("UPDATE PluginConfig p SET p.isActive = false WHERE p.expiresAt < :now AND p.isActive = true")
    int deactivateExpiredConfigs(@Param("now") LocalDateTime now);

    /**
     * 检查API密钥是否存在
     */
    boolean existsByApiKey(String apiKey);

    /**
     * 检查客户端ID是否存在
     */
    boolean existsByClientId(String clientId);
}
