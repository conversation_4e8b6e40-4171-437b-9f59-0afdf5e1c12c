package com.cvi.compute_view_input.service;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * OCR文字识别服务
 * 使用Tesseract进行图片文字识别
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Slf4j
@Service
public class OcrService {

    @Value("${app.ocr.data-path:}")
    private String tessDataPath;

    @Value("${app.ocr.default-language:chi_sim+eng}")
    private String defaultLanguage;

    @Value("${app.ocr.page-seg-mode:3}")
    private int pageSegMode;

    @Value("${app.ocr.oem-mode:3}")
    private int oemMode;

    private ITesseract tesseract;

    @PostConstruct
    public void init() {
        try {
            tesseract = new Tesseract();
            
            // 设置Tesseract数据路径
            if (tessDataPath != null && !tessDataPath.isEmpty()) {
                tesseract.setDatapath(tessDataPath);
                log.info("设置Tesseract数据路径: {}", tessDataPath);
            }
            
            // 设置默认语言
            tesseract.setLanguage(defaultLanguage);
            
            // 设置OCR引擎模式
            tesseract.setOcrEngineMode(oemMode);
            
            // 设置页面分割模式
            tesseract.setPageSegMode(pageSegMode);
            
            // 设置其他配置
            tesseract.setVariable("user_defined_dpi", "300");
            tesseract.setVariable("tessedit_char_whitelist", 
                "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz" +
                "一二三四五六七八九十百千万亿" +
                "年月日时分秒" +
                "公司有限责任股份集团" +
                "发货人收货人通知方" +
                "提单号集装箱号船名航次" +
                "起运港目的港装货港卸货港" +
                "货物描述件数毛重体积" +
                "发票号日期金额币种" +
                "：:：，,。.；;！!？?（）()【】[]《》<>""''");
            
            log.info("OCR服务初始化完成，语言: {}, 页面分割模式: {}, 引擎模式: {}", 
                    defaultLanguage, pageSegMode, oemMode);
                    
        } catch (Exception e) {
            log.error("OCR服务初始化失败", e);
            throw new RuntimeException("OCR服务初始化失败", e);
        }
    }

    /**
     * 从图片文件识别文字
     */
    public String recognizeText(String imagePath) throws TesseractException {
        return recognizeText(imagePath, defaultLanguage);
    }

    /**
     * 从图片文件识别文字（指定语言）
     */
    public String recognizeText(String imagePath, String language) throws TesseractException {
        log.info("开始OCR识别: {}, 语言: {}", imagePath, language);
        
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            throw new IllegalArgumentException("图片文件不存在: " + imagePath);
        }
        
        try {
            // 设置语言
            tesseract.setLanguage(language);
            
            // 执行OCR识别
            long startTime = System.currentTimeMillis();
            String result = tesseract.doOCR(imageFile);
            long endTime = System.currentTimeMillis();
            
            // 清理识别结果
            result = cleanOcrResult(result);
            
            log.info("OCR识别完成，耗时: {}ms, 识别文字长度: {} 字符", 
                    endTime - startTime, result.length());
            
            return result;
            
        } catch (TesseractException e) {
            log.error("OCR识别失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从BufferedImage识别文字
     */
    public String recognizeText(BufferedImage image) throws TesseractException {
        return recognizeText(image, defaultLanguage);
    }

    /**
     * 从BufferedImage识别文字（指定语言）
     */
    public String recognizeText(BufferedImage image, String language) throws TesseractException {
        log.info("开始OCR识别BufferedImage, 尺寸: {}x{}, 语言: {}", 
                image.getWidth(), image.getHeight(), language);
        
        try {
            // 设置语言
            tesseract.setLanguage(language);
            
            // 执行OCR识别
            long startTime = System.currentTimeMillis();
            String result = tesseract.doOCR(image);
            long endTime = System.currentTimeMillis();
            
            // 清理识别结果
            result = cleanOcrResult(result);
            
            log.info("OCR识别完成，耗时: {}ms, 识别文字长度: {} 字符", 
                    endTime - startTime, result.length());
            
            return result;
            
        } catch (TesseractException e) {
            log.error("OCR识别失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取详细的OCR识别结果
     */
    public OcrResult recognizeTextWithDetails(String imagePath, String language) throws TesseractException {
        log.info("开始详细OCR识别: {}, 语言: {}", imagePath, language);
        
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            throw new IllegalArgumentException("图片文件不存在: " + imagePath);
        }
        
        try {
            // 设置语言
            tesseract.setLanguage(language);
            
            long startTime = System.currentTimeMillis();
            
            // 获取识别文字
            String text = tesseract.doOCR(imageFile);
            
            // 获取置信度信息
            List<net.sourceforge.tess4j.Word> words = tesseract.getWords(imageFile, ITesseract.RenderedFormat.TEXT);
            
            long endTime = System.currentTimeMillis();
            
            // 构建结果
            OcrResult result = new OcrResult();
            result.setText(cleanOcrResult(text));
            result.setProcessingTime((int) (endTime - startTime));
            result.setWordCount(words.size());
            
            // 计算平均置信度
            if (!words.isEmpty()) {
                double totalConfidence = words.stream()
                    .mapToDouble(word -> word.getConfidence())
                    .average()
                    .orElse(0.0);
                result.setAverageConfidence(totalConfidence);
            }
            
            log.info("详细OCR识别完成，耗时: {}ms, 单词数: {}, 平均置信度: {}", 
                    result.getProcessingTime(), result.getWordCount(), result.getAverageConfidence());
            
            return result;
            
        } catch (TesseractException e) {
            log.error("详细OCR识别失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查OCR服务是否可用
     */
    public boolean isAvailable() {
        try {
            // 创建一个简单的测试图片进行识别测试
            BufferedImage testImage = new BufferedImage(100, 50, BufferedImage.TYPE_INT_RGB);
            tesseract.doOCR(testImage);
            return true;
        } catch (Exception e) {
            log.warn("OCR服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取支持的语言列表
     */
    public List<String> getSupportedLanguages() {
        // 常见的Tesseract语言代码
        return Arrays.asList(
            "eng",          // 英语
            "chi_sim",      // 简体中文
            "chi_tra",      // 繁体中文
            "jpn",          // 日语
            "kor",          // 韩语
            "fra",          // 法语
            "deu",          // 德语
            "spa",          // 西班牙语
            "rus",          // 俄语
            "ara",          // 阿拉伯语
            "chi_sim+eng",  // 中英混合
            "chi_tra+eng"   // 繁体中文+英语
        );
    }

    /**
     * 清理OCR识别结果
     */
    private String cleanOcrResult(String text) {
        if (text == null) {
            return "";
        }
        
        // 移除多余的空白字符
        text = text.replaceAll("\\s+", " ");
        
        // 移除首尾空白
        text = text.trim();
        
        // 移除一些常见的OCR错误字符
        text = text.replaceAll("[|\\\\]", "");
        
        return text;
    }

    /**
     * OCR识别结果
     */
    public static class OcrResult {
        private String text;
        private int processingTime;
        private int wordCount;
        private double averageConfidence;

        // Getters and Setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public int getProcessingTime() { return processingTime; }
        public void setProcessingTime(int processingTime) { this.processingTime = processingTime; }
        public int getWordCount() { return wordCount; }
        public void setWordCount(int wordCount) { this.wordCount = wordCount; }
        public double getAverageConfidence() { return averageConfidence; }
        public void setAverageConfidence(double averageConfidence) { this.averageConfidence = averageConfidence; }
    }
}
