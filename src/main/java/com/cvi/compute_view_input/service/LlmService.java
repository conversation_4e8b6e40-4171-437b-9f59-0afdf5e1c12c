package com.cvi.compute_view_input.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * LLM大模型集成服务
 * 支持多种大模型API调用
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Slf4j
@Service
public class LlmService {

    @Value("${app.llm.timeout:30000}")
    private int timeoutMs;

    @Value("${app.llm.retry-count:3}")
    private int retryCount;

    @Value("${app.llm.default-service:QWEN}")
    private String defaultService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final OkHttpClient httpClient;

    public LlmService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    /**
     * 调用LLM解析文档内容
     */
    public LlmResponse parseDocument(String documentContent, String documentType, String customPrompt) {
        log.info("开始LLM解析，文档类型: {}, 内容长度: {} 字符", documentType, documentContent.length());
        
        try {
            // 构建提示词
            String prompt = buildPrompt(documentContent, documentType, customPrompt);
            
            // 调用LLM
            String response = callLlm(prompt, defaultService);
            
            // 解析响应
            return parseLlmResponse(response, documentType);
            
        } catch (Exception e) {
            log.error("LLM解析失败", e);
            throw new RuntimeException("LLM解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用指定的LLM服务
     */
    public String callLlm(String prompt, String serviceType) throws IOException {
        log.debug("调用LLM服务: {}", serviceType);
        
        switch (serviceType.toUpperCase()) {
            case "OPENAI":
                return callOpenAI(prompt);
            case "CLAUDE":
                return callClaude(prompt);
            case "QWEN":
                return callQwen(prompt);
            default:
                throw new IllegalArgumentException("不支持的LLM服务类型: " + serviceType);
        }
    }

    /**
     * 调用OpenAI GPT-4
     */
    private String callOpenAI(String prompt) throws IOException {
        String apiKey = System.getenv("OPENAI_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            throw new IllegalStateException("OpenAI API Key未配置");
        }

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "gpt-4");
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });
        requestBody.put("max_tokens", 4000);
        requestBody.put("temperature", 0.1);

        RequestBody body = RequestBody.create(
            objectMapper.writeValueAsString(requestBody),
            MediaType.get("application/json")
        );

        Request request = new Request.Builder()
                .url("https://api.openai.com/v1/chat/completions")
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("OpenAI API调用失败: " + response.code() + " " + response.message());
            }

            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            return jsonNode.path("choices").get(0).path("message").path("content").asText();
        }
    }

    /**
     * 调用Claude 3
     */
    private String callClaude(String prompt) throws IOException {
        String apiKey = System.getenv("CLAUDE_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            throw new IllegalStateException("Claude API Key未配置");
        }

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "claude-3-sonnet-20240229");
        requestBody.put("max_tokens", 4000);
        requestBody.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });

        RequestBody body = RequestBody.create(
            objectMapper.writeValueAsString(requestBody),
            MediaType.get("application/json")
        );

        Request request = new Request.Builder()
                .url("https://api.anthropic.com/v1/messages")
                .header("x-api-key", apiKey)
                .header("Content-Type", "application/json")
                .header("anthropic-version", "2023-06-01")
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Claude API调用失败: " + response.code() + " " + response.message());
            }

            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            return jsonNode.path("content").get(0).path("text").asText();
        }
    }

    /**
     * 调用通义千问
     */
    private String callQwen(String prompt) throws IOException {
        String apiKey = System.getenv("QWEN_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            // 使用测试模式，返回模拟数据
            log.warn("通义千问API Key未配置，使用测试模式");
            return generateMockResponse(prompt);
        }

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "qwen-max");
        
        Map<String, Object> input = new HashMap<>();
        input.put("messages", new Object[]{
            Map.of("role", "user", "content", prompt)
        });
        requestBody.put("input", input);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("max_tokens", 4000);
        parameters.put("temperature", 0.1);
        requestBody.put("parameters", parameters);

        RequestBody body = RequestBody.create(
            objectMapper.writeValueAsString(requestBody),
            MediaType.get("application/json")
        );

        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation")
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("通义千问API调用失败: " + response.code() + " " + response.message());
            }

            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            return jsonNode.path("output").path("choices").get(0).path("message").path("content").asText();
        }
    }

    /**
     * 构建提示词
     */
    private String buildPrompt(String documentContent, String documentType, String customPrompt) {
        if (customPrompt != null && !customPrompt.isEmpty()) {
            return customPrompt.replace("{document_content}", documentContent);
        }

        // 使用预定义的提示词模板
        return getPromptTemplate(documentType).replace("{document_content}", documentContent);
    }

    /**
     * 获取提示词模板
     */
    private String getPromptTemplate(String documentType) {
        switch (documentType.toUpperCase()) {
            case "BILL_OF_LADING":
                return """
                    你是货代行业专家，请从以下海运提单文档中提取关键信息：
                    
                    文档内容：{document_content}
                    
                    请提取以下信息并返回JSON格式：
                    - 提单号 (BL_NO)
                    - 发货人 (SHIPPER) 
                    - 收货人 (CONSIGNEE)
                    - 通知方 (NOTIFY_PARTY)
                    - 起运港 (PORT_OF_LOADING)
                    - 目的港 (PORT_OF_DISCHARGE)
                    - 货物描述 (GOODS_DESCRIPTION)
                    - 件数 (PACKAGES)
                    - 毛重 (GROSS_WEIGHT)
                    - 体积 (MEASUREMENT)
                    - 集装箱号 (CONTAINER_NO)
                    - 船名航次 (VESSEL_VOYAGE)
                    - 开船日期 (ETD)
                    - 运费条款 (FREIGHT_TERMS)
                    
                    返回标准JSON格式，字段名使用英文，便于系统处理。
                    """;
                    
            case "COMMERCIAL_INVOICE":
                return """
                    你是货代行业专家，请从以下商业发票中提取关键信息：
                    
                    文档内容：{document_content}
                    
                    请提取以下信息：
                    - 发票号 (INVOICE_NO)
                    - 发票日期 (INVOICE_DATE)
                    - 卖方信息 (SELLER)
                    - 买方信息 (BUYER)
                    - 贸易条款 (TRADE_TERMS) 如FOB、CIF、CFR等
                    - 货物明细 (GOODS_DETAILS) 包括品名、数量、单价
                    - 发票总金额 (TOTAL_AMOUNT)
                    - 币种 (CURRENCY)
                    - 原产地 (ORIGIN)
                    - 目的地 (DESTINATION)
                    
                    注意识别常见贸易术语：FOB、CIF、CFR、EXW、DDP等
                    返回JSON格式，金额字段为数字类型。
                    """;
                    
            default:
                return """
                    你是文档解析专家，请分析以下文档内容并提取关键信息：
                    
                    文档内容：{document_content}
                    
                    请：
                    1. 理解文档的类型和结构
                    2. 提取所有重要的信息字段
                    3. 返回结构化的JSON数据
                    4. JSON字段名要语义化，便于理解
                    
                    返回格式：{"field1": "value1", "field2": "value2", ...}
                    """;
        }
    }

    /**
     * 解析LLM响应
     */
    private LlmResponse parseLlmResponse(String response, String documentType) {
        try {
            // 提取JSON部分
            String jsonStr = extractJsonFromResponse(response);
            
            // 解析JSON
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            Map<String, Object> parsedData = objectMapper.convertValue(jsonNode, Map.class);
            
            LlmResponse llmResponse = new LlmResponse();
            llmResponse.setSuccess(true);
            llmResponse.setParsedData(parsedData);
            llmResponse.setRawResponse(response);
            llmResponse.setDocumentType(documentType);
            
            return llmResponse;
            
        } catch (Exception e) {
            log.error("解析LLM响应失败: {}", e.getMessage(), e);
            
            LlmResponse llmResponse = new LlmResponse();
            llmResponse.setSuccess(false);
            llmResponse.setErrorMessage("解析LLM响应失败: " + e.getMessage());
            llmResponse.setRawResponse(response);
            
            return llmResponse;
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束位置
        int jsonStart = response.indexOf('{');
        int jsonEnd = response.lastIndexOf('}');
        
        if (jsonStart >= 0 && jsonEnd > jsonStart) {
            return response.substring(jsonStart, jsonEnd + 1);
        }
        
        throw new IllegalArgumentException("响应中未找到有效的JSON格式");
    }

    /**
     * 生成模拟响应（用于测试）
     */
    private String generateMockResponse(String prompt) {
        log.info("生成模拟LLM响应");
        
        // 根据提示词内容生成相应的模拟数据
        if (prompt.contains("提单") || prompt.contains("BILL_OF_LADING")) {
            return """
                {
                    "BL_NO": "MOCK123456789",
                    "SHIPPER": "测试发货公司",
                    "CONSIGNEE": "测试收货公司",
                    "PORT_OF_LOADING": "CNSHA",
                    "PORT_OF_DISCHARGE": "USLAX",
                    "GOODS_DESCRIPTION": "测试货物",
                    "PACKAGES": "100",
                    "GROSS_WEIGHT": "1000",
                    "CONTAINER_NO": "MOCK1234567"
                }
                """;
        } else {
            return """
                {
                    "document_type": "未知文档",
                    "extracted_info": "这是模拟的解析结果",
                    "confidence": "0.8"
                }
                """;
        }
    }

    /**
     * LLM响应结果
     */
    public static class LlmResponse {
        private boolean success;
        private Map<String, Object> parsedData;
        private String rawResponse;
        private String errorMessage;
        private String documentType;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public Map<String, Object> getParsedData() { return parsedData; }
        public void setParsedData(Map<String, Object> parsedData) { this.parsedData = parsedData; }
        public String getRawResponse() { return rawResponse; }
        public void setRawResponse(String rawResponse) { this.rawResponse = rawResponse; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public String getDocumentType() { return documentType; }
        public void setDocumentType(String documentType) { this.documentType = documentType; }
    }
}
