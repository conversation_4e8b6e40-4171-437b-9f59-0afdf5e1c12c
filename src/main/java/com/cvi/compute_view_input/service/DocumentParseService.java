package com.cvi.compute_view_input.service;

import com.cvi.compute_view_input.dto.DocumentParseRequest;
import com.cvi.compute_view_input.dto.DocumentParseResponse;
import com.cvi.compute_view_input.entity.DocumentParseRecord;
import com.cvi.compute_view_input.repository.DocumentParseRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.TesseractException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文档解析核心业务服务
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentParseService {

    private final FileProcessingService fileProcessingService;
    private final OcrService ocrService;
    private final LlmService llmService;
    private final DocumentParseRecordRepository documentParseRecordRepository;

    /**
     * 解析文档
     */
    @Transactional
    public DocumentParseResponse parseDocument(DocumentParseRequest request) {
        log.info("开始解析文档: {}, 客户端: {}", request.getFile().getOriginalFilename(), request.getClientId());
        
        // 验证请求
        request.validateFile();
        
        // 创建解析记录
        DocumentParseRecord record = createParseRecord(request);
        record = documentParseRecordRepository.save(record);
        
        try {
            // 更新状态为处理中
            record.setParseStatus(DocumentParseRecord.ParseStatus.PROCESSING);
            record = documentParseRecordRepository.save(record);
            
            long startTime = System.currentTimeMillis();
            
            // 1. 保存文件
            String filePath = fileProcessingService.saveUploadedFile(request.getFile(), request.getClientId());
            record.setFilePath(filePath);
            
            // 2. 提取文档内容
            String documentContent = extractDocumentContent(request, filePath);
            record.setRawContent(documentContent);
            
            // 3. 自动检测文档类型（如果需要）
            if (request.getDocumentType() == DocumentParseRecord.DocumentType.AUTO_DETECT) {
                DocumentParseRecord.DocumentType detectedType = detectDocumentType(documentContent);
                record.setDocumentType(detectedType);
            }
            
            // 4. LLM解析
            LlmService.LlmResponse llmResponse = llmService.parseDocument(
                documentContent, 
                record.getDocumentType().name(), 
                request.getCustomRequirement()
            );
            
            if (llmResponse.isSuccess()) {
                // 解析成功
                record.setParsedData(llmResponse.getParsedData());
                record.setParseStatus(DocumentParseRecord.ParseStatus.SUCCESS);
                record.setConfidenceScore(calculateConfidenceScore(llmResponse.getParsedData()));
            } else {
                // 解析失败
                record.setParseStatus(DocumentParseRecord.ParseStatus.FAILED);
                record.setErrorMessage(llmResponse.getErrorMessage());
            }
            
            // 5. 记录处理时间
            long endTime = System.currentTimeMillis();
            record.setProcessingTime((int) (endTime - startTime));
            
            // 保存最终结果
            record = documentParseRecordRepository.save(record);
            
            log.info("文档解析完成: ID={}, 状态={}, 耗时={}ms", 
                    record.getId(), record.getParseStatus(), record.getProcessingTime());
            
            // 构建响应
            DocumentParseResponse response = request.getIncludeRawContent() ? 
                DocumentParseResponse.fromEntityWithRawContent(record) : 
                DocumentParseResponse.fromEntity(record);
            
            return response;
            
        } catch (Exception e) {
            log.error("文档解析失败: {}", e.getMessage(), e);
            
            // 更新记录状态
            record.setParseStatus(DocumentParseRecord.ParseStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            documentParseRecordRepository.save(record);
            
            // 返回错误响应
            DocumentParseResponse response = DocumentParseResponse.fromEntity(record);
            return response;
        }
    }

    /**
     * 提取文档内容
     */
    private String extractDocumentContent(DocumentParseRequest request, String filePath) 
            throws IOException, TesseractException {
        
        if (request.isPdfFile()) {
            // PDF文件处理
            log.info("提取PDF文档内容: {}", filePath);
            return fileProcessingService.extractTextFromPdf(filePath);
            
        } else if (request.isImageFile()) {
            // 图片文件OCR处理
            log.info("OCR识别图片文档: {}", filePath);
            
            if (!request.getEnableOcr()) {
                throw new IllegalArgumentException("图片文件需要启用OCR功能");
            }
            
            // 加载图片
            BufferedImage image = fileProcessingService.loadImageFile(filePath);
            
            // OCR识别
            return ocrService.recognizeText(image, request.getOcrLanguage());
            
        } else {
            throw new IllegalArgumentException("不支持的文件类型: " + request.getFileType());
        }
    }

    /**
     * 自动检测文档类型
     */
    private DocumentParseRecord.DocumentType detectDocumentType(String content) {
        log.debug("自动检测文档类型，内容长度: {} 字符", content.length());
        
        String lowerContent = content.toLowerCase();
        
        // 提单关键词
        if (containsAny(lowerContent, "bill of lading", "b/l", "提单", "bl no", "shipper", "consignee")) {
            return DocumentParseRecord.DocumentType.BILL_OF_LADING;
        }
        
        // 商业发票关键词
        if (containsAny(lowerContent, "commercial invoice", "invoice", "发票", "inv no", "seller", "buyer")) {
            return DocumentParseRecord.DocumentType.COMMERCIAL_INVOICE;
        }
        
        // 装箱单关键词
        if (containsAny(lowerContent, "packing list", "装箱单", "packing", "packages", "gross weight")) {
            return DocumentParseRecord.DocumentType.PACKING_LIST;
        }
        
        // 报关单关键词
        if (containsAny(lowerContent, "customs declaration", "报关单", "customs", "declaration")) {
            return DocumentParseRecord.DocumentType.CUSTOMS_DECLARATION;
        }
        
        // 原产地证关键词
        if (containsAny(lowerContent, "certificate of origin", "原产地证", "origin", "certificate")) {
            return DocumentParseRecord.DocumentType.CERTIFICATE_OF_ORIGIN;
        }
        
        // 默认返回提单类型
        log.warn("无法自动检测文档类型，使用默认类型: BILL_OF_LADING");
        return DocumentParseRecord.DocumentType.BILL_OF_LADING;
    }

    /**
     * 检查内容是否包含任意关键词
     */
    private boolean containsAny(String content, String... keywords) {
        for (String keyword : keywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算置信度分数
     */
    private BigDecimal calculateConfidenceScore(Map<String, Object> parsedData) {
        if (parsedData == null || parsedData.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 简单的置信度计算：基于提取到的字段数量
        int totalFields = parsedData.size();
        int validFields = 0;
        
        for (Map.Entry<String, Object> entry : parsedData.entrySet()) {
            Object value = entry.getValue();
            if (value != null && !value.toString().trim().isEmpty()) {
                validFields++;
            }
        }
        
        if (totalFields == 0) {
            return BigDecimal.ZERO;
        }
        
        double score = (double) validFields / totalFields * 100;
        return BigDecimal.valueOf(score).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 创建解析记录
     */
    private DocumentParseRecord createParseRecord(DocumentParseRequest request) {
        DocumentParseRecord record = new DocumentParseRecord();
        record.setFileName(request.getFile().getOriginalFilename());
        record.setFileType(request.getFileType());
        record.setFileSize(request.getFile().getSize());
        record.setDocumentType(request.getDocumentType());
        record.setParseStatus(DocumentParseRecord.ParseStatus.PENDING);
        record.setClientId(request.getClientId());
        record.setUserId(request.getUserId());
        
        return record;
    }

    /**
     * 根据ID获取解析记录
     */
    public DocumentParseResponse getParseRecord(Long id) {
        DocumentParseRecord record = documentParseRecordRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("解析记录不存在: " + id));
        
        return DocumentParseResponse.fromEntity(record);
    }

    /**
     * 根据ID获取解析记录（包含原始内容）
     */
    public DocumentParseResponse getParseRecordWithRawContent(Long id) {
        DocumentParseRecord record = documentParseRecordRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("解析记录不存在: " + id));
        
        return DocumentParseResponse.fromEntityWithRawContent(record);
    }

    /**
     * 删除解析记录和相关文件
     */
    @Transactional
    public void deleteParseRecord(Long id) {
        DocumentParseRecord record = documentParseRecordRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("解析记录不存在: " + id));
        
        // 删除文件
        if (record.getFilePath() != null) {
            fileProcessingService.deleteFile(record.getFilePath());
        }
        
        // 删除记录
        documentParseRecordRepository.delete(record);
        
        log.info("删除解析记录: ID={}", id);
    }

    /**
     * 重新解析文档
     */
    @Transactional
    public DocumentParseResponse reparseDocument(Long id, String customRequirement) {
        DocumentParseRecord record = documentParseRecordRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("解析记录不存在: " + id));
        
        if (record.getRawContent() == null || record.getRawContent().isEmpty()) {
            throw new IllegalArgumentException("原始内容为空，无法重新解析");
        }
        
        try {
            // 重新调用LLM解析
            LlmService.LlmResponse llmResponse = llmService.parseDocument(
                record.getRawContent(), 
                record.getDocumentType().name(), 
                customRequirement
            );
            
            if (llmResponse.isSuccess()) {
                record.setParsedData(llmResponse.getParsedData());
                record.setParseStatus(DocumentParseRecord.ParseStatus.SUCCESS);
                record.setConfidenceScore(calculateConfidenceScore(llmResponse.getParsedData()));
                record.setErrorMessage(null);
            } else {
                record.setParseStatus(DocumentParseRecord.ParseStatus.FAILED);
                record.setErrorMessage(llmResponse.getErrorMessage());
            }
            
            record = documentParseRecordRepository.save(record);
            
            log.info("重新解析完成: ID={}, 状态={}", record.getId(), record.getParseStatus());
            
            return DocumentParseResponse.fromEntity(record);
            
        } catch (Exception e) {
            log.error("重新解析失败: {}", e.getMessage(), e);
            
            record.setParseStatus(DocumentParseRecord.ParseStatus.FAILED);
            record.setErrorMessage(e.getMessage());
            documentParseRecordRepository.save(record);
            
            return DocumentParseResponse.fromEntity(record);
        }
    }
}
