package com.cvi.compute_view_input.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件处理服务
 * 负责文件上传、存储、PDF解析等基础功能
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Slf4j
@Service
public class FileProcessingService {

    @Value("${app.file.upload-dir:./uploads}")
    private String uploadDir;

    @Value("${app.file.temp-dir:./temp}")
    private String tempDir;

    /**
     * 保存上传的文件
     */
    public String saveUploadedFile(MultipartFile file, String clientId) throws IOException {
        // 创建上传目录
        Path uploadPath = createUploadDirectory(clientId);
        
        // 生成唯一文件名
        String originalFileName = file.getOriginalFilename();
        String fileExtension = FilenameUtils.getExtension(originalFileName);
        String uniqueFileName = generateUniqueFileName(originalFileName, fileExtension);
        
        // 保存文件
        Path filePath = uploadPath.resolve(uniqueFileName);
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        
        log.info("文件保存成功: {}", filePath.toString());
        return filePath.toString();
    }

    /**
     * 从PDF文件提取文本内容
     */
    public String extractTextFromPdf(String filePath) throws IOException {
        log.info("开始提取PDF文本: {}", filePath);
        
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            
            // 设置提取参数
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");
            
            String text = stripper.getText(document);
            
            log.info("PDF文本提取完成，内容长度: {} 字符", text.length());
            return text;
            
        } catch (IOException e) {
            log.error("PDF文本提取失败: {}", e.getMessage(), e);
            throw new IOException("PDF文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从PDF文件提取指定页面的文本
     */
    public String extractTextFromPdf(String filePath, int startPage, int endPage) throws IOException {
        log.info("开始提取PDF文本 (页面 {}-{}): {}", startPage, endPage, filePath);
        
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            
            // 设置页面范围
            stripper.setStartPage(startPage);
            stripper.setEndPage(endPage);
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");
            
            String text = stripper.getText(document);
            
            log.info("PDF文本提取完成，内容长度: {} 字符", text.length());
            return text;
            
        } catch (IOException e) {
            log.error("PDF文本提取失败: {}", e.getMessage(), e);
            throw new IOException("PDF文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取PDF文档信息
     */
    public PdfDocumentInfo getPdfDocumentInfo(String filePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PdfDocumentInfo info = new PdfDocumentInfo();
            info.setPageCount(document.getNumberOfPages());
            info.setFilePath(filePath);
            
            // 获取文档元数据
            if (document.getDocumentInformation() != null) {
                info.setTitle(document.getDocumentInformation().getTitle());
                info.setAuthor(document.getDocumentInformation().getAuthor());
                info.setSubject(document.getDocumentInformation().getSubject());
                info.setCreationDate(document.getDocumentInformation().getCreationDate());
            }
            
            return info;
        }
    }

    /**
     * 验证图片文件并转换为BufferedImage
     */
    public BufferedImage loadImageFile(String filePath) throws IOException {
        log.info("开始加载图片文件: {}", filePath);
        
        File imageFile = new File(filePath);
        if (!imageFile.exists()) {
            throw new IOException("图片文件不存在: " + filePath);
        }
        
        BufferedImage image = ImageIO.read(imageFile);
        if (image == null) {
            throw new IOException("无法读取图片文件，可能格式不支持: " + filePath);
        }
        
        log.info("图片加载成功，尺寸: {}x{}", image.getWidth(), image.getHeight());
        return image;
    }

    /**
     * 获取文件信息
     */
    public FileInfo getFileInfo(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            throw new IOException("文件不存在: " + filePath);
        }
        
        FileInfo info = new FileInfo();
        info.setFilePath(filePath);
        info.setFileName(path.getFileName().toString());
        info.setFileSize(Files.size(path));
        info.setFileExtension(FilenameUtils.getExtension(info.getFileName()));
        info.setLastModified(Files.getLastModifiedTime(path).toInstant());
        
        return info;
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            boolean deleted = Files.deleteIfExists(path);
            if (deleted) {
                log.info("文件删除成功: {}", filePath);
            } else {
                log.warn("文件不存在，无需删除: {}", filePath);
            }
            return deleted;
        } catch (IOException e) {
            log.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 清理临时文件
     */
    public void cleanupTempFiles(int daysOld) {
        try {
            Path tempPath = Paths.get(tempDir);
            if (!Files.exists(tempPath)) {
                return;
            }
            
            long cutoffTime = System.currentTimeMillis() - (daysOld * 24L * 60L * 60L * 1000L);
            
            Files.walk(tempPath)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    try {
                        return Files.getLastModifiedTime(path).toMillis() < cutoffTime;
                    } catch (IOException e) {
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        log.debug("清理临时文件: {}", path);
                    } catch (IOException e) {
                        log.warn("清理临时文件失败: {}", path, e);
                    }
                });
                
        } catch (IOException e) {
            log.error("清理临时文件时发生错误", e);
        }
    }

    /**
     * 创建上传目录
     */
    private Path createUploadDirectory(String clientId) throws IOException {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        Path uploadPath = Paths.get(uploadDir, clientId, dateStr);
        
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
            log.debug("创建上传目录: {}", uploadPath);
        }
        
        return uploadPath;
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFileName, String extension) {
        String baseName = FilenameUtils.getBaseName(originalFileName);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        return String.format("%s_%s_%s.%s", baseName, timestamp, uuid, extension);
    }

    /**
     * PDF文档信息
     */
    public static class PdfDocumentInfo {
        private String filePath;
        private int pageCount;
        private String title;
        private String author;
        private String subject;
        private java.util.Calendar creationDate;

        // Getters and Setters
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        public int getPageCount() { return pageCount; }
        public void setPageCount(int pageCount) { this.pageCount = pageCount; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getAuthor() { return author; }
        public void setAuthor(String author) { this.author = author; }
        public String getSubject() { return subject; }
        public void setSubject(String subject) { this.subject = subject; }
        public java.util.Calendar getCreationDate() { return creationDate; }
        public void setCreationDate(java.util.Calendar creationDate) { this.creationDate = creationDate; }
    }

    /**
     * 文件信息
     */
    public static class FileInfo {
        private String filePath;
        private String fileName;
        private long fileSize;
        private String fileExtension;
        private java.time.Instant lastModified;

        // Getters and Setters
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public String getFileExtension() { return fileExtension; }
        public void setFileExtension(String fileExtension) { this.fileExtension = fileExtension; }
        public java.time.Instant getLastModified() { return lastModified; }
        public void setLastModified(java.time.Instant lastModified) { this.lastModified = lastModified; }
    }
}
