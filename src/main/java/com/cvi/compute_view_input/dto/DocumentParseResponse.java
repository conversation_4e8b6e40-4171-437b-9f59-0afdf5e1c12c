package com.cvi.compute_view_input.dto;

import com.cvi.compute_view_input.entity.DocumentParseRecord;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文档解析响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DocumentParseResponse {

    /**
     * 解析记录ID
     */
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 单证类型
     */
    private DocumentParseRecord.DocumentType documentType;

    /**
     * 解析状态
     */
    private DocumentParseRecord.ParseStatus parseStatus;

    /**
     * 解析后的结构化数据
     */
    private Map<String, Object> parsedData;

    /**
     * 原始提取内容（可选）
     */
    private String rawContent;

    /**
     * 解析置信度分数
     */
    private BigDecimal confidenceScore;

    /**
     * 处理耗时（毫秒）
     */
    private Integer processingTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 字段匹配建议
     */
    private FieldMatchingSuggestion fieldMatchingSuggestion;

    /**
     * 从实体转换为DTO
     */
    public static DocumentParseResponse fromEntity(DocumentParseRecord entity) {
        DocumentParseResponse response = new DocumentParseResponse();
        response.setId(entity.getId());
        response.setFileName(entity.getFileName());
        response.setFileType(entity.getFileType());
        response.setFileSize(entity.getFileSize());
        response.setDocumentType(entity.getDocumentType());
        response.setParseStatus(entity.getParseStatus());
        response.setParsedData(entity.getParsedData());
        response.setConfidenceScore(entity.getConfidenceScore());
        response.setProcessingTime(entity.getProcessingTime());
        response.setErrorMessage(entity.getErrorMessage());
        response.setCreatedTime(entity.getCreatedTime());
        response.setUpdatedTime(entity.getUpdatedTime());
        return response;
    }

    /**
     * 从实体转换为DTO（包含原始内容）
     */
    public static DocumentParseResponse fromEntityWithRawContent(DocumentParseRecord entity) {
        DocumentParseResponse response = fromEntity(entity);
        response.setRawContent(entity.getRawContent());
        return response;
    }

    /**
     * 字段匹配建议
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FieldMatchingSuggestion {
        
        /**
         * 检测到的表单字段数量
         */
        private Integer detectedFormFields;

        /**
         * 可匹配的字段数量
         */
        private Integer matchableFields;

        /**
         * 匹配建议列表
         */
        private java.util.List<FieldMatch> fieldMatches;

        /**
         * 单个字段匹配
         */
        @Data
        public static class FieldMatch {
            /**
             * 数据字段名
             */
            private String dataField;

            /**
             * 数据值
             */
            private Object dataValue;

            /**
             * 建议的表单字段选择器
             */
            private String suggestedSelector;

            /**
             * 字段描述
             */
            private String fieldDescription;

            /**
             * 匹配置信度
             */
            private BigDecimal confidence;

            /**
             * 匹配类型
             */
            private String matchType;
        }
    }

    /**
     * 检查是否解析成功
     */
    public boolean isSuccess() {
        return DocumentParseRecord.ParseStatus.SUCCESS.equals(parseStatus);
    }

    /**
     * 检查是否解析失败
     */
    public boolean isFailed() {
        return DocumentParseRecord.ParseStatus.FAILED.equals(parseStatus);
    }

    /**
     * 检查是否正在处理
     */
    public boolean isProcessing() {
        return DocumentParseRecord.ParseStatus.PROCESSING.equals(parseStatus);
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return null;
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
}
