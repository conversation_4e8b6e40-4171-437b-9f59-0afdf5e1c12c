package com.cvi.compute_view_input.dto;

import com.cvi.compute_view_input.entity.DocumentParseRecord;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文档解析请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Data
public class DocumentParseRequest {

    /**
     * 上传的文件
     */
    @NotNull(message = "文件不能为空")
    private MultipartFile file;

    /**
     * 单证类型
     */
    private DocumentParseRecord.DocumentType documentType = DocumentParseRecord.DocumentType.AUTO_DETECT;

    /**
     * 客户端ID
     */
    @NotBlank(message = "客户端ID不能为空")
    private String clientId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 自定义解析需求（当documentType为AUTO_DETECT时使用）
     */
    private String customRequirement;

    /**
     * 是否启用OCR（对于图片文件）
     */
    private Boolean enableOcr = true;

    /**
     * OCR语言设置
     */
    private String ocrLanguage = "chi_sim+eng";

    /**
     * 解析超时时间（秒）
     */
    private Integer timeoutSeconds = 30;

    /**
     * 是否返回原始内容
     */
    private Boolean includeRawContent = false;

    /**
     * 验证文件
     */
    public void validateFile() {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 检查文件类型
        String fileExtension = getFileExtension(fileName).toLowerCase();
        if (!isValidFileType(fileExtension)) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
        }

        // 检查文件大小（20MB限制）
        if (file.getSize() > 20 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过20MB");
        }
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    /**
     * 检查是否为有效的文件类型
     */
    private boolean isValidFileType(String extension) {
        return extension.equals("pdf") || 
               extension.equals("jpg") || 
               extension.equals("jpeg") || 
               extension.equals("png");
    }

    /**
     * 获取文件类型
     */
    public String getFileType() {
        if (file == null || file.getOriginalFilename() == null) {
            return null;
        }
        return getFileExtension(file.getOriginalFilename()).toUpperCase();
    }

    /**
     * 是否为图片文件
     */
    public boolean isImageFile() {
        String fileType = getFileType();
        return "JPG".equals(fileType) || "JPEG".equals(fileType) || "PNG".equals(fileType);
    }

    /**
     * 是否为PDF文件
     */
    public boolean isPdfFile() {
        return "PDF".equals(getFileType());
    }
}
