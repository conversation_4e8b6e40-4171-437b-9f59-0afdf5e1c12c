package com.cvi.compute_view_input.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * 缓存配置类
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Value("${app.plugin.cache-ttl:3600}")
    private int cacheTtlSeconds;

    /**
     * 配置Caffeine缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofSeconds(cacheTtlSeconds))
                .recordStats());
        
        return cacheManager;
    }
}
