package com.cvi.compute_view_input.controller;

import com.cvi.compute_view_input.dto.DocumentParseRequest;
import com.cvi.compute_view_input.dto.DocumentParseResponse;
import com.cvi.compute_view_input.entity.DocumentParseRecord;
import com.cvi.compute_view_input.service.DocumentParseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档解析API控制器
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/document")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class DocumentParseController {

    private final DocumentParseService documentParseService;

    /**
     * 解析文档
     */
    @PostMapping("/parse")
    public ResponseEntity<ApiResponse<DocumentParseResponse>> parseDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "documentType", defaultValue = "AUTO_DETECT") String documentType,
            @RequestParam("clientId") String clientId,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "customRequirement", required = false) String customRequirement,
            @RequestParam(value = "enableOcr", defaultValue = "true") Boolean enableOcr,
            @RequestParam(value = "ocrLanguage", defaultValue = "chi_sim+eng") String ocrLanguage,
            @RequestParam(value = "timeoutSeconds", defaultValue = "30") Integer timeoutSeconds,
            @RequestParam(value = "includeRawContent", defaultValue = "false") Boolean includeRawContent) {
        
        try {
            log.info("接收文档解析请求: 文件={}, 客户端={}, 用户={}", 
                    file.getOriginalFilename(), clientId, userId);
            
            // 构建请求对象
            DocumentParseRequest request = new DocumentParseRequest();
            request.setFile(file);
            request.setDocumentType(DocumentParseRecord.DocumentType.valueOf(documentType));
            request.setClientId(clientId);
            request.setUserId(userId);
            request.setCustomRequirement(customRequirement);
            request.setEnableOcr(enableOcr);
            request.setOcrLanguage(ocrLanguage);
            request.setTimeoutSeconds(timeoutSeconds);
            request.setIncludeRawContent(includeRawContent);
            
            // 执行解析
            DocumentParseResponse response = documentParseService.parseDocument(request);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (IllegalArgumentException e) {
            log.warn("文档解析请求参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("请求参数错误: " + e.getMessage()));
                
        } catch (Exception e) {
            log.error("文档解析失败", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("文档解析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取解析记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<DocumentParseResponse>> getParseRecord(
            @PathVariable Long id,
            @RequestParam(value = "includeRawContent", defaultValue = "false") Boolean includeRawContent) {
        
        try {
            DocumentParseResponse response = includeRawContent ? 
                documentParseService.getParseRecordWithRawContent(id) :
                documentParseService.getParseRecord(id);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            log.error("获取解析记录失败: ID={}", id, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取解析记录失败: " + e.getMessage()));
        }
    }

    /**
     * 重新解析文档
     */
    @PostMapping("/{id}/reparse")
    public ResponseEntity<ApiResponse<DocumentParseResponse>> reparseDocument(
            @PathVariable Long id,
            @RequestParam(value = "customRequirement", required = false) String customRequirement) {
        
        try {
            DocumentParseResponse response = documentParseService.reparseDocument(id, customRequirement);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("重新解析失败: " + e.getMessage()));
                
        } catch (Exception e) {
            log.error("重新解析失败: ID={}", id, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("重新解析失败: " + e.getMessage()));
        }
    }

    /**
     * 删除解析记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteParseRecord(@PathVariable Long id) {
        try {
            documentParseService.deleteParseRecord(id);
            
            return ResponseEntity.ok(ApiResponse.success(null, "删除成功"));
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            log.error("删除解析记录失败: ID={}", id, e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("删除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取支持的文档类型
     */
    @GetMapping("/types")
    public ResponseEntity<ApiResponse<Map<String, String>>> getSupportedDocumentTypes() {
        Map<String, String> types = new HashMap<>();
        
        for (DocumentParseRecord.DocumentType type : DocumentParseRecord.DocumentType.values()) {
            types.put(type.name(), type.getDescription());
        }
        
        return ResponseEntity.ok(ApiResponse.success(types));
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "document-parse-service");
        
        return ResponseEntity.ok(ApiResponse.success(health));
    }

    /**
     * 统一API响应格式
     */
    public static class ApiResponse<T> {
        private boolean success;
        private String message;
        private T data;
        private long timestamp;

        public ApiResponse() {
            this.timestamp = System.currentTimeMillis();
        }

        public static <T> ApiResponse<T> success(T data) {
            return success(data, "操作成功");
        }

        public static <T> ApiResponse<T> success(T data, String message) {
            ApiResponse<T> response = new ApiResponse<>();
            response.success = true;
            response.message = message;
            response.data = data;
            return response;
        }

        public static <T> ApiResponse<T> error(String message) {
            ApiResponse<T> response = new ApiResponse<>();
            response.success = false;
            response.message = message;
            return response;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
}
