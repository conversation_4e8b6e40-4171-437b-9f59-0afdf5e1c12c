package com.cvi.compute_view_input.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 插件配置实体
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "plugin_config",
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_client_id", columnNames = "clientId"),
           @UniqueConstraint(name = "uk_api_key", columnNames = "apiKey")
       },
       indexes = {
           @Index(name = "idx_domain", columnList = "domain"),
           @Index(name = "idx_is_active", columnList = "isActive"),
           @Index(name = "idx_expires_at", columnList = "expiresAt")
       })
public class PluginConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 客户端ID
     */
    @Column(name = "client_id", nullable = false, length = 100)
    private String clientId;

    /**
     * 客户端名称
     */
    @Column(name = "client_name", length = 255)
    private String clientName;

    /**
     * 授权域名
     */
    @Column(name = "domain", nullable = false, length = 255)
    private String domain;

    /**
     * API密钥
     */
    @Column(name = "api_key", nullable = false, length = 255)
    private String apiKey;

    /**
     * 配置数据
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "config_data", nullable = false, columnDefinition = "JSON")
    private Map<String, Object> configData;

    /**
     * 每日最大请求数
     */
    @Column(name = "max_daily_requests")
    private Integer maxDailyRequests = 1000;

    /**
     * 是否启用
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 过期时间
     */
    @Column(name = "expires_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdTime = now;
        this.updatedTime = now;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedTime = LocalDateTime.now();
    }

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查域名是否匹配
     */
    public boolean isDomainMatched(String requestDomain) {
        if (domain == null || requestDomain == null) {
            return false;
        }
        
        // 支持通配符匹配
        if ("*".equals(domain)) {
            return true;
        }
        
        // 精确匹配
        if (domain.equals(requestDomain)) {
            return true;
        }
        
        // 子域名匹配
        if (domain.startsWith("*.")) {
            String baseDomain = domain.substring(2);
            return requestDomain.endsWith("." + baseDomain) || requestDomain.equals(baseDomain);
        }
        
        return false;
    }
}
