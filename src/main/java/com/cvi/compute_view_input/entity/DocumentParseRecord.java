package com.cvi.compute_view_input.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文档解析记录实体
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "document_parse_record", indexes = {
    @Index(name = "idx_client_user", columnList = "clientId,userId"),
    @Index(name = "idx_document_type", columnList = "documentType"),
    @Index(name = "idx_parse_status", columnList = "parseStatus"),
    @Index(name = "idx_created_time", columnList = "createdTime")
})
public class DocumentParseRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 文件名
     */
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    /**
     * 文件类型(PDF/JPG/PNG)
     */
    @Column(name = "file_type", nullable = false, length = 50)
    private String fileType;

    /**
     * 文件大小(字节)
     */
    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    /**
     * 文件存储路径
     */
    @Column(name = "file_path", length = 500)
    private String filePath;

    /**
     * 单证类型
     */
    @Column(name = "document_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private DocumentType documentType;

    /**
     * 解析状态
     */
    @Column(name = "parse_status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private ParseStatus parseStatus = ParseStatus.PENDING;

    /**
     * 原始提取内容
     */
    @Column(name = "raw_content", columnDefinition = "TEXT")
    private String rawContent;

    /**
     * 解析后的结构化数据
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "parsed_data", columnDefinition = "JSON")
    private Map<String, Object> parsedData;

    /**
     * 解析置信度分数
     */
    @Column(name = "confidence_score", precision = 5, scale = 2)
    private BigDecimal confidenceScore;

    /**
     * 处理耗时(毫秒)
     */
    @Column(name = "processing_time")
    private Integer processingTime;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 客户端ID
     */
    @Column(name = "client_id", length = 100)
    private String clientId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 100)
    private String userId;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdTime = now;
        this.updatedTime = now;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedTime = LocalDateTime.now();
    }

    /**
     * 单证类型枚举
     */
    public enum DocumentType {
        BILL_OF_LADING("海运提单"),
        COMMERCIAL_INVOICE("商业发票"),
        PACKING_LIST("装箱单"),
        CUSTOMS_DECLARATION("报关单"),
        FREIGHT_INVOICE("货代费用单"),
        CERTIFICATE_OF_ORIGIN("原产地证"),
        AUTO_DETECT("自动识别");

        private final String description;

        DocumentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 解析状态枚举
     */
    public enum ParseStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败");

        private final String description;

        ParseStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
