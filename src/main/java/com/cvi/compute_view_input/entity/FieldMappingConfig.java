package com.cvi.compute_view_input.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 字段映射配置实体
 * 
 * <AUTHOR> Assistant
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "field_mapping_config", 
       uniqueConstraints = @UniqueConstraint(name = "uk_doc_field", columnNames = {"documentType", "fieldName"}),
       indexes = {
           @Index(name = "idx_document_type", columnList = "documentType"),
           @Index(name = "idx_is_active", columnList = "isActive")
       })
public class FieldMappingConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 单证类型
     */
    @Column(name = "document_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private DocumentParseRecord.DocumentType documentType;

    /**
     * 字段名称
     */
    @Column(name = "field_name", nullable = false, length = 100)
    private String fieldName;

    /**
     * 字段别名列表
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "field_aliases", columnDefinition = "JSON")
    private List<String> fieldAliases;

    /**
     * 字段类型
     */
    @Column(name = "field_type", length = 50)
    @Enumerated(EnumType.STRING)
    private FieldType fieldType;

    /**
     * 验证规则
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "validation_rules", columnDefinition = "JSON")
    private Map<String, Object> validationRules;

    /**
     * 是否必填
     */
    @Column(name = "is_required")
    private Boolean isRequired = false;

    /**
     * 默认值
     */
    @Column(name = "default_value", length = 255)
    private String defaultValue;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否启用
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdTime = now;
        this.updatedTime = now;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedTime = LocalDateTime.now();
    }

    /**
     * 字段类型枚举
     */
    public enum FieldType {
        TEXT("文本"),
        NUMBER("数字"),
        DATE("日期"),
        EMAIL("邮箱"),
        PHONE("电话"),
        URL("网址"),
        TEXTAREA("多行文本"),
        SELECT("下拉选择"),
        CHECKBOX("复选框"),
        RADIO("单选框");

        private final String description;

        FieldType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
